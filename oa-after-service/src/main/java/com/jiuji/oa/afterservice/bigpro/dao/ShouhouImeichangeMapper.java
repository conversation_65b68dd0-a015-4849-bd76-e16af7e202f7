package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouImeichange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2020-05-28 18:58:44
 */
@Mapper
public interface ShouhouImeichangeMapper extends BaseMapper<ShouhouImeichange> {

    /**
     * 获取售后转出的imei的修改记录
     * @param imei
     * @return
     */
    ShouhouImeichange getTransferOutImeiChange(@Param("imei") String imei);
}
