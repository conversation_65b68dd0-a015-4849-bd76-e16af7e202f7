package com.jiuji.oa.oacore.thirdplatform.doudian.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物流公司枚举
 * Enum for Logistics Companies
 *
 * <AUTHOR>
 * @date 2023/11/28
 * @since 1.0.0
 */
@AllArgsConstructor
@Getter
public enum WuLiuCompanyEnum implements CodeMessageEnumInterface {

    SF_EXPRESS("shunfeng", "顺丰快递", "顺丰快递"),
    ZTO_EXPRESS("zhongtong", "中通", "中通"),
    MEITUAN("meituan", "美团", "美团"),
    EMS("ems", "EMS", "EMS"),
    YTO_EXPRESS("yuantong", "圆通快递", "圆通快递"),
    PASSENGER_TRANSPORTATION("keyun", "客运", "客运"),
    ALL_IN_ONE_EXPRESS("quanyi", "全一快递", "全一快递"),
    STO_EXPRESS("shentong", "申通快递", "申通快递"),
    OTHER("other", "其它", "其它");

    private final String code;
    private final String subCode;
    private final String message;
}
