package com.jiuji.oa.oacore.subRecommended.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.product.service.PriceCloud;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.service.CategoryService;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.subRecommended.entity.*;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigSelectTypeEnum;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigStateEnum;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigTimeTypeEnum;
import com.jiuji.oa.oacore.subRecommended.enums.PersonnelTypeEnum;
import com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedMainConfigMapper;
import com.jiuji.oa.oacore.subRecommended.service.*;
import com.jiuji.oa.oacore.subRecommended.utils.SubRecommendedUtils;
import com.jiuji.oa.oacore.subRecommended.vo.bo.InitializationDataBo;
import com.jiuji.oa.oacore.subRecommended.vo.bo.RecommendedBasketInfo;
import com.jiuji.oa.oacore.subRecommended.vo.bo.ServiceInfo;
import com.jiuji.oa.oacore.subRecommended.vo.bo.ViewProductStBo;
import com.jiuji.oa.oacore.subRecommended.vo.req.*;
import com.jiuji.oa.oacore.subRecommended.vo.res.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.req.RecommendedSelectAppReq;
import com.jiuji.oa.oacore.weborder.res.recommendedRes.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class SubRecommendedMainConfigServiceImpl extends ServiceImpl<SubRecommendedMainConfigMapper, SubRecommendedMainConfig> implements SubRecommendedMainConfigService {


    @Resource
    private AbstractCurrentRequestComponent component;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private SubRecommendedMainAreaConfigService areaConfigService;
    @Resource
    private SubRecommendedMainProductConfigService mainProductService;
    @Resource
    private SubRecommendedPackageConfigService packageConfigService;
    @Resource
    private SubRecommendedCategoryConfigService categoryConfigService;
    @Resource
    private SubRecommendedProductConfigService productConfigService;
    @Resource
    private SubRecommendedMainConfigLogService logService;
    @Resource
    private ProductinfoService productInfoService;
    @Resource
    private SubRecommendedProductServiceConfigService serviceConfigService;
    @Resource
    private CategoryService categoryService;


    private static final Integer del = 0;
    private static final Integer saveOrUpdate = 1;

    private static final String rank = "tcgl";
    @Resource
    private PriceCloud priceCloud;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> initializationData() {
        List<InitializationDataBo> initializationDataBos = this.baseMapper.selectInitializationData();
        StringJoiner joiner = new StringJoiner(",");
        if(CollectionUtil.isNotEmpty(initializationDataBos)){
            List<InitializationDataBo> collect = initializationDataBos.stream().filter(item -> ObjectUtil.isNull(item.getConfigCreateAreaId()) || ObjectUtil.isNull(item.getConfigXtenant())).collect(Collectors.toList());
            collect.forEach(item->{
                boolean update = this.lambdaUpdate().eq(SubRecommendedMainConfig::getId, item.getId())
                        .eq(SubRecommendedMainConfig::getCreateUser, item.getCreateUser())
                        .set(ObjectUtil.isNull(item.getConfigCreateAreaId()),SubRecommendedMainConfig::getCreateAreaId, item.getArea1id())
                        .set(ObjectUtil.isNull(item.getConfigXtenant()),SubRecommendedMainConfig::getXtenant, item.getXtenant())
                        .update();
                if(update){
                    joiner.add(item.getId().toString());
                } else {
                    throw new CustomizeException("更新失败 配置id："+item.getId());
                }
            });
        }
        return R.success(joiner.toString());
    }
    @Override
    public void copyInfo(CopyInfoReq copyInfoReq) {
        OaUserBO userBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new ClassCastException("当前用户信息为空"));
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(!rankList.contains(rank)){
            throw new CustomizeException("没有权限");
        }
        RecommendedByIdReq req = new RecommendedByIdReq();
        req.setId(copyInfoReq.getMainConfigId());
        RecommendedInfo recommend = getRecommendedById(req);
        //复制数据处理(所有id设置为null 默认为隐藏)
        RecommendedInfoMainInfo mainInfo = recommend.getMainInfo();
        mainInfo.setIsEnabled(NumberConstant.ZERO);
        mainInfo.setId(null);
        //地区设置为当前默认地区
        recommend.setApplyAreaIdList(Collections.singletonList(userBO.getAreaId()));
        //商品信息处理 所有id设置为null
        recommend.getProductList().forEach(item->{
            item.setId(null);
            item.setFkConfigId(null);
        });
        //套餐信息处理 所有id设为null
        List<RecommendedInfoPackageInfo> packageList = recommend.getPackageList();
        if(CollectionUtil.isNotEmpty(packageList)){
            packageList.forEach(item->{
                item.setId(null);
                item.setCreateTime(null);
                item.setFkConfigId(null);
                List<RecommendedInfoCategoryInfo> categoryInfoList = item.getCategoryInfoList();
                if(CollectionUtil.isNotEmpty(categoryInfoList)){
                    categoryInfoList.forEach(categoryInfo -> {
                        categoryInfo.setId(null);
                        categoryInfo.setCreateTime(null);
                        categoryInfo.setFkPackageId(null);
                        List<RecommendedInfoProductInfo> productInfoList = categoryInfo.getProductInfoList();
                        if(CollectionUtil.isNotEmpty(productInfoList)){
                            productInfoList.forEach(productInfo -> {
                                productInfo.setId(null);
                                productInfo.setFkCategoryId(null);
                                productInfo.setCreateTime(null);
                                productInfo.setFkPackageId(null);
                                List<RecommendedInfoProductServiceInfo> serviceInfoList = productInfo.getServiceInfoList();
                                if(CollectionUtil.isNotEmpty(serviceInfoList)){
                                    serviceInfoList.forEach(serviceInfo -> {
                                        serviceInfo.setId(null);
                                        serviceInfo.setFkProductId(null);
                                        serviceInfo.setCreateTime(null);
                                    });
                                }
                            });
                        }

                    });
                }
            });
        }

        //数据保存
        saveOrUpdate(recommend);
    }

    @Override
    public R<Page<RecommendedPageRes>> recommendedPage(RecommendedPageReq req) {
        Page<RecommendedPageRes>  page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("main.id");
        String selectKey = req.getSelectKey();
        if(StringUtil.isNotBlank(selectKey)){
            req.setSelectKey(selectKey.trim());
        }
        if(MainConfigSelectTypeEnum.product_id.getCode().equals(req.getSelectType()) && StringUtil.isNotBlank(selectKey)){
            if(!StringUtils.isNumeric(selectKey)){
               throw new CustomizeException(MainConfigSelectTypeEnum.product_id.getMessage()+"必须为数字");
            }
            if(selectKey.length()>NumberConstant.NINE){
                throw new CustomizeException(MainConfigSelectTypeEnum.product_id.getMessage()+"长度不能超过9");
            }
        }
        //设置默认时间类型
        if(ObjectUtil.isNull(req.getTimeType())){
            req.setTimeType(MainConfigTimeTypeEnum.EFFECTIVE_TIME.getCode());
        }
        //交易时间 处理逻辑
        if(MainConfigTimeTypeEnum.TRADING_HOURS.getCode().equals(req.getTimeType())){
            //设置默认时间
            if(ObjectUtil.isNull(req.getStartTime()) && ObjectUtil.isNull(req.getEndTime())){
                req.setTimeType(null);
            }
        }
        if(ObjectUtil.isNull(req.getEndTime())){
            req.setEndTime(SubRecommendedMainConfig.getDefaultEndTime());
        }
        //开始时间设置
        if(ObjectUtil.isNull(req.getStartTime())){
            req.setStartTime(SubRecommendedMainConfig.getDefaultStartTime());
        }

        Page<RecommendedPageRes> recommendedPageBoIPage = this.baseMapper.pageInfo(page, req);
        List<RecommendedPageRes> records = recommendedPageBoIPage.getRecords();
        if(CollectionUtil.isNotEmpty(records)){
            for (RecommendedPageRes recommendedPageRes : records) {
                LocalDateTime endTime = recommendedPageRes.getEndTime();
                LocalDateTime startTime = recommendedPageRes.getStartTime();
                //设置状态
                recommendedPageRes.setStateVale(MainConfigStateEnum.getMessageByTime(startTime, endTime));
                recommendedPageRes.setEffectiveTime(SubRecommendedMainConfig.createEffectiveTime(startTime,endTime));
                if(StringUtil.isEmpty(recommendedPageRes.getApplyArea())){
                    recommendedPageRes.setApplyArea("全区");
                }
            }
        }
        return R.success(recommendedPageBoIPage);
    }

    @Override
    public OaBySubIdRes recommendedSelectOaBySubId(OaBySubIdReq req) {
        OaBySubIdRes oaBySubIdRes = new OaBySubIdRes();
        //订单相关信息查询
        List<RecommendedBasketInfo> recommendedBasketInfos = this.baseMapper.selectBasketInfo(req.getSubId());
        if(CollectionUtil.isEmpty(recommendedBasketInfos)){
            return oaBySubIdRes;
        }
        List<Integer> basketIdList = new ArrayList<>();
        for (RecommendedBasketInfo recommendedBasketInfo : recommendedBasketInfos) {
            //找出符合条件的ppid
            List<SubRecommendedMainConfig> subRecommendedMainConfigList = this.baseMapper.selectRecommendedSelectOa(recommendedBasketInfo.getPpriceid(),recommendedBasketInfo.getXtenant());
            if(CollectionUtil.isNotEmpty(subRecommendedMainConfigList)){
                //找出符合条件的ppid
                RecommendedByIdReq recommended = createGetRecommendedByIdReq(recommendedBasketInfo.getAreaId(), subRecommendedMainConfigList);
                if(ObjectUtil.isNotNull(recommended.getId())){
                    basketIdList.add(recommendedBasketInfo.getBasketId());
                }
            }
        }
        oaBySubIdRes.setBasketIdList(basketIdList);
        return oaBySubIdRes;
    }

    @Override
    public R<RecommendedSelectOaRes> recommendedSelectOa(RecommendedSelectOaReq req) {
        RecommendedSelectOaRes recommendedSelectOaRes = new RecommendedSelectOaRes();
        Integer fkConfigId = req.getFkConfigId();
        RecommendedInfo recommendedById = new RecommendedInfo();
        if(ObjectUtil.isNotNull(fkConfigId) && fkConfigId!=0){
            RecommendedByIdReq recommendedByIdReq = new RecommendedByIdReq();
            recommendedByIdReq.setId(fkConfigId);
            recommendedById = Optional.ofNullable(getRecommendedById(recommendedByIdReq)).orElse(new RecommendedInfo());
        } else {
            Integer ppid = Optional.ofNullable(req.getPpid()).orElseThrow(()->new CustomizeException("ppid不能为空")) ;
            Integer areaId = Optional.ofNullable(req.getAreaId()).orElseThrow(()->new CustomizeException("areaId不能为空")) ;
            AreaInfo areaInfo = Optional.ofNullable(areaInfoService.getById(areaId)).orElse(new AreaInfo());
            //找出符合条件的ppid
            List<SubRecommendedMainConfig> subRecommendedMainConfigList = this.baseMapper.selectRecommendedSelectOa(ppid,areaInfo.getXtenant());
            if(CollectionUtil.isEmpty(subRecommendedMainConfigList)){
                return R.success(recommendedSelectOaRes);
            }
            //找出符合条件的areaId
            RecommendedByIdReq recommendedByIdReq = createGetRecommendedByIdReq(areaId, subRecommendedMainConfigList);
            recommendedById = Optional.ofNullable(getRecommendedById(recommendedByIdReq)).orElse(new RecommendedInfo());
        }
        String errorMessage = recommendedById.getErrorMessage();
        //判断如果有错误信息那就抛出异常
        if(StringUtil.isNotEmpty(errorMessage)){
            throw new CustomizeException(errorMessage);
        }
        List<RecommendedInfoPackageInfo> packageList = recommendedById.getPackageList();
        if(CollectionUtil.isEmpty(packageList)){
            return R.success(recommendedSelectOaRes);
        }
        //移除分类内没有商品的分类
        packageList.forEach(packageInfo ->{
            List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
            if(CollectionUtil.isNotEmpty(categoryInfoList)){
                categoryInfoList.removeIf(categoryInfo -> CollectionUtil.isEmpty(categoryInfo.getProductInfoList()));
            }
        });
        //移除套餐内没有分类的套餐
        packageList.removeIf(item->CollectionUtil.isEmpty(item.getCategoryInfoList()));
        if(CollectionUtil.isEmpty(packageList)){
            return R.success(recommendedSelectOaRes);
        }
        //获取oa 和 app 所需要数据 （避免新增逻辑影响老接口 所以暂时不抛出异常）
        try {
            createProductAggregationList(packageList);
        } catch (Exception e) {
            RRExceptionHandler.logError("套餐获取oa 和 app 所需要数据异常",req,e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        recommendedSelectOaRes.setPackageList(packageList);
        return R.success(recommendedSelectOaRes);
    }


    /**
     * 获取oa 和 app 所需要数据
     * @param packageList
     */
    private void createProductAggregationList(List<RecommendedInfoPackageInfo> packageList){
        //获取商品所有ppid
        List<Integer> ppidList = packageList.stream()
                .filter(item -> CollectionUtil.isNotEmpty(item.getCategoryInfoList()))
                .flatMap(item -> item.getCategoryInfoList().stream()).filter(Objects::nonNull)
                .filter(item -> CollectionUtil.isNotEmpty(item.getProductInfoList()))
                .flatMap(item -> item.getProductInfoList().stream()).filter(Objects::nonNull)
                .map(RecommendedInfoProductInfo::getPpid).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        if(CollectionUtil.isEmpty(ppidList)){
            return ;
        }
        List<ViewProductStBo> viewProductStBos = CommonUtils.bigDataInQuery(ppidList, ids->this.baseMapper.selectViewProductStBoByPpid(ids));
        //viewProductStBos 根据 ppriceid 收集成为map
        Map<Integer, List<ViewProductStBo>> viewProductStBoMap = viewProductStBos.stream().collect(Collectors.groupingBy(ViewProductStBo::getPpriceid));
        for (RecommendedInfoPackageInfo packageInfo : packageList) {
            List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
            if(CollectionUtil.isEmpty(categoryInfoList)){
                continue;
            }
            for (RecommendedInfoCategoryInfo categoryInfo : categoryInfoList) {
                List<RecommendedInfoProductInfo> productInfoList = categoryInfo.getProductInfoList();
                if(CollectionUtil.isEmpty(productInfoList)){
                    continue;
                }
                //进行productAggregationList 数据的封装 productInfoList 根据productId进行聚合
                Map<Integer, List<RecommendedInfoProductInfo>> productPolymerizationMap = productInfoList.stream()
                        .filter(item->ObjectUtil.isNotNull(item.getProductId()))
                        .collect(Collectors.groupingBy(RecommendedInfoProductInfo::getProductId));
                List<ProductAggregation> productAggregationList = new ArrayList<>();
                productPolymerizationMap.forEach((k,v)->{
                    ProductAggregation productAggregation = new ProductAggregation();
                    RecommendedInfoProductInfo recommendedInfoProductInfo = Optional.ofNullable(v.get(NumberConstant.ZERO)).orElse(new RecommendedInfoProductInfo());
                    List<RecommendedInfoProductInfo> productInfoChildrenList = createProductInfoChildrenList(viewProductStBoMap, v);
                    //处理排序所需要的数据
                    int size = productInfoChildrenList.stream().filter(item -> ObjectUtil.isNotNull(item.getPpid())).collect(Collectors.toList()).size();
                    Integer sum = productInfoChildrenList.stream().map(RecommendedInfoProductInfo::getSeqNum).filter(Objects::nonNull).reduce(Integer::sum).orElse(Integer.MAX_VALUE);
                    productAggregation.setProductId(k)
                            .setProductName(recommendedInfoProductInfo.getProductName())
                            //.setSpecList(createSpecList(viewProductStBoMap,v))
                            .setProductInfoChildrenList(productInfoChildrenList);
                    if(NumberConstant.ZERO == size){
                        productAggregation.setRank(Integer.MAX_VALUE);
                    } else {
                        productAggregation.setRank(sum/size);
                    }
                    productAggregationList.add(productAggregation);
                });
                //productAggregationList根据rank进行排序（如果过rank为空的情况那就排在最后）
                productAggregationList.sort(Comparator.nullsLast(Comparator.comparing(ProductAggregation::getRank)));
                categoryInfo.setProductAggregationList(productAggregationList);
            }
       }

    }
    /**
     * 创建规格维度
     * @param viewProductStBoMap
     * @param productInfoList
     * @return
     */
    private List<RecommendedInfoProductInfo> createProductInfoChildrenList(Map<Integer, List<ViewProductStBo>> viewProductStBoMap,List<RecommendedInfoProductInfo> productInfoList) {
        productInfoList.forEach(item->{
            List<ViewProductStBo> viewProductStBos = viewProductStBoMap.get(item.getPpid());
            if(CollectionUtil.isNotEmpty(viewProductStBos)){
                item.setSpecifications(viewProductStBos.stream().map(ViewProductStBo::getValue).collect(Collectors.toList()));
            } else {
                item.setSpecifications(new ArrayList<>());
            }
        });
        return productInfoList;
    }

    /**
     * 创建规格维度
     * @param viewProductStBoMap
     * @param productInfoList
     * @return
     */
    private List<SpecListInfo> createSpecList(Map<Integer, List<ViewProductStBo>> viewProductStBoMap,List<RecommendedInfoProductInfo> productInfoList) {
        List<SpecListInfo> createSpecList = new ArrayList<>();
        Map<String, List<String>> map = new HashMap<>();
        for (RecommendedInfoProductInfo item : productInfoList) {
            List<ViewProductStBo> viewProductStBos = viewProductStBoMap.get(item.getPpid());
            if(CollectionUtil.isEmpty(viewProductStBos)){
                continue;
            }
            viewProductStBos.forEach(obj->{
                List<String> valueList = map.getOrDefault(obj.getName(), new ArrayList<>());
                if(!valueList.contains(obj.getValue())){
                    valueList.add(obj.getValue());
                }
                map.put(obj.getName(),valueList);
            });
        }
        //map转换成为createSpecList
        if(CollectionUtil.isNotEmpty(map)){
            map.forEach((k,v)->{
                SpecListInfo specListInfo = new SpecListInfo();
                specListInfo.setName(k).setDetailList(v);
                createSpecList.add(specListInfo);
            });
        }

        return createSpecList;
    }
    @Override
    public R<RecommendedSelectOaRes> recommendedSelectApp(RecommendedSelectAppReq req) {
        RecommendedSelectOaReq oaReq = new RecommendedSelectOaReq();
        BeanUtil.copyProperties(req,oaReq);
        return recommendedSelectOa(oaReq);
    }

    private RecommendedByIdReq createGetRecommendedByIdReq(Integer areaId, List<SubRecommendedMainConfig> subRecommendedMainConfigList) {
        List<Integer> idList = subRecommendedMainConfigList.stream().map(SubRecommendedMainConfig::getId).collect(Collectors.toList());
        Map<Integer, List<SubRecommendedMainAreaConfig>> areaMap = new HashMap<>();
        List<SubRecommendedMainAreaConfig> areaConfigsAll = areaConfigService.lambdaQuery().in(SubRecommendedMainAreaConfig::getFkConfigId, idList).list();
        if (CollectionUtil.isNotEmpty(areaConfigsAll)) {
            areaMap = areaConfigsAll.stream().collect(Collectors.groupingBy(SubRecommendedMainAreaConfig::getFkConfigId));
        }
        //找出符合条件的areaid
        RecommendedByIdReq recommendedByIdReq = new RecommendedByIdReq();
        List<Integer> comprehensiveStoreAreaIdList = this.baseMapper.selectComprehensiveStore(areaId);
        for (SubRecommendedMainConfig subRecommendedMainConfig : subRecommendedMainConfigList) {
            Integer mainConfigId = subRecommendedMainConfig.getId();
            List<SubRecommendedMainAreaConfig> areaConfigs = areaMap.get(mainConfigId);
            //判断是否配置areaid 如果没有没配置那就从全区’指的是所有九机综合门店 里面找
            if (CollectionUtil.isNotEmpty(areaConfigs)) {
                List<Integer> areaIdList = areaConfigs.stream().map(SubRecommendedMainAreaConfig::getAreaId).filter(Objects::nonNull).collect(Collectors.toList());
                if (areaIdList.contains(areaId)) {
                    recommendedByIdReq.setId(subRecommendedMainConfig.getId());
                    break;
                }
            } else {
                if (CollectionUtil.isNotEmpty(comprehensiveStoreAreaIdList)) {
                    recommendedByIdReq.setId(subRecommendedMainConfig.getId());
                    break;
                }
            }
        }
        return recommendedByIdReq;
    }

    /**
     * 判断是否为总部人员
     * @param userId
     * @return
     */
    private Boolean isHeadquartersPersonnel(Integer userId){
        if(ObjectUtil.isNull(userId)){
            return false;
        }
        return PersonnelTypeEnum.HQ_BACKEND.getCode().equals(this.baseMapper.selectPersonnelType(userId));
    }

    /**
     * 时间处理
     * @param recommendedInfoMainInfo
     */
    private void handleTime(SubRecommendedMainConfig subRecommendedMainConfig){
        LocalDateTime startTime = subRecommendedMainConfig.getStartTime();
        if(subRecommendedMainConfig.getDefaultStartTime().compareTo(startTime)==0){
            subRecommendedMainConfig.setStartTime(null);
        }
        LocalDateTime endTime =  subRecommendedMainConfig.getEndTime();
        if(subRecommendedMainConfig.getDefaultEndTime().compareTo(endTime)==0){
            subRecommendedMainConfig.setEndTime(null);
        }
    }
    /**
     * 该接口 前端和C#都在用
     * @param req
     * @return
     */
    @Override
    public RecommendedInfo getRecommendedById(RecommendedByIdReq req) {
        RecommendedInfo recommendedInfo = new RecommendedInfo();
        Integer mainConfigId = req.getId();
        //套餐主要信息查询
        RecommendedInfoMainInfo recommendedInfoMainInfo = new RecommendedInfoMainInfo();
        SubRecommendedMainConfig subRecommendedMainConfig = Optional.ofNullable(this.getById(mainConfigId)).orElseThrow(() -> new CustomizeException("套餐不存在"));
        handleTime(subRecommendedMainConfig);
        BeanUtils.copyProperties(subRecommendedMainConfig, recommendedInfoMainInfo);
        recommendedInfo.setMainInfo(recommendedInfoMainInfo);
        //套餐区域信息查询
        List<SubRecommendedMainAreaConfig> subRecommendedMainAreaConfigs = areaConfigService.lambdaQuery().eq(SubRecommendedMainAreaConfig::getFkConfigId, mainConfigId).list();
        if(CollectionUtil.isNotEmpty(subRecommendedMainAreaConfigs)){
            List<Integer> collect = subRecommendedMainAreaConfigs.stream().map(SubRecommendedMainAreaConfig::getAreaId).collect(Collectors.toList());
            recommendedInfo.setApplyAreaIdList(collect);
            //地区转成为名称
            recommendedInfo.setApplyAreaIdValueList(areaInfoService.getAreaMap(collect).values().stream().map(AreaInfo::getArea).collect(Collectors.toList()));
        }
        //套餐主商品信息查询
        List<SubRecommendedMainProductConfig> mainProductConfigList = mainProductService.lambdaQuery().eq(SubRecommendedMainProductConfig::getFkConfigId, mainConfigId).list();
        if(CollectionUtil.isNotEmpty(mainProductConfigList)){
            List<Integer> productIdList = mainProductConfigList.stream().map(SubRecommendedMainProductConfig::getProductId).collect(Collectors.toList());
            Map<Integer, List<ProductSimpleBO>> map = productInfoService.getProductInfoByProductId(productIdList);
            List<RecommendedInfoMainProductInfo> collect = mainProductConfigList.stream().map(item -> {
                RecommendedInfoMainProductInfo recommendedInfoMainProductInfo = new RecommendedInfoMainProductInfo();
                BeanUtils.copyProperties(item, recommendedInfoMainProductInfo);
                List<ProductSimpleBO> productinfoList = map.getOrDefault(item.getProductId(), new ArrayList<>());
                if (CollectionUtil.isNotEmpty(productinfoList)) {
                    recommendedInfoMainProductInfo.setProductName(productinfoList.get(0).getProductName());
                }
                return recommendedInfoMainProductInfo;
            }).collect(Collectors.toList());
            recommendedInfo.setProductList(collect);
        }
        //套餐套餐信息查询
        List<SubRecommendedPackageConfig> packageConfigList = packageConfigService.lambdaQuery().eq(SubRecommendedPackageConfig::getFkConfigId, mainConfigId).list();
        if(CollectionUtil.isNotEmpty(packageConfigList)){
            List<RecommendedInfoPackageInfo> packageList = new ArrayList<>();
            packageConfigList.forEach(item->{
                RecommendedInfoPackageInfo packageInfo = new RecommendedInfoPackageInfo();
                BeanUtils.copyProperties(item, packageInfo);
                packageList.add(packageInfo);
            });
            recommendedInfo.setPackageList(packageList);
        } else {
            return  recommendedInfo;
        }
        //套餐分类查询
        List<Integer> packageConfigIdList = packageConfigList.stream().map(SubRecommendedPackageConfig::getId).collect(Collectors.toList());
        List<SubRecommendedCategoryConfig> categoryConfigList = categoryConfigService.lambdaQuery().in(SubRecommendedCategoryConfig::getFkPackageId, packageConfigIdList).list();
        if(CollectionUtil.isNotEmpty(categoryConfigList)){
            Map<Integer, List<RecommendedInfoCategoryInfo>> categoryConfigMap = categoryConfigList.stream().map(item -> {
                RecommendedInfoCategoryInfo categoryInfo = new RecommendedInfoCategoryInfo();
                BeanUtils.copyProperties(item, categoryInfo);
                return categoryInfo;
            }).collect(Collectors.groupingBy(RecommendedInfoCategoryInfo::getFkPackageId));
            if(CollectionUtil.isNotEmpty(categoryConfigMap)){
                List<RecommendedInfoPackageInfo> packageList = recommendedInfo.getPackageList();
                packageList.forEach(item->{
                    List<RecommendedInfoCategoryInfo> categoryInfoList = categoryConfigMap.get(item.getId());
                    item.setCategoryInfoList(categoryInfoList);
                });
            }
        } else {
            return  recommendedInfo;
        }
        //套餐分类商品查询
        List<Integer> categoryConfigIdList = categoryConfigList.stream().map(SubRecommendedCategoryConfig::getId).collect(Collectors.toList());
        List<SubRecommendedProductConfig> productConfigList = productConfigService.lambdaQuery().in(SubRecommendedProductConfig::getFkCategoryId, categoryConfigIdList).list();
        List<RecommendedInfoProductInfo> productInfoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(productConfigList)){
            //获取所有的ppid
            List<Integer> ppidList = productConfigList.stream().map(SubRecommendedProductConfig::getPpid).collect(Collectors.toList());
            //根据ppid查询商品信息 价格库存
            Map<Integer, ProductBaseInfo> productBaseInfoMap = productInfoService.selectProductBaseInfoMap(ppidList,recommendedInfoMainInfo.getCreateAreaId());
            for (SubRecommendedProductConfig item : productConfigList){
                RecommendedInfoProductInfo productInfo = new RecommendedInfoProductInfo();
                BeanUtils.copyProperties(item, productInfo);
                ProductBaseInfo baseInfo = productBaseInfoMap.getOrDefault(item.getPpid(), new ProductBaseInfo());
                productInfo.setProductName(baseInfo.getProductName())
                        .setProductColor(baseInfo.getProductColor())
                        .setCostPrice(baseInfo.getCostPrice())
                        .setProductId(baseInfo.getProductId())
                        .setOriginalPrice(baseInfo.getOriginalPrice())
                        .setIsMobile(baseInfo.getIsMobile())
                        .setIsAnnualPackage(baseInfo.getIsAnnualPackage())
                        .setIsCaseOrFilm(baseInfo.getIsCaseOrFilm())
                        .setProductCount(baseInfo.getProductCount());
                productInfoList.add(productInfo);
            }
        }
        //设置分类商品
        if(CollectionUtil.isNotEmpty(productInfoList)){
            //分类商品服务查询
            Map<Integer, List<RecommendedInfoProductServiceInfo>> serviceInfoHashMap = new HashMap<>();
            List<Integer> productInfoIdList = productInfoList.stream().map(RecommendedInfoProductInfo::getId).collect(Collectors.toList());
            List<SubRecommendedProductServiceConfig> serviceConfigList = serviceConfigService.lambdaQuery().in(SubRecommendedProductServiceConfig::getFkProductId, productInfoIdList).list();
            if(CollectionUtil.isNotEmpty(serviceConfigList)) {
                List<Integer> ppidList = serviceConfigList.stream().map(SubRecommendedProductServiceConfig::getPpid).collect(Collectors.toList());
                //获取商品基本信息
                Map<Integer, ProductSimpleBO> productInfoMap = productInfoService.getProductMapByPpidsNew(ppidList);
                //价格查询调用主站接口
                Map<String, Map<Integer, ServiceInfo>> serviceMap = selectPriceMap(productInfoList);
                serviceInfoHashMap = serviceConfigList.stream().map(item -> {
                    RecommendedInfoProductServiceInfo recommendedInfoProductServiceInfo = new RecommendedInfoProductServiceInfo();
                    BigDecimal price = serviceMap.getOrDefault(Optional.ofNullable(item.getFkProductUuid()).orElse(item.getFkProductId()+""), new HashMap<>()).getOrDefault(item.getPpid(), new ServiceInfo()).getPrice();
                    ProductSimpleBO productSimpleBO = productInfoMap.getOrDefault(item.getPpid(), new ProductSimpleBO());
                    String productName = Optional.ofNullable(productSimpleBO.getProductName()).orElse("");
                    if(ObjectUtil.isNull(price) || price.compareTo(BigDecimal.ZERO) == 0){
                        recommendedInfo.setErrorMessage(productName+productSimpleBO.getProductColor()+"的商品价格为空或0，不能在套餐中配置此年包服务");
                    }
                    recommendedInfoProductServiceInfo.setPpid(item.getPpid())
                            .setId(item.getId())
                            .setProductName(productName+productSimpleBO.getProductColor())
                            .setPrice(price)
                            .setProductColor(productInfoMap.getOrDefault(item.getPpid(), new ProductSimpleBO()).getProductColor())
                            .setCreateTime(item.getCreateTime())
                            .setFkCategoryId(item.getFkCategoryId())
                            .setFkPackageId(item.getFkPackageId())
                            .setFkProductId(item.getFkProductId());
                    return recommendedInfoProductServiceInfo;
                }).collect(Collectors.groupingBy(RecommendedInfoProductServiceInfo::getFkProductId));
            }
            //分类商品服务设置
            for (RecommendedInfoProductInfo item : productInfoList){
                item.setServiceInfoList(serviceInfoHashMap.getOrDefault(item.getId(),new ArrayList()));
            }
            Map<Integer, List<RecommendedInfoProductInfo>> productInfoMap = productInfoList.stream().collect(Collectors.groupingBy(RecommendedInfoProductInfo::getFkCategoryId));
            List<RecommendedInfoPackageInfo> packageList = recommendedInfo.getPackageList();
            for (RecommendedInfoPackageInfo packageInfo : packageList){
                List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
                if(CollectionUtil.isNotEmpty(categoryInfoList)){
                    for (RecommendedInfoCategoryInfo categoryInfo : categoryInfoList){
                        categoryInfo.setProductInfoList(productInfoMap.get(categoryInfo.getId()));
                    }
                }
            }
        }
        //套餐排序
        sortPackageList(recommendedInfo);
        return recommendedInfo;
    }


    /**
     * Map<Integer,Map<Integer,ServiceInfo>> 第一个Integer绑定商品的id  第二个Integer服务的ppid
     * @param productInfoList
     * @return
     */
    private Map<String,Map<Integer,ServiceInfo>> selectPriceMap(List<RecommendedInfoProductInfo> productInfoList){
        Map<String,Map<Integer,ServiceInfo>> map = new HashMap<>();
        if(CollectionUtil.isEmpty(productInfoList)){
            return map;
        }
        //过滤出壳膜商品的ppid
        List<RecommendedInfoProductInfo> caseOrFilmProductInfoList = productInfoList.stream().filter(item->{
            return Optional.ofNullable(item.getIsCaseOrFilm()).orElse(Boolean.FALSE);
        }).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(caseOrFilmProductInfoList)){
            return map;
        }
        Map<Integer, List<ServiceInfo>> serviceInfoMap = serviceConfigService.getServiceInfoMap(caseOrFilmProductInfoList.stream().map(RecommendedInfoProductInfo::getPpid).collect(Collectors.toList()));
        if(CollectionUtil.isEmpty(serviceInfoMap)){
            return map;
        }
        caseOrFilmProductInfoList.forEach(item->{
            Map<Integer, ServiceInfo> infoMap = new HashMap<>();
            List<ServiceInfo> serviceInfos = serviceInfoMap.get(item.getPpid());
            if(CollectionUtil.isNotEmpty(serviceInfos)){
                infoMap = serviceInfos.stream().collect(Collectors.toMap(ServiceInfo::getPpid,Function.identity(),(v1,v2)->v1));
            }
            //编辑的时候因为保存过所以没有id  所以编辑的时候采用虚拟id - uuid 已经存在id的情况那就采用id
            map.put(Optional.ofNullable(item.getUuid()).orElse(item.getId()+""),infoMap);
        });
        return map;
    }



    /**
     * 排序套餐
     * @param recommendedInfo
     */
    private void sortPackageList(RecommendedInfo recommendedInfo){
        List<RecommendedInfoPackageInfo> packageList = recommendedInfo.getPackageList();
        if(CollectionUtil.isNotEmpty(packageList)){
            packageList.sort(Comparator.comparing(item->Optional.ofNullable(item.getSeqNum()).orElse(Integer.MAX_VALUE)));
            packageList.forEach(item->{
                List<RecommendedInfoCategoryInfo> categoryInfoList = item.getCategoryInfoList();
                if(CollectionUtil.isNotEmpty(categoryInfoList)){
                    categoryInfoList.sort(Comparator.comparing(obj->Optional.ofNullable(obj.getSeqNum()).orElse(Integer.MAX_VALUE)));
                    categoryInfoList.forEach(obj->{
                        List<RecommendedInfoProductInfo> productInfoList = obj.getProductInfoList();
                        if(CollectionUtil.isNotEmpty(productInfoList)){
                            productInfoList.sort(Comparator.comparing(productInfo->Optional.ofNullable(productInfo.getSeqNum()).orElse(Integer.MAX_VALUE)));
                        }
                    });
                }
            });
        }
    }

    /**
     * 数据验证和处理
     * @param req
     */
    private void handleAndCheck(RecommendedInfo req,OaUserBO userBO){
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        //只能编辑和删除自己添加的套餐，别人添加的套餐不能编辑和删除；
        if(ObjectUtil.isNotNull(mainInfo.getId())){
            SubRecommendedMainConfig mainConfig = Optional.ofNullable(this.getById(mainInfo.getId())).orElseThrow(() -> new CustomizeException("套餐不存在"));
            if(!userBO.getUserName().equals(mainConfig.getCreateUser())){
                throw new CustomizeException("只能编辑和删除自己添加的套餐");
            }
        }
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(!rankList.contains(rank)){
            throw new CustomizeException("没有权限");
        }
        //生效默认时间设置
        if(ObjectUtil.isNull(mainInfo.getStartTime())){
            mainInfo.setStartTime(SubRecommendedMainConfig.getDefaultStartTime());
        }
        if(ObjectUtil.isNull(mainInfo.getEndTime())){
            mainInfo.setEndTime(SubRecommendedMainConfig.getDefaultEndTime());
        }
        //主推判断
        List<RecommendedInfoPackageInfo> packageList = req.getPackageList();
        List<Integer> ppidList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(packageList)){
            List<RecommendedInfoPackageInfo> collect = packageList.stream().filter(item -> NumberConstant.ONE.equals(item.getIsMain())).collect(Collectors.toList());
            if(collect.size() > NumberConstant.ONE){
                throw new CustomizeException("主推套餐只能有一个");
            }
            //获取所有商品的ppid
            ppidList = packageList.stream()
                    .filter(Objects::nonNull)
                    .flatMap(packageInfo -> {
                        List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
                        if(CollectionUtil.isNotEmpty(categoryInfoList)){
                            return categoryInfoList.stream();
                        }
                        return null;
                    }).filter(Objects::nonNull)
                            .flatMap(categoryInfo -> {
                                List<RecommendedInfoProductInfo> productInfoList = categoryInfo.getProductInfoList();
                                if(CollectionUtil.isNotEmpty(productInfoList)){
                                    return productInfoList.stream();
                                }
                                return null;
                            }).filter(Objects::nonNull)
                                    .map(RecommendedInfoProductInfo::getPpid).filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        List<Integer> applyAreaIdList = req.getApplyAreaIdList();
        //非总部人员地区校验
        if(!isHeadquartersPersonnel(userBO.getUserId())){
            if(CollectionUtil.isEmpty(applyAreaIdList) && !isHeadquartersPersonnel(userBO.getUserId())){
                throw new CustomizeException("非总部人员，必须选择适用地区");
            }
            //人员门店权限控制
            List<Integer> areaIdList = areaInfoService.selectRankAreaId(userBO.getUserId());
            if(CollectionUtil.isNotEmpty(applyAreaIdList)){
                List<Integer> areaIds = applyAreaIdList.stream().filter(item -> !areaIdList.contains(item)).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(areaIds)){
                    Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(areaIds);
                    String area = areaMap.values().stream().map(AreaInfo::getArea).collect(Collectors.joining(","));
                    throw new CustomizeException("以下适用地区权限不足："+area);
                }
            }
        }
        List<RecommendedInfoMainProductInfo> productList = req.getProductList();
        if(CollectionUtil.isEmpty(productList)){
            throw new CustomizeException("套餐主商品不能为空");
        }
        //商品校验
        //根据ppid查询商品信息 价格库存
        Integer createAreaId = Optional.ofNullable(Optional.ofNullable(req.getMainInfo()).orElse(new RecommendedInfoMainInfo()).getCreateAreaId()).orElse(userBO.getArea1id());
        Map<Integer, ProductBaseInfo> productBaseInfoMap = productInfoService.selectProductBaseInfoMap(ppidList,createAreaId);
        if(CollectionUtil.isNotEmpty(packageList)){
            for (RecommendedInfoPackageInfo packageInfo : packageList){
                List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
                if(CollectionUtil.isNotEmpty(categoryInfoList)){
                    for (RecommendedInfoCategoryInfo categoryInfo : categoryInfoList){
                        List<RecommendedInfoProductInfo> productInfoList = categoryInfo.getProductInfoList();
                        if(CollectionUtil.isNotEmpty(productInfoList)){
                            Map<String, Map<Integer, ServiceInfo>> serviceMap = selectPriceMap(productInfoList);
                            for (RecommendedInfoProductInfo productInfo : productInfoList){
                                Integer ppid = productInfo.getPpid();
                                if(ObjectUtil.isNull(ppid)){
                                    throw new CustomizeException("配置分类套餐里面的商品不能为空");
                                }
                                ProductBaseInfo baseInfo = productBaseInfoMap.getOrDefault(ppid, new ProductBaseInfo());
                                BigDecimal salePrice = productInfo.getSalePrice();
                                if(ObjectUtil.isNull(salePrice)|| salePrice.compareTo(BigDecimal.ZERO) <0){
                                    throw new CustomizeException("售价不能为空");
                                }
                                //1.年包的售价自动填充会员价，不能修改
                                if(productInfo.getIsAnnualPackage() && salePrice.compareTo(baseInfo.getOriginalPrice()) != 0) {
                                   throw new CustomizeException(productInfo.getProductName()+"年包售价不能修改");
                                }
                                //2. 原装商品的售价不能低于成本价，非原装商品的售价不能低于会员价的50%且不能低于成本价（即：若会员价的50%计算出来是低于成本价的，也不可以）；
                                String name = Optional.ofNullable(productInfo.getProductName()).orElseThrow(() -> new CustomizeException(productInfo.getPpid() + "商品名称不能为空"));
                                if(!name.contains("原装")){
                                    if(salePrice.compareTo(baseInfo.getOriginalPrice().multiply(new BigDecimal("0.5")))<0){
                                        throw new CustomizeException("非原装商品"+productInfo.getProductName()+"的售价不能低于会员价的50%");
                                    }
                                }
                                BigDecimal costPrice = baseInfo.getCostPrice();
                                if(salePrice.compareTo(costPrice) < 0){
                                    throw new CustomizeException(productInfo.getProductName()+"售价不能低于成本价");
                                }
                                List<RecommendedInfoProductServiceInfo> serviceInfoList = productInfo.getServiceInfoList();
                                if(CollectionUtil.isNotEmpty(serviceInfoList)){
                                    serviceInfoList.forEach(item->{
                                        BigDecimal price = serviceMap.getOrDefault(item.getFkProductUuid(), new HashMap<>()).getOrDefault(item.getPpid(), new ServiceInfo()).getPrice();
                                        if(ObjectUtil.isNull(price) || price.compareTo(BigDecimal.ZERO) == 0) {
                                            throw new CustomizeException(item.getProductName() + "的商品价格为空或0，不能在套餐中配置此年包服务");
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public SaveOrUpdateRes saveOrUpdate(RecommendedInfo req) {
        //建立管理关系
        SubRecommendedUtils.initializationUuid(req);
        OaUserBO userBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new ClassCastException("当前用户信息为空"));
        //数据验证
        handleAndCheck(req,userBO);
        //日志信息封装
        StringJoiner comment = new StringJoiner(",");
        //套餐主要信息处理
        SubRecommendedMainConfig subRecommendedMainConfig = handleSubRecommendedMainConfig(req, comment, userBO);
        //套餐门店处理
        List<SubRecommendedMainAreaConfig> subRecommendedMainAreaConfigs = handleSubRecommendedMainAreaConfig(req, comment, userBO);
        //套餐主商品处理
        List<SubRecommendedMainProductConfig> subRecommendedMainProductConfigs = handleSubRecommendedMainProductConfig(req, comment, userBO);
        //套餐名称处理
        Map<Integer,List<SubRecommendedPackageConfig>> subRecommendedPackageConfigMap = handleSubRecommendedPackageConfig(req, comment, userBO);
        //套餐分类处理
        Map<Integer, List<SubRecommendedCategoryConfig>> subRecommendedCategoryConfigMap = handleSubRecommendedCategoryConfig(req, comment, userBO);
        //套餐商品处理
        Map<Integer, List<SubRecommendedProductConfig>> subRecommendedProductConfigMap = handleSubRecommendedProductConfig(req, comment, userBO);
        //套餐商品服务处理
        Map<Integer, List<SubRecommendedProductServiceConfig>> subRecommendedProductServiceConfigMap = handleSubRecommendedProductServiceConfig(req, comment, userBO);
        //数据库保存
        SaveOrUpdateRes saveOrUpdateRes = new SaveOrUpdateRes();
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            this.saveOrUpdate(subRecommendedMainConfig);
            Integer mainConfigId = subRecommendedMainConfig.getId();
            saveOrUpdateRes.setMainConfigId(mainConfigId);
            //地区保存
            if(CollectionUtil.isNotEmpty(subRecommendedMainAreaConfigs)){
                //删除之前的门店数据
                areaConfigService.lambdaUpdate().eq(SubRecommendedMainAreaConfig::getFkConfigId, mainConfigId)
                                .set(SubRecommendedMainAreaConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedMainAreaConfig::getIsDel, NumberConstant.ONE).update();
                subRecommendedMainAreaConfigs.forEach(item->{
                    item.setFkConfigId(mainConfigId);
                    areaConfigService.saveOrUpdate(item);
                });
            }
            //主商品保存
            if(CollectionUtil.isNotEmpty(subRecommendedMainProductConfigs)){
                //删除之前的商品
                mainProductService.lambdaUpdate().eq(SubRecommendedMainProductConfig::getFkConfigId, mainConfigId)
                        .set(SubRecommendedMainProductConfig::getUpdateTime,LocalDateTime.now())
                        .set(SubRecommendedMainProductConfig::getIsDel, NumberConstant.ONE).update();
                subRecommendedMainProductConfigs.forEach(item->{
                    item.setFkConfigId(mainConfigId);
                    mainProductService.saveOrUpdate(item);
                });
            }
            //套餐名称保存
            Map<String, SubRecommendedPackageConfig> packageConfigMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(subRecommendedPackageConfigMap)){
                //数据删除
                List<SubRecommendedPackageConfig> listDel = subRecommendedPackageConfigMap.get(del);
                if(CollectionUtil.isNotEmpty(listDel)){
                    listDel.forEach(item->{
                        packageConfigService.removeById(item.getId());
                        categoryConfigService.lambdaUpdate().eq(SubRecommendedCategoryConfig::getFkPackageId,item.getId())
                                .set(SubRecommendedCategoryConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedCategoryConfig::getIsDel,true).update();
                        productConfigService.lambdaUpdate().eq(SubRecommendedProductConfig::getFkPackageId,item.getId())
                                .set(SubRecommendedProductConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedProductConfig::getIsDel,true).update();
                        serviceConfigService.lambdaUpdate().eq(SubRecommendedProductServiceConfig::getFkPackageId,item.getId())
                                .set(SubRecommendedProductServiceConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedProductServiceConfig::getIsDel,true).update();
                    });
                }
                //数据新增或修改
                List<SubRecommendedPackageConfig> listSaveOrUpdate = subRecommendedPackageConfigMap.get(saveOrUpdate);
                if(CollectionUtil.isNotEmpty(listSaveOrUpdate)){
                    listSaveOrUpdate.forEach(item->{
                        item.setFkConfigId(mainConfigId);
                        packageConfigService.saveOrUpdate(item);
                    });
                }
                packageConfigMap = listSaveOrUpdate.stream().collect(Collectors.toMap(SubRecommendedPackageConfig::getUuid, Function.identity()));
            }
            //套餐分类保存
            Map<String, SubRecommendedCategoryConfig>  categoryConfigMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(subRecommendedCategoryConfigMap)){
                //数据删除
                List<SubRecommendedCategoryConfig> listDel = subRecommendedCategoryConfigMap.get(del);
                if(CollectionUtil.isNotEmpty(listDel)){
                    listDel.forEach(item->{
                        categoryConfigService.removeById(item.getId());
                        productConfigService.lambdaUpdate().eq(SubRecommendedProductConfig::getFkCategoryId,item.getId())
                                .set(SubRecommendedProductConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedProductConfig::getIsDel,true).update();
                        serviceConfigService.lambdaUpdate().eq(SubRecommendedProductServiceConfig::getFkCategoryId,item.getId())
                                .set(SubRecommendedProductServiceConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedProductServiceConfig::getIsDel,true).update();
                    });
                }
                //数据新增或修改
                List<SubRecommendedCategoryConfig> listSaveOrUpdate = subRecommendedCategoryConfigMap.get(saveOrUpdate);
                if(CollectionUtil.isNotEmpty(listSaveOrUpdate)){
                    for (SubRecommendedCategoryConfig categoryConfig: listSaveOrUpdate){
                        categoryConfig.setFkPackageId(packageConfigMap.getOrDefault(categoryConfig.getFkPackageUuid(),new SubRecommendedPackageConfig()).getId());
                        categoryConfigService.saveOrUpdate(categoryConfig);
                    }
                }
                categoryConfigMap = listSaveOrUpdate.stream().collect(Collectors.toMap(SubRecommendedCategoryConfig::getUuid, Function.identity()));
            }
            //套餐商品保存
            Map<String, SubRecommendedProductConfig> productConfigMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(subRecommendedProductConfigMap)){
                //数据删除
                List<SubRecommendedProductConfig> listDel = subRecommendedProductConfigMap.get(del);
                if(CollectionUtil.isNotEmpty(listDel)){
                    listDel.forEach(item->{
                        productConfigService.removeById(item.getId());
                        serviceConfigService.lambdaUpdate().eq(SubRecommendedProductServiceConfig::getFkProductId,item.getId())
                                .set(SubRecommendedProductServiceConfig::getUpdateTime,LocalDateTime.now())
                                .set(SubRecommendedProductServiceConfig::getIsDel,true).update();
                    });
                }
                //数据新增或修改
                List<SubRecommendedProductConfig> listSaveOrUpdate = subRecommendedProductConfigMap.get(saveOrUpdate);
                if(CollectionUtil.isNotEmpty(listSaveOrUpdate)){
                    for (SubRecommendedProductConfig productConfig: listSaveOrUpdate){
                        productConfig.setFkCategoryId(categoryConfigMap.getOrDefault(productConfig.getFkCategoryUuid(),new SubRecommendedCategoryConfig()).getId());
                        productConfig.setFkPackageId(packageConfigMap.getOrDefault(productConfig.getFkPackageUuid(),new SubRecommendedPackageConfig()).getId());
                        productConfigService.saveOrUpdate(productConfig);
                    }
                }
                productConfigMap=listSaveOrUpdate.stream().collect(Collectors.toMap(SubRecommendedProductConfig::getUuid, Function.identity()));
            }
            //商品服务保存
            if(CollectionUtil.isNotEmpty(subRecommendedProductServiceConfigMap)){
                //数据删除
                List<SubRecommendedProductServiceConfig> listDel = subRecommendedProductServiceConfigMap.get(del);
                if(CollectionUtil.isNotEmpty(listDel)){
                    listDel.forEach(item->{
                        serviceConfigService.removeById(item.getId());
                    });
                }
                //数据新增或修改
                List<SubRecommendedProductServiceConfig> listSaveOrUpdate = subRecommendedProductServiceConfigMap.get(saveOrUpdate);
                if(CollectionUtil.isNotEmpty(listSaveOrUpdate)){
                    for (SubRecommendedProductServiceConfig serviceConfig: listSaveOrUpdate){
                        serviceConfig.setFkProductId(productConfigMap.getOrDefault(serviceConfig.getFkProductUuid(),new SubRecommendedProductConfig()).getId());
                        serviceConfig.setFkCategoryId(categoryConfigMap.getOrDefault(serviceConfig.getFkCategoryUuid(),new SubRecommendedCategoryConfig()).getId());
                        serviceConfig.setFkPackageId(packageConfigMap.getOrDefault(serviceConfig.getFkPackageUuid(),new SubRecommendedPackageConfig()).getId());
                        serviceConfigService.saveOrUpdate(serviceConfig);
                    }
                }
            }
        }).commit();
        //日志记录
        logService.insertLog(saveOrUpdateRes.getMainConfigId(),comment.toString(),userBO.getUserName());
        return saveOrUpdateRes;
    }

    @Override
    public void updateMainInfo(UpdateMainInfoReq req) {
        Integer mainConfigId = req.getMainConfigId();
        SubRecommendedMainConfig mainConfig = Optional.ofNullable(this.getById(mainConfigId)).orElseThrow(() -> new CustomizeException("主表信息不存在"));
        OaUserBO userBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息不存在"));
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(!rankList.contains(rank)){
            throw new CustomizeException("没有权限");
        }
        if(!userBO.getUserName().equals(mainConfig.getCreateUser())){
            throw new CustomizeException("当前用户不是创建人，不能修改");
        }
        Integer isDel =req.getIsDel();
        //如果是直接删除那就不用管是否隐藏了
        if(NumberConstant.ONE.equals(isDel)){
            this.removeById(mainConfigId);
            return;
        }
        Integer isEnabledOld = mainConfig.getIsEnabled();
        Integer isEnabledNew = req.getIsEnabled();
        if(ObjectUtil.isNotNull(isEnabledNew) && !isEnabledNew.equals(isEnabledOld)){
            //如果是由禁用修改为启用需要进行校验 服务价格
            if(NumberConstant.ZERO.equals(isEnabledOld)&&NumberConstant.ONE.equals(isEnabledNew)){
                RecommendedByIdReq recommended = new RecommendedByIdReq();
                recommended.setId(mainConfigId);
                RecommendedInfo recommendedById = getRecommendedById(recommended);
                String errorMessage = recommendedById.getErrorMessage();
                if(StringUtil.isNotEmpty(errorMessage)){
                    throw new CustomizeException(errorMessage);
                }
            }
            this.lambdaUpdate().eq(SubRecommendedMainConfig::getId,mainConfigId)
                    .set(SubRecommendedMainConfig::getIsEnabled,isEnabledNew)
                    .set(SubRecommendedMainConfig::getUpdateTime,LocalDateTime.now())
                    .update();
            String isEnabledOldValue = NumberConstant.ONE.equals(isEnabledOld) ? "否" : "是";
            String isEnabledNewValue = NumberConstant.ONE.equals(isEnabledNew) ? "否" : "是";
            String comment = SubRecommendedUtils.dataComparison(isEnabledOldValue, isEnabledNewValue, "是否隐藏");
            logService.insertLog(mainConfigId,comment.toString(),userBO.getUserName());
        }
    }

    private List<SubRecommendedProductConfig> updateProductInfo(OaUserBO userBO,Map<String, StringJoiner> commentMap,RecommendedInfoCategoryInfo category,RecommendedInfoPackageInfo packageInfo,List<SubRecommendedProductConfig> subRecommendedProductConfigListDel){
        List<SubRecommendedProductConfig> subRecommendedProductConfigListNew =new ArrayList<>();
        //查询出原来的分类的商品信息
        Map<Integer, SubRecommendedProductConfig> subRecommendedProductConfigOldMap = productConfigService.lambdaQuery().eq(SubRecommendedProductConfig::getFkCategoryId, category.getId())
                .list().stream().collect(Collectors.toMap(SubRecommendedProductConfig::getId, Function.identity()));
        List<RecommendedInfoProductInfo> productInfoList = category.getProductInfoList();
        List<SubRecommendedProductConfig> recommendedInfoProductConfigList = SubRecommendedUtils.createRecommendedInfoProductConfigList(productInfoList, userBO);
        if(CollectionUtil.isNotEmpty(recommendedInfoProductConfigList)){
            subRecommendedProductConfigListNew.addAll(recommendedInfoProductConfigList);
        }
        if(CollectionUtil.isNotEmpty(subRecommendedProductConfigListNew)){
            //遍历新分类的商品 然后找老分类的商品（1.如果能找到的那就行数据对比 2.如果找不到那就是新增）
            for (SubRecommendedProductConfig productConfigNew: subRecommendedProductConfigListNew){
                SubRecommendedProductConfig subRecommendedProductConfig = subRecommendedProductConfigOldMap.get(Optional.ofNullable(productConfigNew.getId()).orElse(Integer.MAX_VALUE));
              //  RecommendedInfoPackageInfo packageInfo = packageInfoMap.getOrDefault(productConfigNew.getFkPackageUuid(), new RecommendedInfoPackageInfo());
                String key = packageInfo.getName() + ":" + category.getName()+":";
                if(ObjectUtil.isNotNull(subRecommendedProductConfig)){
                    //分类的商品名称 数据对比
                    String ppid = SubRecommendedUtils.dataComparison(subRecommendedProductConfig.getPpid(), productConfigNew.getPpid(), productConfigNew.getPpid()+":"+"ppid");
                    if(StringUtil.isNotBlank(ppid)){
                        SubRecommendedUtils.addComment(commentMap, key, ppid);
                    }
                    //分类的商品排序 数据对比
                    String seqNum = SubRecommendedUtils.dataComparison(subRecommendedProductConfig.getSeqNum(), productConfigNew.getSeqNum(), productConfigNew.getPpid()+":"+"排序");
                    if(StringUtil.isNotBlank(seqNum)){
                        SubRecommendedUtils.addComment(commentMap, key, seqNum);
                    }
                    //分类的商品排序 数据对比
                    String salePrice = SubRecommendedUtils.dataComparison(subRecommendedProductConfig.getSalePrice(), productConfigNew.getSalePrice(), productConfigNew.getPpid()+":"+"售价");
                    if(StringUtil.isNotBlank(salePrice)){
                        SubRecommendedUtils.addComment(commentMap, key, salePrice);
                    }

                } else {
                    if(ObjectUtil.isNotNull(productConfigNew.getPpid())){
                        SubRecommendedUtils.addComment(commentMap, key+"新增ppid:", productConfigNew.getPpid().toString());
                    }
                }
            }
            //判断老分类是否为空 如果不为空那就需 (用老分类来遍历找新分类 如果找不到那就删除)
            if(CollectionUtil.isNotEmpty(subRecommendedProductConfigOldMap)){
                List<SubRecommendedProductConfig> collect = subRecommendedProductConfigOldMap.values().stream().collect(Collectors.toList());
                Map<Integer, SubRecommendedProductConfig> productConfigNewMap = subRecommendedProductConfigListNew.stream()
                        .filter(item -> ObjectUtil.isNotNull(item.getId()))
                        .collect(Collectors.toMap(SubRecommendedProductConfig::getId, Function.identity()));
                collect.forEach(oldProductConfig->{
                    SubRecommendedProductConfig subRecommendedProductConfig = productConfigNewMap.get(oldProductConfig.getId());
                    if(ObjectUtil.isNull(subRecommendedProductConfig)){
                      //  RecommendedInfoPackageInfo packageInfo = packageInfoMap.getOrDefault(category.getFkPackageUuid(), new RecommendedInfoPackageInfo());
                        String key = packageInfo.getName() + ":" + category.getName()+":";
                        if(ObjectUtil.isNotNull(oldProductConfig.getPpid())){
                            SubRecommendedUtils.addComment(commentMap, key+"删除ppid:", oldProductConfig.getPpid().toString());
                            subRecommendedProductConfigListDel.add(oldProductConfig);
                        }
                    }
                });
            }
        } else {
            //如果新套餐为空那就是删除所有老套餐  把packageList里面的name用逗号拼接起来
            String productPpidStr =  subRecommendedProductConfigOldMap.values().stream()
                    .filter(item->ObjectUtil.isNotNull(item.getPpid()))
                    .map(item->item.getPpid().toString()).collect(Collectors.joining(","));
            if(StringUtil.isNotBlank(productPpidStr)){
                SubRecommendedProductConfig subRecommendedProductConfig = subRecommendedProductConfigOldMap.values().stream().collect(Collectors.toList()).get(0);
              //  RecommendedInfoPackageInfo packageInfo = packageInfoMap.getOrDefault(subRecommendedProductConfig.getFkPackageUuid(), new RecommendedInfoPackageInfo());
                String key = packageInfo.getName()+":"+category.getName()+":";
                SubRecommendedUtils.addComment(commentMap, key+"删除ppid:", productPpidStr);
                subRecommendedProductConfigListDel.addAll(subRecommendedProductConfigOldMap.values());
            }
        }
        return subRecommendedProductConfigListNew;
    }

    /**
     * 套餐商品服务处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private Map<Integer,List<SubRecommendedProductServiceConfig>> handleSubRecommendedProductServiceConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        List<RecommendedInfoPackageInfo> packageList = req.getPackageList();
        List<RecommendedInfoProductServiceInfo> serviceInfoListNew = packageList.stream()
                .filter(item-> CollectionUtil.isNotEmpty(item.getCategoryInfoList()))
                .flatMap(item -> item.getCategoryInfoList().stream()).filter(Objects::nonNull)
                .filter(item-> CollectionUtil.isNotEmpty(item.getProductInfoList()))
                .flatMap(item -> item.getProductInfoList().stream()).filter(Objects::nonNull)
                .filter(item-> CollectionUtil.isNotEmpty(item.getServiceInfoList()))
                .flatMap(item -> item.getServiceInfoList().stream()).filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, StringJoiner> commentMap = new HashMap<>();
        List<SubRecommendedProductServiceConfig> subRecommendedProductServiceConfigNew = new ArrayList<>();
        List<SubRecommendedProductServiceConfig> subRecommendedProductServiceConfigDel = new ArrayList<>();
        //过滤出serviceInfoListNew 里面 id 为空的数据（id为空说明需要新增）
        List<RecommendedInfoProductServiceInfo> serviceInfoListAdd = serviceInfoListNew.stream().filter(item -> ObjectUtil.isNull(item.getId())).collect(Collectors.toList());
        subRecommendedProductServiceConfigNew.addAll(SubRecommendedUtils.createRecommendedInfoProductServiceInfoList(serviceInfoListAdd, userBO));
        //判断是修改的状态并且subRecommendedProductServiceConfigNew 新加的数据不为空
        if(ObjectUtil.isNotNull(req.getMainInfo().getId()) && CollectionUtil.isNotEmpty(subRecommendedProductServiceConfigNew)){
            //判断是修改的时候进行日志记录
            //套餐map集合
            Map<String, RecommendedInfoPackageInfo> packageInfoMap = packageList.stream().collect(Collectors.toMap(RecommendedInfoPackageInfo::getUuid, Function.identity()));
            //套餐分类map集合
            Map<String, RecommendedInfoCategoryInfo> categoryInfoMap = packageList.stream()
                    .filter(item-> CollectionUtil.isNotEmpty(item.getCategoryInfoList()))
                    .flatMap(item -> item.getCategoryInfoList().stream()).filter(Objects::nonNull)
                    .collect(Collectors.toMap(RecommendedInfoCategoryInfo::getUuid, Function.identity()));
            //套餐分类商品map集合
            Map<String, RecommendedInfoProductInfo> productInfoMap = packageList.stream()
                    .filter(item-> CollectionUtil.isNotEmpty(item.getCategoryInfoList()))
                    .flatMap(item -> item.getCategoryInfoList().stream()).filter(Objects::nonNull)
                    .filter(item-> CollectionUtil.isNotEmpty(item.getProductInfoList()))
                    .flatMap(item -> item.getProductInfoList().stream()).filter(Objects::nonNull)
                    .collect(Collectors.toMap(RecommendedInfoProductInfo::getUuid, Function.identity()));
            //获取商品信息
            Map<Integer, ProductSimpleBO> productOldMap = productInfoService.getProductMapByPpidsNew(subRecommendedProductServiceConfigNew.stream().map(SubRecommendedProductServiceConfig::getPpid).collect(Collectors.toList()));
            subRecommendedProductServiceConfigNew.forEach(item->{
                String key = packageInfoMap.getOrDefault(item.getFkPackageUuid(), new RecommendedInfoPackageInfo()).getName()+":"+
                        categoryInfoMap.getOrDefault(item.getFkCategoryUuid(), new RecommendedInfoCategoryInfo()).getName()+":"+
                        productInfoMap.getOrDefault(item.getFkProductUuid(), new RecommendedInfoProductInfo()).getPpid()+":";
                ProductSimpleBO productSimpleBO = productOldMap.getOrDefault(item.getPpid(), new ProductSimpleBO());
                String value= Optional.ofNullable(productSimpleBO.getProductName()).orElse("")+productSimpleBO.getProductColor();
                SubRecommendedUtils.addComment(commentMap, key, "新增服务:"+value);
            });
        }
        //过滤出serviceInfoListNew 里面 id 不为空的数据（该部分数据和原来数据库数据做对比，如果过数据库里面的数据在serviceInfoNewMap找不到那就需要删除）
        Map<Integer, RecommendedInfoProductServiceInfo> serviceInfoNewMap = serviceInfoListNew.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).collect(Collectors.toMap(RecommendedInfoProductServiceInfo::getId, Function.identity()));
        //查询出该配置所有的套餐商品服务
        List<SubRecommendedProductServiceConfig> serviceInfoListOld = serviceConfigService.lambdaQuery().in(SubRecommendedProductServiceConfig::getFkPackageId, packageList.stream().map(item -> item.getId())
                .collect(Collectors.toList())).list();
        //判断是否存在老数据
        if(CollectionUtil.isNotEmpty(serviceInfoListOld)){
            //套餐map集合
            Map<Integer, SubRecommendedPackageConfig> packageInfoMap = packageConfigService.lambdaQuery().in(SubRecommendedPackageConfig::getId, serviceInfoListOld.stream()
                            .map(SubRecommendedProductServiceConfig::getFkPackageId).collect(Collectors.toList())).list().stream()
                    .collect(Collectors.toMap(SubRecommendedPackageConfig::getId, Function.identity()));
            //套餐分类map集合
            Map<Integer, SubRecommendedCategoryConfig> categoryInfoMap = categoryConfigService.lambdaQuery().in(SubRecommendedCategoryConfig::getId, serviceInfoListOld.stream()
                            .map(SubRecommendedProductServiceConfig::getFkCategoryId).collect(Collectors.toList())).list().stream()
                    .collect(Collectors.toMap(SubRecommendedCategoryConfig::getId, Function.identity()));
            //套餐分类商品map集合
            Map<Integer, SubRecommendedProductConfig> productInfoMap = productConfigService.lambdaQuery().in(SubRecommendedProductConfig::getId, serviceInfoListOld.stream()
                            .map(SubRecommendedProductServiceConfig::getFkProductId).collect(Collectors.toList())).list().stream()
                    .collect(Collectors.toMap(SubRecommendedProductConfig::getId, Function.identity()));
            //获取商品信息
            Map<Integer, ProductSimpleBO> productOldMap = productInfoService.getProductMapByPpidsNew(serviceInfoListOld.stream().map(SubRecommendedProductServiceConfig::getPpid).collect(Collectors.toList()));

            serviceInfoListOld.forEach(item ->{
                RecommendedInfoProductServiceInfo recommendedInfoProductServiceInfo = serviceInfoNewMap.get(item.getId());
                if(ObjectUtil.isNull(recommendedInfoProductServiceInfo)){
                    subRecommendedProductServiceConfigDel.add(item);
                    String key = packageInfoMap.getOrDefault(item.getFkPackageId(), new SubRecommendedPackageConfig()).getName()+":"+
                            categoryInfoMap.getOrDefault(item.getFkCategoryId(), new SubRecommendedCategoryConfig()).getName()+":"+
                            productInfoMap.getOrDefault(item.getFkProductId(), new SubRecommendedProductConfig()).getPpid()+":";
                    ProductSimpleBO productSimpleBO = productOldMap.getOrDefault(item.getPpid(), new ProductSimpleBO());
                    String value= Optional.ofNullable(productSimpleBO.getProductName()).orElse("")+productSimpleBO.getProductColor();
                    SubRecommendedUtils.addComment(commentMap, key, "删除服务:"+value);
                }
            });
        }

        //日志处理
        if(CollectionUtil.isNotEmpty(commentMap)){
            commentMap.forEach((key,value)->{
                String msg = String.format("{%s}", value);
                comment.add(key+msg);
            });
        }
        Map<Integer,List<SubRecommendedProductServiceConfig>> map = new HashMap<>();
        map.put(saveOrUpdate, subRecommendedProductServiceConfigNew);
        map.put(del, subRecommendedProductServiceConfigDel);
        return map;
    }
    /**
     * 套餐商品处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private Map<Integer,List<SubRecommendedProductConfig>> handleSubRecommendedProductConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        Map<String, StringJoiner> commentMap = new HashMap<>();
        List<RecommendedInfoPackageInfo> packageList = req.getPackageList();
        //套餐map集合
        Map<String, RecommendedInfoPackageInfo> packageInfoMap = packageList.stream().collect(Collectors.toMap(RecommendedInfoPackageInfo::getUuid, Function.identity()));
        //收集所有套餐分类
        List<RecommendedInfoCategoryInfo> categoryInfoList = new ArrayList<>();
        packageList.forEach(item->{
            List<RecommendedInfoCategoryInfo> list = item.getCategoryInfoList();
            if(CollectionUtil.isNotEmpty(list)){
                categoryInfoList.addAll(list);
            }
        });
        Map<String, RecommendedInfoCategoryInfo> categoryInfoMap = categoryInfoList.stream().collect(Collectors.toMap(RecommendedInfoCategoryInfo::getUuid, Function.identity()));
        //收集所有套餐分类商品
        List<RecommendedInfoProductInfo> productInfoList = new ArrayList<>();
        categoryInfoList.forEach(item->{
            List<RecommendedInfoProductInfo> list = item.getProductInfoList();
                if(CollectionUtil.isNotEmpty(list)){
                    productInfoList.addAll(list);
                }
        });
        List<SubRecommendedProductConfig> subRecommendedProductConfigListNew = new ArrayList<>();
        List<SubRecommendedProductConfig> subRecommendedProductConfigListDel = new ArrayList<>();
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        Integer mainInfoId = mainInfo.getId();
        if(ObjectUtil.isNull(mainInfoId)){
            //新增逻辑
            if(CollectionUtil.isNotEmpty(productInfoList)){
                subRecommendedProductConfigListNew.addAll(SubRecommendedUtils.createRecommendedInfoProductConfigList(productInfoList, userBO));
                //把packageList里面的name用逗号拼接起来
//                productInfoList.forEach(item->{
//                    //获取套餐
//                    RecommendedInfoPackageInfo packageInfo = packageInfoMap.getOrDefault(item.getFkPackageUuid(), new RecommendedInfoPackageInfo());
//                    //获取分类
//                    RecommendedInfoCategoryInfo categoryInfo = categoryInfoMap.getOrDefault(item.getFkCategoryUuid(), new RecommendedInfoCategoryInfo());
//                    if(ObjectUtil.isNotNull(item.getPpid())){
//                        String key = packageInfo.getName() + ":" + categoryInfo.getName() + "新增ppid:";
//                        SubRecommendedUtils.addComment(commentMap,key,item.getPpid().toString());
//                    }
//                });
            }
        } else{
            //修改逻辑
            for (RecommendedInfoPackageInfo packageInfo: packageList){
                List<RecommendedInfoCategoryInfo> categoryInfoLists = packageInfo.getCategoryInfoList();
                if(CollectionUtil.isNotEmpty(categoryInfoLists)){
                    for (RecommendedInfoCategoryInfo category :categoryInfoLists){
                        subRecommendedProductConfigListNew.addAll(updateProductInfo(userBO,commentMap,category,packageInfo,subRecommendedProductConfigListDel));
                    }
                }
            }
        }
        //日志处理
        if(CollectionUtil.isNotEmpty(commentMap)){
            commentMap.forEach((key,value)->{
                String msg = String.format("{%s}", value);
                comment.add(key+msg);
            });
        }


        Map<Integer,List<SubRecommendedProductConfig>> map = new HashMap<>();
        map.put(saveOrUpdate, subRecommendedProductConfigListNew);
        map.put(del, subRecommendedProductConfigListDel);
        return map;

    }



    /**
     * 套餐分类处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private Map<Integer,List<SubRecommendedCategoryConfig>> handleSubRecommendedCategoryConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        Map<String, StringJoiner> commentMap = new HashMap<>();
        List<RecommendedInfoPackageInfo> packageList = Optional.ofNullable(req.getPackageList()).orElse(new ArrayList<>());
        //套餐map集合
        Map<String, RecommendedInfoPackageInfo> packageInfoMap = packageList.stream().collect(Collectors.toMap(RecommendedInfoPackageInfo::getUuid, Function.identity()));
        List<SubRecommendedCategoryConfig> recommendedInfoCategoryConfigListNew = new ArrayList<>();
        List<SubRecommendedCategoryConfig> recommendedInfoCategoryConfigListDel = new ArrayList<>();
        //手机所有的套餐分类
        List<RecommendedInfoCategoryInfo> categoryInfoList = new ArrayList<>();
        packageList.forEach(item->categoryInfoList.addAll(item.getCategoryInfoList()));
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        Integer mainInfoId = mainInfo.getId();
        if(ObjectUtil.isNull(mainInfoId)){
            //新增逻辑
            if(CollectionUtil.isNotEmpty(categoryInfoList)){
                recommendedInfoCategoryConfigListNew.addAll(SubRecommendedUtils.createRecommendedInfoCategoryConfigList(categoryInfoList, userBO));
                //把packageList里面的name用逗号拼接起来
//                categoryInfoList.forEach(item->{
//                    RecommendedInfoPackageInfo packageInfo = packageInfoMap.getOrDefault(item.getFkPackageUuid(), new RecommendedInfoPackageInfo());
//                    if(StringUtil.isNotEmpty(item.getName())){
//                        SubRecommendedUtils.addComment(commentMap,packageInfo.getName()+"新增分类：",item.getName());
//                    }
//                });
            }
        } else{
            //修改逻辑
            for (RecommendedInfoPackageInfo packageInfo: packageList){
                recommendedInfoCategoryConfigListNew.addAll(updateCategoryConfig(packageInfo,userBO,commentMap,recommendedInfoCategoryConfigListDel));
            }
        }
        //日志处理
        if(CollectionUtil.isNotEmpty(commentMap)){
            commentMap.forEach((key,value)->{
                String msg = String.format("{%s}", value);
                comment.add(key+msg);
            });
        }

        Map<Integer,List<SubRecommendedCategoryConfig>> map = new HashMap<>();
        map.put(saveOrUpdate, recommendedInfoCategoryConfigListNew);
        map.put(del, recommendedInfoCategoryConfigListDel);
        return map;
    }

    private List<SubRecommendedCategoryConfig> updateCategoryConfig(RecommendedInfoPackageInfo packageInfo,OaUserBO userBO,Map<String, StringJoiner> commentMap,List<SubRecommendedCategoryConfig> recommendedInfoCategoryConfigListDel){
        List<SubRecommendedCategoryConfig> recommendedInfoCategoryConfigListNew =new ArrayList<>();
        //查询出原来的套餐名称信息
        Map<Integer, SubRecommendedCategoryConfig> subRecommendedCategoryConfigOldMap = categoryConfigService.lambdaQuery().eq(SubRecommendedCategoryConfig::getFkPackageId, packageInfo.getId())
                .list().stream().collect(Collectors.toMap(SubRecommendedCategoryConfig::getId, Function.identity()));
        List<RecommendedInfoCategoryInfo> categoryInfoList = packageInfo.getCategoryInfoList();
        List<SubRecommendedCategoryConfig> recommendedInfoCategoryConfigList = SubRecommendedUtils.createRecommendedInfoCategoryConfigList(categoryInfoList, userBO);
        if(CollectionUtil.isNotEmpty(recommendedInfoCategoryConfigList)){
            recommendedInfoCategoryConfigListNew.addAll(recommendedInfoCategoryConfigList);
        }
        if(CollectionUtil.isNotEmpty(recommendedInfoCategoryConfigListNew)){
            //遍历新套餐 然后找老套餐（1.如果能找到的那就行数据对比 2.如果找不到那就是新增）
            recommendedInfoCategoryConfigListNew.forEach(categoryConfigNew->{
                SubRecommendedCategoryConfig subRecommendedCategoryConfig = subRecommendedCategoryConfigOldMap.get(Optional.ofNullable(categoryConfigNew.getId()).orElse(Integer.MAX_VALUE));
                String key = packageInfo.getName()+":";
                if(ObjectUtil.isNotNull(subRecommendedCategoryConfig)){
                    //套餐名称 数据对比
                    String name = SubRecommendedUtils.dataComparison(subRecommendedCategoryConfig.getName(), categoryConfigNew.getName(), "分类名称");
                    if(StringUtil.isNotBlank(name)){
                        SubRecommendedUtils.addComment(commentMap, key, name);
                    }
                    //套餐排序 数据对比
                    String seqNum = SubRecommendedUtils.dataComparison(subRecommendedCategoryConfig.getSeqNum(), categoryConfigNew.getSeqNum(), categoryConfigNew.getName()+"分类排序");
                    if(StringUtil.isNotBlank(seqNum)){
                        SubRecommendedUtils.addComment(commentMap, key, seqNum);
                    }

                } else {
                    if(StringUtil.isNotBlank(categoryConfigNew.getName())){
                        SubRecommendedUtils.addComment(commentMap, key+"新增分类"+":", categoryConfigNew.getName());
                    }
                }
            });
            //判断老分类是否为空 如果不为空那就需 (用老分类来遍历找新分类 如果找不到那就删除)
            if(CollectionUtil.isNotEmpty(subRecommendedCategoryConfigOldMap)){
                List<SubRecommendedCategoryConfig> collect = subRecommendedCategoryConfigOldMap.values().stream().collect(Collectors.toList());
                Map<Integer, SubRecommendedCategoryConfig> packageConfigNewMap = recommendedInfoCategoryConfigListNew.stream()
                        .filter(item -> ObjectUtil.isNotNull(item.getId()))
                        .collect(Collectors.toMap(SubRecommendedCategoryConfig::getId, Function.identity()));
                collect.forEach(oldCategoryConfig->{
                    SubRecommendedCategoryConfig subRecommendedCategoryConfig = packageConfigNewMap.get(oldCategoryConfig.getId());
                    if(ObjectUtil.isNull(subRecommendedCategoryConfig)){
                        SubRecommendedUtils.addComment(commentMap, packageInfo.getName()+"删除分类：",oldCategoryConfig.getName());
                        recommendedInfoCategoryConfigListDel.add(oldCategoryConfig);
                    }
                });
            }
        } else {
            //如果新套餐为空那就是删除所有老套餐  把packageList里面的name用逗号拼接起来
            String cardNameListStr = subRecommendedCategoryConfigOldMap.values().stream().map(SubRecommendedCategoryConfig::getName).collect(Collectors.joining(","));
            if(StringUtil.isNotBlank(cardNameListStr)){
                SubRecommendedUtils.addComment(commentMap, packageInfo.getName()+"删除分类：",cardNameListStr);
                recommendedInfoCategoryConfigListDel.addAll(subRecommendedCategoryConfigOldMap.values());
            }
        }
        return recommendedInfoCategoryConfigListNew;
    }
    /**
     * 套餐名称处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private Map<Integer,List<SubRecommendedPackageConfig>> handleSubRecommendedPackageConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        List<RecommendedInfoPackageInfo> packageList = req.getPackageList();
        List<SubRecommendedPackageConfig> packageConfigListNew = new ArrayList<>();
        List<SubRecommendedPackageConfig> packageConfigListNewDel = new ArrayList<>();
        Map<Integer,List<SubRecommendedPackageConfig>> map = new HashMap<>();
        Integer mainInfoId = mainInfo.getId();
        if(ObjectUtil.isNull(mainInfoId)){
            //新增逻辑
            if(CollectionUtil.isNotEmpty(packageList)){
                packageConfigListNew = SubRecommendedUtils.createRecommendedInfoPackageInfoList(packageList,userBO);
                //把packageList里面的name用逗号拼接起来
                //String packageNameListStr = packageList.stream().map(RecommendedInfoPackageInfo::getName).collect(Collectors.joining(","));
                //comment.add("新增套餐"+packageNameListStr);
            }
        } else {
            //修改逻辑
            //查询出原来的套餐名称信息
            Map<Integer, SubRecommendedPackageConfig> subRecommendedPackageOldMap = packageConfigService.lambdaQuery().eq(SubRecommendedPackageConfig::getFkConfigId, mainInfoId)
                    .list().stream().collect(Collectors.toMap(SubRecommendedPackageConfig::getId, Function.identity()));
            //新套餐名称
            packageConfigListNew = SubRecommendedUtils.createRecommendedInfoPackageInfoList(packageList,userBO);
            //如果新套餐不为空
            if(CollectionUtil.isNotEmpty(packageConfigListNew)){
                //遍历新套餐 然后找老套餐（1.如果能找到的那就行数据对比 2.如果找不到那就是新增）
                packageConfigListNew.forEach(packageConfigNew->{
                    SubRecommendedPackageConfig subRecommendedPackageConfig = subRecommendedPackageOldMap.get(Optional.ofNullable(packageConfigNew.getId()).orElse(Integer.MAX_VALUE));
                    if(ObjectUtil.isNotNull(subRecommendedPackageConfig)){
                        //套餐名称 数据对比
                        String name = SubRecommendedUtils.dataComparison(subRecommendedPackageConfig.getName(), packageConfigNew.getName(), "套餐名称");
                        if(StringUtil.isNotBlank(name)){
                            comment.add(name);
                        }
                        //套餐排序 数据对比
                        String seqNum = SubRecommendedUtils.dataComparison(subRecommendedPackageConfig.getSeqNum(), packageConfigNew.getSeqNum(), "套餐排序");
                        if(StringUtil.isNotBlank(seqNum)){
                            comment.add(seqNum);
                        }
                        //套餐排序 主推对比
                        String isMainOldValue = NumberConstant.ONE.equals(subRecommendedPackageConfig.getIsMain()) ? "是" : "否";
                        String isMainNewValue = NumberConstant.ONE.equals(packageConfigNew.getIsMain()) ? "是" : "否";
                        String isMain = SubRecommendedUtils.dataComparison(isMainOldValue, isMainNewValue, "套餐主推");
                        if(StringUtil.isNotBlank(isMain)){
                            comment.add(isMain);
                        }
                    } else {
                        comment.add("新增套餐"+packageConfigNew.getName());
                    }
                });
                //判断老套餐是否为空 如果不为空那就需 (用老套餐来遍历找新套餐 如果找不到那就删除)
                if(CollectionUtil.isNotEmpty(subRecommendedPackageOldMap)){
                    List<SubRecommendedPackageConfig> collect = subRecommendedPackageOldMap.values().stream().collect(Collectors.toList());
                    Map<Integer, SubRecommendedPackageConfig> packageConfigNewMap = packageConfigListNew.stream()
                            .filter(item -> ObjectUtil.isNotNull(item.getId()))
                            .collect(Collectors.toMap(SubRecommendedPackageConfig::getId, Function.identity()));
                    collect.forEach(oldPackage->{
                        SubRecommendedPackageConfig subRecommendedPackageConfig = packageConfigNewMap.get(oldPackage.getId());
                        if(ObjectUtil.isNull(subRecommendedPackageConfig)){
                            comment.add("删除套餐"+oldPackage.getName());
                            packageConfigListNewDel.add(oldPackage);
                        }
                    });
                }
            } else {
                //如果新套餐为空那就是删除所有老套餐  把packageList里面的name用逗号拼接起来
                String packageNameListStr = subRecommendedPackageOldMap.values().stream().map(SubRecommendedPackageConfig::getName).collect(Collectors.joining(","));
                if(StringUtil.isNotBlank(packageNameListStr)){
                    comment.add("删除套餐"+packageNameListStr);
                    packageConfigListNewDel.addAll(subRecommendedPackageOldMap.values());
                }
            }
        }
        map.put(saveOrUpdate, packageConfigListNew);
        map.put(del, packageConfigListNewDel);
        return map;
    }

    /**
     * 套餐门店处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private List<SubRecommendedMainProductConfig> handleSubRecommendedMainProductConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        Integer mainInfoId = mainInfo.getId();
        List<SubRecommendedMainProductConfig> list = new ArrayList<SubRecommendedMainProductConfig>();
        //新增逻辑
        List<Integer> mainProductIdListNew = req.getProductList().stream().map(RecommendedInfoMainProductInfo::getProductId).collect(Collectors.toList());
        if(ObjectUtil.isNull(mainInfoId)){
            if(CollectionUtil.isNotEmpty(mainProductIdListNew)){
                list=SubRecommendedUtils.createDataByMainProductList(mainProductIdListNew,userBO);
                //comment.add("新增套餐主商品"+getProductId(mainProductIdListNew));
            }
        } else {
            List<SubRecommendedMainProductConfig> subRecommendedMainProductConfigs = mainProductService.lambdaQuery().eq(SubRecommendedMainProductConfig::getFkConfigId, mainInfoId).list();
            List<Integer> mainProductIdListOld = subRecommendedMainProductConfigs.stream().filter(Objects::nonNull).map(SubRecommendedMainProductConfig::getProductId).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(mainProductIdListNew) && CollectionUtil.isNotEmpty(mainProductIdListOld)){
                if(CollectionUtil.isNotEmpty(mainProductIdListNew)){
                    list = subRecommendedMainProductConfigs.stream().map(item->{
                        item.setIsDel(NumberConstant.ONE)
                                .setUpdateTime(LocalDateTime.now());
                        return item;
                    }).collect(Collectors.toList());
                }
                comment.add("套餐主商品由"+getProductId(mainProductIdListOld)+"修改为"+getProductId(mainProductIdListNew));
                return list;
            }
            //判断两个是否相同
            if(SubRecommendedUtils.dataComparison(mainProductIdListOld, mainProductIdListNew)){
                return list;
            } else {
                list=SubRecommendedUtils.createDataByMainProductList(mainProductIdListNew,userBO);
                comment.add("套餐主商品由"+getProductId(mainProductIdListOld)+"修改为"+getProductId(mainProductIdListNew));
            }
        }
        return list;
    }

    /**
     * 套餐门店处理
     * @param req
     * @param comment
     * @param userBO
     * @return
     */
    private List<SubRecommendedMainAreaConfig> handleSubRecommendedMainAreaConfig(RecommendedInfo req, StringJoiner comment, OaUserBO userBO){
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        Integer mainInfoId = mainInfo.getId();
        List<SubRecommendedMainAreaConfig> list = new ArrayList<SubRecommendedMainAreaConfig>();
        //新增逻辑
        List<Integer> applyAreaIdListNew = req.getApplyAreaIdList();
        if(ObjectUtil.isNull(mainInfoId)){
            if(CollectionUtil.isNotEmpty(applyAreaIdListNew)){
                list=SubRecommendedUtils.createDataByAreaIdList(applyAreaIdListNew, userBO);
               // comment.add("新增套餐适用地区"+getAreaName(applyAreaIdListNew));
            }
        } else {
            List<SubRecommendedMainAreaConfig> SubRecommendedMainAreaConfigOlds = areaConfigService.lambdaQuery().eq(SubRecommendedMainAreaConfig::getFkConfigId, mainInfoId).list();
            List<Integer> applyAreaIdListOld = SubRecommendedMainAreaConfigOlds.stream().filter(Objects::nonNull).map(SubRecommendedMainAreaConfig::getAreaId).collect(Collectors.toList());
            //判断传入进来的套餐适用地区是否为空 并且之前的老数据不为空 就是需要清楚之前的所有数据
            if(CollectionUtil.isEmpty(applyAreaIdListNew) && CollectionUtil.isNotEmpty(SubRecommendedMainAreaConfigOlds)){
                list=SubRecommendedMainAreaConfigOlds.stream().map(item->{
                    item.setIsDel(NumberConstant.ONE);
                    item.setUpdateTime(LocalDateTime.now());
                    return item;
                }).collect(Collectors.toList());
                comment.add("套餐适用地区由"+getAreaName(applyAreaIdListOld)+"修改为"+getAreaName(applyAreaIdListNew));
                return list;
            }
            //判断两个是否相同
            if(SubRecommendedUtils.dataComparison(applyAreaIdListOld, applyAreaIdListNew)){
                return list;
            } else {
                list=SubRecommendedUtils.createDataByAreaIdList(applyAreaIdListNew, userBO);
                comment.add("套餐适用地区由"+getAreaName(applyAreaIdListOld)+"修改为"+getAreaName(applyAreaIdListNew));
            }
        }
        return list;
    }

    /**
     * 地区id转换成为area
     * @param areaIdList
     * @return
     */
    private String getAreaName( List<Integer> areaIdList){
        Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(areaIdList);
        StringJoiner joiner = new StringJoiner(",");
        if(CollectionUtil.isNotEmpty(areaMap)){
            areaMap.forEach((k,v)->{
                joiner.add(v.getArea());
            });
        } else {
            return "空";
        }
        return joiner.toString();
    }


    /**
     * 获取productId
     * @param productIdList
     * @return
     */
    private String getProductId(List<Integer> productIdList){
        StringJoiner joiner = new StringJoiner(",");
        if(CollectionUtil.isNotEmpty(productIdList)){
            productIdList.forEach(item->joiner.add(item+""));
        } else {
            return "空";
        }
        return joiner.toString();
    }




    /**
     * 套餐主要信息处理
     * @param mainInfo
     */
    private SubRecommendedMainConfig handleSubRecommendedMainConfig(RecommendedInfo req,StringJoiner comment,OaUserBO userBO){
        RecommendedInfoMainInfo mainInfo = req.getMainInfo();
        if(ObjectUtil.isNull(mainInfo)){
            throw new ClassCastException("套餐主要信息不能为空");
        }
        Integer id = mainInfo.getId();
        //根据id判断是新增还是修改
        SubRecommendedMainConfig subRecommendedMainConfig;
        if(ObjectUtil.isNull(id)){
            //新增逻辑
            subRecommendedMainConfig = new SubRecommendedMainConfig();
            comment.add("添加商品套餐");
            BeanUtils.copyProperties(mainInfo,subRecommendedMainConfig);
            subRecommendedMainConfig.setCreateUser(userBO.getUserName())
                    .setCreateAreaId(userBO.getArea1id())
                    .setXtenant(XtenantEnum.getXtenant())
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());
        } else {
            //修改逻辑
            subRecommendedMainConfig = Optional.ofNullable(this.getById(id)).orElseThrow(()->new ClassCastException("套餐信息不存在 id:"+id));
            Integer isEnabledOld = Optional.ofNullable(subRecommendedMainConfig.getIsEnabled()).orElse(Integer.MAX_VALUE);
            Integer isEnabledNew = Optional.ofNullable(mainInfo.getIsEnabled()).orElseThrow(()->new ClassCastException("套餐状态不能为空"));
            if(!isEnabledOld.equals(isEnabledNew)){
                subRecommendedMainConfig.setIsEnabled(isEnabledNew);
                String isEnabledNewValue = isEnabledNew.equals(NumberConstant.ONE) ? "是" : "否";
                String isEnabledOldValue = isEnabledOld.equals(NumberConstant.ONE) ? "是" : "否";
                comment.add("是否隐藏由："+isEnabledOldValue+"修改为"+isEnabledNewValue);
            }
            //开始时间处理
            String startTimeValue = SubRecommendedUtils.dataComparison(subRecommendedMainConfig.getStartTime(), mainInfo.getStartTime(), "生效开始时间");
            if(StringUtil.isNotEmpty(startTimeValue)){
                comment.add(startTimeValue);
                subRecommendedMainConfig.setStartTime(mainInfo.getStartTime());
            }
            //结束时间处理
            String endTimeValue = SubRecommendedUtils.dataComparison(subRecommendedMainConfig.getEndTime(), mainInfo.getEndTime(), "生效结束时间");
            if(StringUtil.isNotEmpty(endTimeValue)){
                comment.add(endTimeValue);
                subRecommendedMainConfig.setEndTime(mainInfo.getEndTime());
            }

        }
        return subRecommendedMainConfig;
    }
}
