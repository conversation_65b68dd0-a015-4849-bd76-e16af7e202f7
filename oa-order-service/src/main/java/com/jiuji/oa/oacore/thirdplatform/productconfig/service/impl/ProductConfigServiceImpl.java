package com.jiuji.oa.oacore.thirdplatform.productconfig.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.jiuji.oa.oacore.cloud.WebCloud;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRException;
import com.jiuji.oa.oacore.common.util.*;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.XtenantEnum;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketinfo;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketsubinfo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.oaorder.service.RecoverMarketinfoService;
import com.jiuji.oa.oacore.oaorder.service.RecoverMarketsubinfoService;
import com.jiuji.oa.oacore.recover.po.UsedGoods;
import com.jiuji.oa.oacore.recover.service.UsedGoodsService;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.*;
import com.jiuji.oa.oacore.thirdplatform.doudian.enums.CategoryTagEnum;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.OrderRes;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.*;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.ProductConfigMapper;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.*;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.SyncMeituanStoreStock;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.request.RetailSkuPriceRequest;
import com.sankuai.meituan.shangou.open.sdk.request.RetailUpdateAppFoodCodeByNameAndSpecRequest;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import jodd.util.StringPool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商户配置接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@DS("smallpro_write")
public class ProductConfigServiceImpl extends ServiceImpl<ProductConfigMapper, ProductConfig> implements ProductConfigService {

    private static final int SIZE = 999;

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private StoreService storeService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private AmqpTemplate amqpTemplate;
    @Resource
    private SmsService smsService;
    @Resource
    private TenantService tenantService;

    @Resource
    private WebCloud webCloud;

    @CreateCache(name = "afterservice:ProductConfigService:importPoiCodeSpu:", expire = 30, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    private Cache<String, Dict> importPoiCodeCache;

    @Override
    public ProductConfig getOneProductConfigBy(String tenantCode, String platCode, String spuid, String skuid, Integer ppid) {
        QueryWrapper<ProductConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_code", tenantCode);
        queryWrapper.eq("plat_code", platCode);
        queryWrapper.eq("product_code", spuid);
        queryWrapper.eq("sku_id", skuid);
        if (null != ppid) {
            queryWrapper.eq("ppriceid", ppid);
        }
        List<ProductConfig> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    @Override
    public Page<ListProductConfigsRes> listProductConfigs(ListProductConfigsReq req) {
        Page<ListProductConfigsRes> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page.setDesc("id");
        Page<ListProductConfigsRes> result = this.baseMapper.listProductConfigs(page, req);
        return result;
    }

    @Override
    public List<ProductConfig> getProductConfigListByMkcIdList(String tenantCode, String platCode, List<Integer> mkcIdList) {
        return CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, mkcIdList, ids -> baseMapper.getProductConfigListByMkcIdList(tenantCode, platCode, ids));
    }

    /**
     * 获取小米商品
     * @param platCode
     * @return
     */
    private Map<String,List<String>> getProductMap(String platCode){
        Map<String,List<String>> map = new HashMap<>();
        List<ProductConfig> list = this.lambdaQuery().eq(ProductConfig::getPlatCode, platCode)
                .list();
        if(CollectionUtils.isEmpty(list)){
            return map;
        }
        list.stream().collect(Collectors.groupingBy(ProductConfig::getTenantCode))
                .forEach((k, v) -> map.put(k, v.stream().map(ProductConfig::getSkuId).collect(Collectors.toList())));
        return map;
    }

    /**
     * 小米商品校验
     * @param productConfigBO
     */
    private void checkMiProduct(ProductConfigAddBO productConfigBO){
        Map<String, List<String>> productMap = getProductMap(productConfigBO.getPlatCode());
        List<String> skuIdList = productMap.getOrDefault(productConfigBO.getTenantCode(), new ArrayList<>());
        List<ProductConfigAddBO.ProductConfigItemBO> products = productConfigBO.getProducts();
        //获取小米门店的商户编码
        List<Tenant> list = tenantService.lambdaQuery().eq(Tenant::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MI).eq(Tenant::getIsEnable, Boolean.TRUE).list();
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("小米可以用的启用商户为空");
        }
        List<String> tenantCodeList = list.stream().map(Tenant::getTenantCode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(products)){
            List<String> collect = products.stream().map(ProductConfigAddBO.ProductConfigItemBO::getSkuId).collect(Collectors.toList());
            Set<String> duplicateElements = collect.stream()
                    .filter(i -> collect.stream().filter(x -> x.equals(i)).count() > 1)
                    .collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(duplicateElements)){
                StringJoiner joiner = new StringJoiner(",");
                duplicateElements.forEach(joiner::add);
                throw new CustomizeException("商家KSU编码"+joiner+"重复");
            }
            for (ProductConfigAddBO.ProductConfigItemBO item: products) {
                if(skuIdList.contains(item.getSkuId())){
                    throw new CustomizeException("商家KSU编码"+item.getSkuId()+"已经存在");
                }
                if(XtenantJudgeUtil.isJiujiMore()){
                    Optional.ofNullable(item.getTenantCode()).ifPresent(code->{
                        if(!ThirdPlatformCommonConst.AGGREGATION_MI_TENANT_CODE.equals(code)){
                            throw new CustomizeException("商户编码"+code+"不可用");
                        }
                    });
                } else {
                    Optional.ofNullable(item.getTenantCode()).ifPresent(code->{
                        if(!tenantCodeList.contains(code)){
                            throw new CustomizeException("商户编码"+code+"不可用");
                        }
                    });
                }

            }
        }

    }


    /**
     * 小米商品校验
     * @param dataList
     */
    private void checkMiProduct(List<ProductConfig> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        ProductConfigAddBO productConfigAddBO = new ProductConfigAddBO();
        productConfigAddBO.setPlatCode(ThirdPlatformCommonConst.THIRD_PLAT_MI)
                .setTenantCode(dataList.get(0).getTenantCode());
        List<ProductConfigAddBO.ProductConfigItemBO> products = new ArrayList<>();
        dataList.forEach(item->{
            ProductConfigAddBO.ProductConfigItemBO productConfigItemBO = new ProductConfigAddBO.ProductConfigItemBO();
            productConfigItemBO.setSkuId(item.getSkuId());
            productConfigItemBO.setTenantCode(item.getTenantCode());
            products.add(productConfigItemBO);
        });
        productConfigAddBO.setProducts(products);
        checkMiProduct(productConfigAddBO);
    }

    @Override
    public R saveData(ProductConfigAddBO productConfigBO) {
        if (CollUtil.isEmpty(productConfigBO.getProducts())) {
            return R.error("不能为空！");
        }
        //只有小米才进行校验
        if(ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(productConfigBO.getPlatCode())){
            checkMiProduct(productConfigBO);
        }

        //判断mkcId是否有值
        if (productConfigBO.getProducts().stream().anyMatch(pr -> CommonUtil.isNotNullZero(pr.mkcId))
                && ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(productConfigBO.getPlatCode())) {
            return saveDataByMkcId(productConfigBO);
        }
        List<ProductConfig> list = new ArrayList<>();
        for (ProductConfigAddBO.ProductConfigItemBO product : productConfigBO.getProducts()) {
            //小米的判断修改
            if(ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(productConfigBO.getPlatCode())){
                if (StringUtils.isBlank(product.getSkuId()) || StringUtils.isBlank(product.getPpriceid())) {
                    continue;
                }
            } else {
                if (StringUtils.isBlank(product.getProductCode()) || StringUtils.isBlank(product.getSkuId()) || StringUtils.isBlank(product.getPpriceid())) {
                    continue;
                }
            }

            List<Integer> ppidList = CommonUtils.covertIdStr(product.getPpriceid());
            if (CollectionUtils.isEmpty(ppidList)) {
                continue;
            }
            //数据库校验重复
            for (Integer ppid : ppidList) {
                ProductConfig pc = getOneProductConfigBy(productConfigBO.getTenantCode(), productConfigBO.getPlatCode(), product.getProductCode(), product.getSkuId(), ppid);
                if (null != pc) {
                    return R.error(String.format("绑定重复，商家SKU编码%s，本地SKU编码%s", product.getSkuId(), ppid));
                }
                ProductConfig productConfig = new ProductConfig();
                productConfig.setPlatCode(productConfigBO.getPlatCode());
                productConfig.setTenantCode(productConfigBO.getTenantCode());
                productConfig.setProductCode(product.getProductCode());
                productConfig.setSkuId(product.getSkuId());
                productConfig.setPpriceid(ppid);
                productConfig.setType(NumberConstant.ZERO);
                productConfig.setLibraryLock(Boolean.FALSE);
                Productinfo productinfo = productinfoService.getProductinfoByPpid(Arrays.asList(ppid)).stream().findFirst().orElse(null);
                if(Objects.isNull(productinfo)){
                    return R.error(String.format("找不到本地SKU编码%s对应的商品", ppid));
                }
                Double priceSplit = productinfo.getMemberprice();
                productConfig.setPriceSplit(priceSplit);
                //同步系数：默认“1”
                productConfig.setSyncRatio(1D);
                //同步开关：默认“打开”
                productConfig.setSyncOff(true);
                //赠品同步开关：默认"关闭"
                productConfig.setGiftOff(false);
                //同步上限：默认“-1”
                productConfig.setSyncLimit(-1);
                //优先同步：默认“打开”
                productConfig.setSyncFirst(true);
                //计算方式：默认“相乘”
                productConfig.setSyncType(3);
                list.add(productConfig);
            }
        }
        Integer platFrom = PlatfromEnum.getCodeByName(productConfigBO.getPlatCode());
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(productConfigBO.getPlatCode())) {
            platFrom = PlatfromEnum.DY.getCode();
        }
        //查询商品的价格 并填入
        List<Integer> ppriceids = list.stream().filter(li -> CommonUtil.isNullOrZero(li.getMkcId())).map(ProductConfig::getPpriceid).collect(Collectors.toList());
        List<Productinfo> productinfoByPpid = productinfoService.getProductinfoByPpid(ppriceids);
        list.forEach(li -> productinfoByPpid.forEach(pr -> {
            if (Objects.equals(li.getPpriceid(), pr.getPpriceid())) {
                li.setPriceSplit(Optional.ofNullable(pr.getMemberprice()).orElse(0D));
            }
        }));

        //日志记录操作
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_ADD.getMessage(), MessageFormat.format("商家SKU编码：{0}/本地商品SKU：{1}",
                list.stream().map(ProductConfig::getSkuId).collect(Collectors.toList()).toString(), list.stream().map(ProductConfig::getPpriceid).collect(Collectors.toList()).toString()),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_ADD.getCode(), platFrom, list.stream().map(ProductConfig::getTenantCode).findFirst().orElse("未查询到商户"));
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            saveMyBatch(list);
        }).commit();
        return R.success("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveDataByMkcId(@NotNull ProductConfigAddBO productConfigBO) {
        //校验用户输入的skuId是否存在
        List<String> skuIdList = productConfigBO.getProducts().stream().map(ProductConfigAddBO.ProductConfigItemBO::getSkuId).distinct().collect(Collectors.toList());
        if (skuIdList.size() != productConfigBO.getProducts().size()) {
            return R.error("输出重复,请检查！");
        }
        if (CollUtil.isEmpty(skuIdList)) {
            return R.error("数据不能为空！");
        }
        //查询配置项是否存在skuId
        if (baseMapper.checkSkuIdList(skuIdList) > 0) {
            return R.error("校验不通过，SKU_ID重复！");
        }
        //校验mkcId是否被占用
        List<Integer> mkcIdList = productConfigBO.getProducts().stream().map(ProductConfigAddBO.ProductConfigItemBO::getMkcId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(mkcIdList)) {
            return R.error("mkcId为空！");
        }
        //查询当前mkcId是否存在绑定重复
        List<ProductConfig> productConfigListByMkcIdList = getProductConfigListByMkcIdList(productConfigBO.getTenantCode(), productConfigBO.getPlatCode(), mkcIdList);
        //根据mkcId查询对应的ppid
        if (CollUtil.isNotEmpty(productConfigListByMkcIdList)) {
            ProductConfig productConfig = productConfigListByMkcIdList.stream().findFirst().orElseThrow(() -> new RRException("操作异常！"));
            return R.error(String.format("绑定重复，商家SKU编码%s，mkcId编码%s", productConfig.getSkuId(), productConfig.getMkcId()));
        }
        //根据mkcId 查询ppid
        List<SearchProductInfoVO> allProductNamesByMkcId = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,
                () ->productinfoService.getProductNamesByMkcId(mkcIdList.stream().map(Object::toString)
                        .collect(Collectors.joining(",")), false));
        if (CollUtil.isEmpty(allProductNamesByMkcId)) {
            return R.error("mkcId错误, 获取不到库存信息");
        }

        List<SearchProductInfoVO> productNamesByMkcId = new LinkedList<>(allProductNamesByMkcId);
        Map<Integer, RecoverMarketsubinfo> outDouYinBasketMap = CollUtil.newHashMap(productNamesByMkcId.size());
        StringJoiner outErrMsgJoiner = new StringJoiner(StringPool.SLASH);
        handleDouYinBasket(productNamesByMkcId, outDouYinBasketMap, outErrMsgJoiner);

        List<ProductConfig> productConfiglist = new ArrayList<>();
        for (ProductConfigAddBO.ProductConfigItemBO product : productConfigBO.getProducts()) {
            for (SearchProductInfoVO searchProductInfoVO : allProductNamesByMkcId) {
                if (Objects.equals(searchProductInfoVO.getId(), product.getMkcId())) {
                    ProductConfig productConfig = new ProductConfig();
                    productConfig.setPlatCode(productConfigBO.getPlatCode());
                    productConfig.setTenantCode(productConfigBO.getTenantCode());
                    productConfig.setProductCode(product.getProductCode());
                    //skuId 数据库限制无法为null 统一设置为0 处理skuId编码大写
                    productConfig.setSkuId(product.getSkuId());
                    productConfig.setMkcId(product.getMkcId());
                    productConfig.setPpriceid(searchProductInfoVO.getPpid());
                    productConfig.setPriceSplit(0D);
                    productConfig.setSyncRatio(0D);
                    productConfig.setSyncOff(false);
                    productConfig.setSyncType(1);
                    //良品标识
                    productConfig.setType(1);
                    //锁定状态
                    productConfig.setLibraryLock(productNamesByMkcId.stream().anyMatch(pn -> ObjectUtil.equals(pn.getId(), product.getMkcId())));
                    //良品是否售出状态 1是已售出
                    productConfig.setSellType(0);
                    productConfig.setLabel(product.getLabel());
                    //记录抖音的锁定商品id
                    productConfig.setRuleCode(Optional.ofNullable(searchProductInfoVO.getToBasketId()).map(outDouYinBasketMap::get)
                            .map(RecoverMarketsubinfo::getBasketId).map(Convert::toStr).orElse(null));
                    productConfiglist.add(productConfig);
                    break;
                }
            }
        }
        List<Integer> lockMkcIds = new LinkedList<>();
        List<Integer> mkcIds = productConfiglist.stream().filter(pc -> ObjectUtil.defaultIfNull(pc.getMkcId(), 0)>0)
                .peek(pc -> {
                   if(Boolean.TRUE.equals(pc.getLibraryLock())){
                       lockMkcIds.add(pc.getMkcId());
                   }
                }).map(ProductConfig::getMkcId).collect(Collectors.toList());
        if (CollUtil.isEmpty(mkcIds)) {
            return R.error("mkcId为空, 操作失败！");
        }
        List<RecoverMkcBO> recoverMkcBOS = MultipleTransaction.query(DataSourceConstants.ERSHOU,()-> baseMapper.getPriceByMkcId(mkcIds));
        productConfiglist.forEach(li -> recoverMkcBOS.forEach(re -> {
            if (Objects.equals(li.getMkcId(), re.getMkcId())) {
                li.setPriceSplit(Optional.ofNullable(re.getSalePirce()).orElse(0D));
            }
        }));
        //日志记录操作
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_ADD.getMessage(), MessageFormat.format("商家SKU编码：{0}/本地商品SKU：{1}",
                productConfiglist.stream().map(ProductConfig::getSkuId).collect(Collectors.toList()).toString(), productConfiglist.stream().map(ProductConfig::getPpriceid).collect(Collectors.toList()).toString()),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_ADD.getCode(), PlatfromEnum.DY.getCode(), productConfiglist.stream().map(ProductConfig::getTenantCode).findFirst().orElse("未查询到商户"));
        R<Boolean> result = R.success(ObjectUtil.defaultIfBlank(outErrMsgJoiner.toString(), "操作成功"));
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            saveMyBatch(productConfiglist);
            //锁定
            int lockNum = lockMkcIds.isEmpty() ? 0 : baseMapper.lockStockBatch(lockMkcIds, NumberConstant.ONE, outDouYinBasketMap.keySet());
            result.put("lockNum", lockNum);
            if(lockNum>0){
                //锁定成功之后再推送下架消息
                updateGisGroundByMkcId(lockMkcIds, GisgroundEnum.NOT_ON_SALE.getCode());
            }

        }).commit();
        return result;
    }

    @Override
    public void handleDouYinBasket(List<SearchProductInfoVO> productNames, Map<Integer, RecoverMarketsubinfo> outDouYingBasketMap, StringJoiner outErrMsgJoiner) {
        //查询是否为抖音预占商品信息
        Map<Integer, SearchProductInfoVO> toBasketIdMap = productNames.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(SearchProductInfoVO::getToBasketId, Function.identity(), (v1, v2) -> v1));
        if(!toBasketIdMap.isEmpty()){
            List<RecoverMarketsubinfo> rmsInfos = CommonUtils.bigDataInQuery(toBasketIdMap.keySet(), ids ->
                    SpringUtil.getBean(RecoverMarketsubinfoService.class).lambdaQuery().in(RecoverMarketsubinfo::getBasketId, ids)
                    .list());
            //dou抖音手动预占订单
            List<Long> douYinSubIds = CommonUtils.bigDataPageNotParallel(100,rmsInfos.stream().map(RecoverMarketsubinfo::getSubId).collect(Collectors.toList()),
                    ids -> SpringUtil.getBean(RecoverMarketinfoService.class).lambdaQuery().in(RecoverMarketinfo::getSubId, ids)
                            .in(RecoverMarketinfo::getSubCheck, SubCheckEnum.SUB_CHECK_UNCONFIRMED.getCode(),
                                    SubCheckEnum.SUB_CHECK_CONFIRMED.getCode())
                            .eq(RecoverMarketinfo::getSubtype, SubTypeEnum.DOU_YIN.getCode())
                            .le(RecoverMarketinfo::getYifuM, 0).select(RecoverMarketinfo::getSubId).list())
                    .stream().flatMap(List::stream).map(RecoverMarketinfo::getSubId).collect(Collectors.toList());
            for (RecoverMarketsubinfo rmsInfo : rmsInfos) {
                if(douYinSubIds.contains(rmsInfo.getSubId())){
                    outDouYingBasketMap.put(rmsInfo.getBasketId(), rmsInfo);
                }else{
                    SearchProductInfoVO searchProductInfoVO = toBasketIdMap.get(rmsInfo.getBasketId());
                    productNames.remove(searchProductInfoVO);
                    outErrMsgJoiner.add(StrUtil.format("mkcId[{}]已售卖", searchProductInfoVO.getId()));
                }
            }
        }
    }

    /**
     * 批量保存
     *
     * @param list
     */
    private void saveMyBatch(List<ProductConfig> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<List<ProductConfig>> listList = ListUtils.partition(list, 100);
            for (List<ProductConfig> subList : listList) {
                baseMapper.saveBatch(subList);
            }
        }
    }

    @Override
    public Page<ProductConfigVO> listByPage(ProductConfigSearchBO search) {
        if (StrUtil.isEmpty(search.getPlatCode())) {
            //默认查询美团
            search.setPlatCode(ThirdPlatformCommonConst.THIRD_PLAT_MT);
            search.setType(NumberConstant.ZERO);
        }
        Page<ProductConfigVO> page = new Page<>(search.getCurrent(), search.getSize());
        List<ProductConfigVO> list = baseMapper.productConfigList(search, page);
        //查询抖音的良品是否已售出
        if (Objects.equals(search.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY) && Objects.equals(search.getType(), NumberConstant.ONE)) {
            List<Integer> mkcIdList = list.stream().map(ProductConfigVO::getMkcId).filter(Objects::nonNull).collect(Collectors.toList());
            List<SearchProductInfoVO> productNamesByMkcId = productinfoService.getProductNamesByMkcId(mkcIdList.stream()
                    .map(Object::toString).collect(Collectors.joining(",")), false);
            //设置商品是否已售
            Map<Integer, SearchProductInfoVO> mkcIdMap = productNamesByMkcId.stream()
                    .collect(Collectors.toMap(spi -> spi.getId(), Function.identity(), (v1,v2) -> v1));
            //如果toBasketId不为空且与配置中的锁定库存的basketId不相等,即为已销售
            list.stream().filter(li -> {
                SearchProductInfoVO spi = mkcIdMap.get(li.getMkcId());
                return spi !=null && spi.getToBasketId() != null && !(StrUtil.isNotBlank(li.getRuleCode()) && ObjectUtil.equals(Convert.toStr(spi.getToBasketId()), li.getRuleCode()));
            }).forEach(li -> li.setSellType(NumberConstant.ONE));

            //查询良品商品id
            Map<Integer, Integer> salfGoodMap = CommonUtils.bigDataInQuery(mkcIdList,
                    ids -> MultipleTransaction.query(DataSourceConstants.ERSHOU, () -> SpringUtil.getBean(UsedGoodsService.class)
                            .lambdaQuery().in(UsedGoods::getMkcId, ids).eq(UsedGoods::getIsdelete, 0)
                            .orderByDesc(UsedGoods::getId).select(UsedGoods::getId, UsedGoods::getMkcId).list()))
                    .stream().collect(Collectors.toMap(UsedGoods::getMkcId, UsedGoods::getId, (v1, v2) -> v1));
            list.forEach(pc -> pc.setSalfGoodsId(salfGoodMap.get(pc.getMkcId())));
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(li -> li.setQueName(EnumUtil.getMessageByCode(QueEnum.class, li.getQue())));
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public Page<ProductConfigVOV2> listByPageV2(ProductConfigSearchBOV2 search) {
        Page<ProductConfigVOV2> page = new Page<>(search.getCurrent(), search.getSize());
        List<ProductConfigVOV2> list = baseMapper.productConfigListV2(search, page);
        if (CollectionUtils.isNotEmpty(list)) {
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public List<ProductConfigVO> selectListWithStock(String storeCode) {
        return baseMapper.selectListWithStock(storeCode);
    }

    @Override
    public List<ProductConfigVO> selectListWithStockByJiuJi(String storeCode) {
        Store mt = storeService.getOneStoreByStoreCode("MT", storeCode);
        //如果没有库存关联门店 则查询库存的商品列表
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(mt.getAssociatedStores())) {
            return baseMapper.selectListWithStock(storeCode);
        }
        List<String> areas = CommonUtil.toStrList(mt.getAssociatedStores());
        List<AreaInfo> listR = areaInfoClient.listAreaInfoByAreas(areas).getData();
        List<Integer> collect = listR.stream().map(AreaInfo::getId).collect(Collectors.toList());
        collect.add(mt.getAreaId());
        //库存关联门店和门店 的库存总和
        List<ProductLeftCountBO> productLeftCountList = baseMapper.selectListWithStockByJiuJi(storeCode, collect, null,mt.getAreaId());
        if (CollUtil.isNotEmpty(productLeftCountList)) {
            List<ProductConfigVO> productConfigs = baseMapper.productConfigListByIds(productLeftCountList);
            productConfigs.forEach(p -> productLeftCountList.forEach(po -> {
                if (Objects.equals(p.getId(), po.getId())) {
                    p.setLeftCount(po.getLeftCount());
                }
            }));
            return productConfigs;
        }
        return new ArrayList<>();
    }


    @Override
    public List<ProductConfigVO> selectListWithStockV1(List<String> storeCode) {
        List<ProductConfigVO> result = new ArrayList<>();
        //查询所有的商品集合
        List<ProductConfigVO> productConfigs = baseMapper.productConfigListVo("MT", null, null);
        //分页查询 一次查1000条数据
        List<ProductConfigVO> productConfigVo = CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, storeCode, ids -> baseMapper.selectListWithStockV1(ids, "MT", null));
        //更据门店进行分组
        for (Map.Entry<String, List<ProductConfigVO>> stringListEntry : productConfigVo.stream().collect(Collectors.groupingBy(ProductConfigVO::getStoreCode)).entrySet()) {
            copyProductToStore(result, productConfigs, stringListEntry, Collections.emptyMap());
        }
        return new ArrayList<>();
    }

    private static void copyProductToStore(Collection<ProductConfigVO> result, List<ProductConfigVO> productConfigs,
                                           Map.Entry<String, List<ProductConfigVO>> stringListEntry,
                                           Map<String, SyncMeituanStoreStock> syncMeituanStoreStockMap) {


        ProductConfigVO product;
        String storeC = stringListEntry.getKey();
        List<Integer> storePpids = Optional.ofNullable(syncMeituanStoreStockMap.get(storeC))
                .map(SyncMeituanStoreStock::getPpids).orElse(Collections.emptyList());
        for (ProductConfigVO productConfig : productConfigs) {
            boolean isContinue =
                    // 没在门店的ppid中跳过
                    !storePpids.isEmpty() && !storePpids.contains(productConfig.getPpriceid())
                            // 跳过不同商户配置
                            || stringListEntry.getValue().stream().findFirst()
                            .filter(pc -> ObjectUtil.notEqual(productConfig.getTenantCode(), pc.getTenantCode()))
                            .isPresent();
            if (isContinue) {
                continue;
            }
            product = new ProductConfigVO();
            BeanUtils.copyProperties(productConfig, product);
            //默认库存设置为0
            product.setLeftCount(NumberConstant.ZERO);
            product.setStoreCode(storeC);
            ProductConfigVO finalProduct = product;
            stringListEntry.getValue().forEach(po -> {
                if (Objects.equals(finalProduct.getId(), po.getId())) {
                    //库存小于0的时候 手动填充为0
                    finalProduct.setLeftCount(DecideUtil.iif(po.getLeftCount() < 0, NumberConstant.ZERO, po.getLeftCount()));
                }
            });
            result.add(product);
        }
    }

    @Override
    public Collection<ProductConfigVO> selectListWithStockByJiuJiV1(String platCodeParam, List<String> storeCode, List<Integer> ppids,
                                                                    Map<String, SyncMeituanStoreStock> syncMeituanStoreStockMap) {
        Collection<ProductConfigVO> result = new ConcurrentLinkedQueue<>();
        String platCode = Optional.ofNullable(platCodeParam).filter(StrUtil::isNotBlank).orElse(ThirdPlatformCommonConst.THIRD_PLAT_MI);
        log.warn("平台[{}]开始获取门店数据", platCode);
        List<Store> storeList = storeService.getOneStoreByStoreCodeV1(platCode, storeCode);
        if (CollUtil.isEmpty(storeList)) {
            return new ArrayList<>();
        }
        Map<String, SyncMeituanStoreStock> storePpidsMap = CollUtil.isEmpty(syncMeituanStoreStockMap) ? Collections.emptyMap() : syncMeituanStoreStockMap;
        //库存关联门店的平台门店
        List<String> existAssoStores = storeList.stream().filter(store -> StringUtils.isNotEmpty(store.getAssociatedStores())).map(Store::getStoreCode)
                .distinct().collect(Collectors.toList());
        //获取差集(没有关联库存门店的门店)
        List<String> noAssoStores = (List<String>) CollUtil.disjunction(existAssoStores, storeCode);
        List<ProductConfigVO> noAssoStoreProductConfigVOList = Collections.emptyList();
                //分页查询 一次查1000条数据,获取没有关联门店的商品配置
        if(CollUtil.isNotEmpty(noAssoStores)){
            noAssoStoreProductConfigVOList = Optional.ofNullable(ppids).filter(CollUtil::isNotEmpty)
                    // 非空走分页查询
                    .map(pids -> CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED, pids, ids -> baseMapper.selectListWithStockV1(noAssoStores,platCode,ids)))
                    // 否则直接获取
                    .orElseGet(() -> baseMapper.selectListWithStockV1(noAssoStores, platCode, ppids));
        }
        //更据无关联门店的门店进行分组
        Map<String, List<ProductConfigVO>> productConfigMap = noAssoStoreProductConfigVOList.stream().collect(Collectors.groupingBy(ProductConfigVO::getStoreCode));
        //查询所有的商品集合
        List<String> tenantCodes = storeList.stream().map(Store::getTenantCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ProductConfigVO> productConfigs = baseMapper.productConfigListVo(platCode, ppids, tenantCodes);
        for (Map.Entry<String, List<ProductConfigVO>> stringListEntry : productConfigMap.entrySet()) {
            copyProductToStore(result, productConfigs, stringListEntry, storePpidsMap);
        }
        log.warn("获取到{}商品配置数量: {}", platCode, productConfigs.size());
        Collection<ProductLeftCountBO> productLeftCountList = new ConcurrentLinkedQueue<>();
        //调整为批量查询门店信息
        List<String> assAreas = storeList.stream().map(Store::getAssociatedStores).filter(StrUtil::isNotBlank).flatMap(ass -> StrUtil.splitTrim(ass, StringPool.COMMA).stream())
                .distinct().collect(Collectors.toList());
        List<AreaInfo> areaInfos = CommonUtils.bigDataInQuery(assAreas, areas -> areaInfoClient.listAreaInfoByAreas(areas).getData());
        log.warn("{}库存同步,存在关联门店数量: {}, 总门店数: {}", platCode, existAssoStores.size(), storeList.size());
        existAssoStores.stream().forEach(s -> {
            for (Store store : storeList) {
                if (Objects.equals(store.getStoreCode(), s)) {
                    List<String> areas = CommonUtil.toStrList(store.getAssociatedStores());
                    List<AreaInfo> listR = areaInfos.stream().filter(ai -> areas.contains(ai.getArea())).collect(Collectors.toList());
                    List<Integer> areaIds = listR.stream().map(AreaInfo::getId).collect(Collectors.toList());
                    areaIds.add(store.getAreaId());
                    List<Integer> storePpids = Optional.ofNullable(storePpidsMap.get(s)).map(SyncMeituanStoreStock::getPpids).orElse(ppids);
                    List<ProductLeftCountBO> plcList;
                    if(storePpids.isEmpty()){
                        plcList = baseMapper.selectListWithStockByJiuJi(s, areaIds, storePpids,store.getAreaId());
                    }else{
                        plcList = CommonUtils.bigDataPageNotParallel(500, storePpids, ids ->baseMapper.selectListWithStockByJiuJi(s, areaIds, ids,store.getAreaId()))
                                .stream().flatMap(plcs -> plcs.stream()).collect(Collectors.toList());
                    }
                    productLeftCountList.addAll(plcList);
                    break;
                }
            }
        });
        log.warn("{}同步库存数: {}", platCode, productLeftCountList.size());
        if (CollUtil.isNotEmpty(productLeftCountList)) {
            productLeftCountList.parallelStream().collect(Collectors.groupingBy(ProductLeftCountBO::getStoreCode))
                    .entrySet().parallelStream()
                    .forEach(stringListEntry -> {
                        String storeC = stringListEntry.getKey();
                        List<Integer> storePpids = Optional.ofNullable(storePpidsMap.get(storeC))
                                .map(SyncMeituanStoreStock::getPpids).orElse(Collections.emptyList());
                        for (ProductConfigVO productConfig : productConfigs) {
                            boolean isContinue =
                                    // 没在门店的ppid中跳过
                                    !storePpids.isEmpty() && !storePpids.contains(productConfig.getPpriceid())
                                    // 跳过不同商户配置
                                    || stringListEntry.getValue().stream().findFirst()
                                    .filter(pc -> ObjectUtil.notEqual(productConfig.getTenantCode(), pc.getTenantCode()))
                                    .isPresent();
                            if (isContinue) {
                                continue;
                            }
                            ProductConfigVO product = new ProductConfigVO();
                            BeanUtils.copyProperties(productConfig, product);
                            //默认库存设置为0
                            product.setLeftCount(NumberConstant.ZERO);
                            product.setStoreCode(storeC);
                            ProductConfigVO finalProduct = product;
                            stringListEntry.getValue().forEach(po -> {
                                if (Objects.equals(finalProduct.getId(), po.getId())) {
                                    //库存小于0的时候 手动填充为0
                                    finalProduct.setLeftCount(DecideUtil.iif(po.getLeftCount() < 0, NumberConstant.ZERO, po.getLeftCount()));
                                }
                            });
                            result.add(product);
                        }
                    });
        }
        log.warn("{}需要同步库存数: {}", platCode, result.size());
        return result;
    }

    @Override
    public List<ProductConfigVO> selectListWithStockV2(List<String> storeCode, String tenantCode) {
        List<ProductConfigVO> result = new ArrayList<>();
        List<Store> storeList = storeService.getOneStoreByStoreCodeV1("DY", storeCode);
        if (CollUtil.isEmpty(storeList)) {
            return new ArrayList<>();
        }
        //库存关联门店的门店
        List<String> collect = storeList.stream().filter(store -> StringUtils.isNotEmpty(store.getAssociatedStores())).map(Store::getStoreCode).collect(Collectors.toList());
        //获取差集
        List<String> disjunction = (List<String>) CollUtil.disjunction(collect, storeCode);
        //分页查询 一次查1000条数据
        List<ProductConfigVO> productConfigVOList = CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, disjunction, ids -> baseMapper.selectListWithStockV1(ids, "DY", null));
        //更据门店进行分组
        Map<String, List<ProductConfigVO>> productConfigMap = productConfigVOList.stream().collect(Collectors.groupingBy(ProductConfigVO::getStoreCode));
        //查询所有的商品集合
        List<String> tenantCodes = storeList.stream().map(Store::getTenantCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ProductConfigVO> productConfigs = baseMapper.productConfigListVo("DY", null, tenantCodes);
        for (Map.Entry<String, List<ProductConfigVO>> stringListEntry : productConfigMap.entrySet()) {
            copyProductToStore(result, productConfigs, stringListEntry, Collections.emptyMap());
        }
        //查询出
        List<Integer> areaIds;
        List<ProductLeftCountBO> productLeftCountList = new ArrayList<>();
        for (String s : collect) {
            for (Store store : storeList) {
                if (Objects.equals(store.getStoreCode(), s)) {
                    List<String> areas = CommonUtil.toStrList(store.getAssociatedStores());
                    List<AreaInfo> listR = areaInfoClient.listAreaInfoByAreas(areas).getData();
                    areaIds = listR.stream().map(AreaInfo::getId).collect(Collectors.toList());
                    areaIds.add(store.getAreaId());
                    productLeftCountList.addAll(baseMapper.selectListWithStockByJiuJi(s, areaIds, null,store.getAreaId()));
                }
            }
        }
        if (CollUtil.isNotEmpty(productLeftCountList)) {
            ProductConfigVO product;
            for (Map.Entry<String, List<ProductLeftCountBO>> stringListEntry : productLeftCountList.stream().collect(Collectors.groupingBy(ProductLeftCountBO::getStoreCode)).entrySet()) {
                for (ProductConfigVO productConfig : productConfigs) {
                    product = new ProductConfigVO();
                    BeanUtils.copyProperties(productConfig, product);
                    //默认库存设置为0
                    product.setLeftCount(NumberConstant.ZERO);
                    product.setStoreCode(stringListEntry.getKey());
                    ProductConfigVO finalProduct = product;
                    stringListEntry.getValue().forEach(po -> {
                        if (Objects.equals(finalProduct.getId(), po.getId())) {
                            //库存小于0的时候 手动填充为0
                            finalProduct.setLeftCount(DecideUtil.iif(po.getLeftCount() < 0, NumberConstant.ZERO, po.getLeftCount()));
                        }
                    });
                    result.add(product);
                }
            }
        }
        return result;
    }

    @Override
    public List<ProductConfig> getOneProductConfigBySkuId(String platCode, String tenantCode, String productCode, String skuId) {
        QueryWrapper<ProductConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_code", tenantCode);
        queryWrapper.eq("plat_code", platCode);
        //抖音小时达，不需要product_code条件
        if (!Objects.equals(ThirdPlatformCommonConst.THIRD_PLAT_DY,platCode) || !Objects.equals(AppTypeEnum.MALL_HOURS.getCode(), Optional.ofNullable(tenantService.getOneTenantBy(tenantCode, platCode)).map(Tenant::getAppType).orElse(0))) {
            queryWrapper.eq("product_code", productCode);
        }
        queryWrapper.eq("sku_id", skuId);
        List<ProductConfig> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list;
    }

    @Override
    public Boolean syncCost(ProductConfigCostReq req) {
        String tenantCode = req.getTenantCode();
        List<Integer> productConfigIdList = req.getProductConfigId();
        List<Store> storeList = storeService.lambdaQuery().in(Store::getTenantCode, tenantCode).list();

        List<ProductConfig> productConfigList;
        if (CollectionUtils.isNotEmpty(productConfigIdList)){
            productConfigList = this.lambdaQuery()
                    .in(ProductConfig::getId, productConfigIdList)
                    .isNotNull(ProductConfig::getPlatformCost)
                    .list();
        }else {
            productConfigList = this.lambdaQuery()
                    .isNotNull(ProductConfig::getPlatformCost)
                    .list();
        }
        if (CollectionUtils.isEmpty(productConfigIdList)){
            throw new CustomizeException("没有需要同步平台价格的商品配置");
        }

        //组建请求参数,如有其它参数请补充完整
        Tenant oneTenantBy = SpringUtil.getBean(TenantService.class).getOneTenantBy(tenantCode, ThirdPlatformCommonConst.THIRD_PLAT_MT);
        String meiTuanAppId = oneTenantBy.getTenantCode();
        String meiTuanAppSecret = oneTenantBy.getAppSecret();
        SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
        for (Store store : storeList) {
            RetailSkuPriceRequest retailSkuPriceRequest = new RetailSkuPriceRequest(systemParam);
            retailSkuPriceRequest.setApp_poi_code(store.getStoreCode());
            List<ProductConfigCostSyncVO> foodDataList = new ArrayList<>();
            for (ProductConfig productConfig : productConfigList) {
                ProductConfigCostSyncVO foodData = new ProductConfigCostSyncVO();
                foodData.setAppSpuCode(productConfig.getProductCode());
                List<ProductConfigCostSyncVO.ProductConfigCostSyncData> skus = new ArrayList<>();
                ProductConfigCostSyncVO.ProductConfigCostSyncData sku = new ProductConfigCostSyncVO.ProductConfigCostSyncData();
                sku.setSkuId(productConfig.getSkuId());
                sku.setPrice(Convert.toStr(productConfig.getPlatformCost()));
                skus.add(sku);
                foodData.setSkus(skus);
                foodDataList.add(foodData);
            }
            retailSkuPriceRequest.setFood_data(JSONUtil.toJsonStr(foodDataList));
            SgOpenResponse sgOpenResponse;
            try {
                sgOpenResponse = retailSkuPriceRequest.doRequest();
                log.info(AddLogKind.SYNCHRONIZATION_PLATFORM_COST + "调用结果:" + JSONUtil.toJsonStr(sgOpenResponse));
            } catch (Exception e) {
                smsService.sendOaMsgTo9Ji(AddLogKind.SYNCHRONIZATION_PLATFORM_COST + "操作异常", "13774", OaMesTypeEnum.YCTZ.getCode().toString());
                log.error(AddLogKind.SYNCHRONIZATION_PLATFORM_COST + "操作异常", e);
            }
        }

        return true;
    }

    @Override
    public R<Map<String, List<EnumVO>>> getEnums() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        enumMap.put("labelList", com.jiuji.tc.utils.enums.EnumUtil.toEnumVOList(CategoryTagEnum.class));
        return R.success(enumMap);
    }


    @Override
    public R<Boolean> uploadData(MultipartFile file, String platCode) {
        List<ProductConfigImportBO> sourceImportList = readProductConfigImportFromExcel(file);
        if (CollectionUtils.isEmpty(sourceImportList)) {
            return R.error("数据不能为空");
        }
        List<ProductConfigImportBO> newImportList = new ArrayList<>();
        for (ProductConfigImportBO temp : sourceImportList) {
            String tenantCode = temp.getTenantCode();
            String productCode = temp.getProductCode();
            String skuId = temp.getSkuId();
            Integer ppriceid = temp.getPpriceid();
            if (StringUtils.isEmpty(tenantCode)
                    || StringUtils.isEmpty(productCode)
                    || StringUtils.isEmpty(skuId)
                    || Objects.isNull(ppriceid)) {
                newImportList.add(temp);
                continue;
            }
            ProductConfig productConfig = this.lambdaQuery().eq(ProductConfig::getTenantCode, tenantCode)
                    .eq(ProductConfig::getProductCode, productCode)
                    .eq(ProductConfig::getSkuId, skuId)
                    .eq(ProductConfig::getPpriceid, ppriceid)
                    .list().stream().findFirst().orElse(null);
            if (Objects.nonNull(productConfig)) {
                if (Objects.nonNull(temp.getPriceSplit())) {
                    Productinfo productinfo = productinfoService.getProductinfoByPpid(Collections.singletonList(temp.getPpriceid())).stream()
                            .findFirst().orElse(null);
                    if (Objects.isNull(productinfo)) {
                        throw new CustomizeException("找不到所配置的ppid：" + temp.getPpriceid());
                    }
                    if (temp.getPriceSplit().compareTo(productinfo.getMemberprice()) != 0) {
                        throw new CustomizeException("价格分摊金额和本地商品会员价格不一致");
                    }
                    productConfig.setPriceSplit(temp.getPriceSplit());
                }
                if (Objects.nonNull(temp.getSyncOff())) {
                    productConfig.setSyncOff(Convert.toBool(temp.getSyncOff()));
                }
                if (Objects.nonNull(temp.getSyncType())) {
                    productConfig.setSyncType(temp.getSyncType());
                }
                if (Objects.nonNull(temp.getSyncRatio())) {
                    productConfig.setSyncRatio(temp.getSyncRatio());
                }
                if (Objects.nonNull(temp.getSyncLimit())) {
                    productConfig.setSyncLimit(temp.getSyncLimit());
                }
                if (Objects.nonNull(temp.getSyncFirst())) {
                    productConfig.setSyncFirst(Convert.toBool(temp.getSyncFirst()));
                }
                if (Objects.nonNull(temp.getLabel())) {
                    productConfig.setLabel(temp.getLabel());
                }
                productConfig.setProductCode(productCode);
                this.updateById(productConfig);
            } else {
                newImportList.add(temp);
            }
        }

        List<ProductConfig> dataList = newImportList.stream().map(bo -> {
            ProductConfig obj = new ProductConfig();
            if(ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(platCode)){
                obj.setSkuId(bo.getProductCode());
                obj.setProductCode(bo.getProductCode());
                obj.setPpriceid(Integer.parseInt(bo.getSkuId()));
                obj.setTenantCode(bo.getTenantCode());
            } else {
                BeanUtils.copyProperties(bo, obj);
            }

            if (bo.getSyncFirst() == null && !ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(platCode)) {
                throw new CustomizeException("导入数据为空");
            }
            if (ThirdPlatformCommonConst.COMMON_INTEGER_TRUE.equals(bo.getSyncFirst())) {
                obj.setSyncFirst(true);
            } else {
                obj.setSyncFirst(false);
            }
            if (ThirdPlatformCommonConst.COMMON_INTEGER_TRUE.equals(bo.getSyncOff()) ) {
                obj.setSyncOff(true);
            } else {
                obj.setSyncOff(false);
            }
            obj.setLibraryLock(false);
            obj.setPlatCode(platCode);
            obj.setType(NumberConstant.ZERO);
            return obj;
        }).collect(Collectors.toList());
        //数据过滤
        dataList = dataList.stream().filter(obj -> {
            boolean isBlank = StringUtils.isAnyBlank(obj.getTenantCode(), obj.getProductCode(), obj.getSkuId()) || null == obj.getPpriceid();
            if (isBlank) {
                log.info("导入数据存在空值");
                return false;
            }
//            ProductConfig pc = getOneProductConfigBy(obj.getTenantCode(), obj.getPlatCode(), obj.getProductCode(), obj.getSkuId(), obj.getPpriceid());
//            Tenant tenant = tenantService.getOneTenantBy(obj.getTenantCode(), obj.getPlatCode());
//            if (null == tenant || null != pc) {
//                return false;
//            }
            // 上面循环调用数据库需要优化
            if (ObjectUtil.isNull(obj.getPriceSplit())) {
                obj.setPriceSplit(0D);
            } else {
                Productinfo productinfo = productinfoService.getProductinfoByPpid(Collections.singletonList(obj.getPpriceid())).stream()
                        .findFirst().orElse(null);
                if (Objects.isNull(productinfo)) {
                    throw new CustomizeException("找不到所配置的ppid：" + obj.getPpriceid());
                }
                if (obj.getPriceSplit().compareTo(productinfo.getMemberprice()) != 0) {
                    throw new CustomizeException("价格分摊金额和本地商品会员价格不一致");
                }
            }
            if (ObjectUtil.isNull(obj.getSyncRatio())) {
                obj.setSyncRatio(0D);
            }
            if (ObjectUtil.isNull(obj.getSyncType())) {
                obj.setSyncType(1);
            }
            return true;
        }).collect(Collectors.toList());

        //只有小米才进行校验
        if(ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(platCode)){
            checkMiProduct(dataList);
        }
        //日志记录操作
        Integer platFrom = PlatfromEnum.getCodeByName(platCode);
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_IMPORT.getMessage(), MessageFormat.format("导入{0}条数据", dataList.size()),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_IMPORT.getCode(), platFrom , dataList.stream().map(ProductConfig::getTenantCode).findFirst().orElse("未查询到商户"));
        //开启事务 切换写库
        List<ProductConfig> finalDataList = dataList;
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            saveMyBatch(finalDataList);
        }).commit();
        return R.success(String.format("导入成功(%d条数据)", dataList.size()), true);
    }

    @SneakyThrows
    private List<ProductConfigImportBO> readProductConfigImportFromExcel(MultipartFile file) {
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
        return reader.read().stream().skip(NumberConstant.ONE)
                .map(row -> LambdaBuild.create(ProductConfigImportBO.class)
                        .set(ProductConfigImportBO::setTenantCode, Convert.toStr(CollUtil.get(row, NumberConstant.ZERO)))
                        .set(ProductConfigImportBO::setProductCode, Convert.toStr(CollUtil.get(row, NumberConstant.ONE)))
                        .set(ProductConfigImportBO::setSkuId, Convert.toStr(CollUtil.get(row, NumberConstant.TWO)))
                        .set(ProductConfigImportBO::setPpriceid, Convert.toInt(CollUtil.get(row, NumberConstant.THREE)))
                        .set(ProductConfigImportBO::setPriceSplit, Convert.toDouble(CollUtil.get(row, NumberConstant.FOUR)))
                        .set(ProductConfigImportBO::setSyncOff, Convert.toInt(CollUtil.get(row, NumberConstant.FIVE)))
                        .set(ProductConfigImportBO::setSyncType, Convert.toInt(CollUtil.get(row, NumberConstant.SIX)))
                        .set(ProductConfigImportBO::setSyncRatio, Convert.toDouble(CollUtil.get(row, NumberConstant.SEVEN)))
                        .set(ProductConfigImportBO::setSyncLimit, Convert.toInt(CollUtil.get(row, NumberConstant.EIGHT)))
                        .set(ProductConfigImportBO::setSyncFirst, Convert.toInt(CollUtil.get(row, NumberConstant.NINE)))
                        .set(ProductConfigImportBO::setLabel, Convert.toInt(CollUtil.get(row, NumberConstant.TEN)))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @DS("smallpro_write")
    public R<Boolean> clearData(String tenant, String platCode) {
        //日志记录操作
        Integer platFrom = PlatfromEnum.getCodeByName(platCode);
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)) {
            platFrom = PlatfromEnum.DY.getCode();
        }
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_DELETE.getMessage(), "清空所有商品配置数据",
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_DELETE.getCode(), platFrom, tenant);
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            remove(new LambdaQueryWrapper<ProductConfig>().eq(ProductConfig::getTenantCode, tenant));
        }).commit();
        return R.success("操作成功");
    }


    @Override
    @DS("smallpro_write")
    public R<Boolean> clearDataV2(ProductConfigClearReq req) {
        String tenantCode = req.getTenantCode();
        Tenant tenant = tenantService.lambdaQuery().eq(Tenant::getTenantCode, tenantCode).list().stream().findFirst().orElse(null);
        if (!ThirdPlatformCommonConst.AGGREGATION_MI_TENANT_CODE.equals(tenantCode) && Objects.isNull(tenant)) {
            throw new CustomizeException("找不到商户配置:" + tenantCode);
        }
        String platCode;
        if(ThirdPlatformCommonConst.AGGREGATION_MI_TENANT_CODE.equals(tenantCode)){
            platCode = ThirdPlatformCommonConst.THIRD_PLAT_MI;
        } else {
            platCode = tenant.getPlatCode();
        }

        String skuid = req.getSkuid();
        Integer clearType = req.getClearType();
        if (Objects.isNull(clearType)) {
            clearType = ProductConfigClearTypeEnum.PPID.getCode();
        }

        ProductConfigClearTypeEnum productConfigClearTypeEnum = ProductConfigClearTypeEnum.getBusinessNodeByCode(clearType);
        if (Objects.isNull(productConfigClearTypeEnum)) {
            throw new CustomizeException("清除类型不正确");
        }
        if (StringUtils.isEmpty(skuid)) {
            String result = null;
            switch (productConfigClearTypeEnum) {
                case PPID:
                    result = "请输入需要删除的商品sku";
                    if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)) {
                        result = "请输入需要删除的ppid";
                    }
                    break;
                case SKU:
                    result = "请输入需要删除的商家sku编码";
                    break;
                case SPU:
                    result = "请输入需要删除的商家spu编码";
                    break;
                case MKC:
                    result = "请输入需要删除的mkcid";
                    break;
                default:
            }
            throw new CustomizeException(result);
        }
        skuid = skuid.replaceAll("\\n", StringPool.COMMA);
        List<String> skuidList = Arrays.asList(skuid.split(StringPool.COMMA));
        if (skuidList.size() > SIZE) {
            throw new CustomizeException("最多支持输入" + SIZE + "个");
        }
        switch (productConfigClearTypeEnum) {
            case PPID:
                for (String x : skuidList) {
                    try {
                        Integer.parseInt(x);
                    } catch (Exception e) {
                        throw new CustomizeException("请输入正确的ppid:" + x);
                    }
                }
                break;
            case SKU:
                break;
            case SPU:
                break;
            case MKC:
                break;
            default:
        }
        List<String> existList = new ArrayList<>();
        List<String> existSkuidList = new ArrayList<>();
        Collection<String> notExistSkuidList = new ArrayList<>();
        List<ProductConfig> productConfigList = new ArrayList<>();
        switch (productConfigClearTypeEnum) {
            case PPID:
                productConfigList = this.lambdaQuery()
                        .eq(ProductConfig::getTenantCode, tenantCode).in(ProductConfig::getPpriceid, skuidList).list();
                existList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getPpriceid()))
                        .distinct().collect(Collectors.toList());
                notExistSkuidList = CollectionUtils.subtract(skuidList, existList);
                existSkuidList = existList;

                break;
            case SKU:
                productConfigList = this.lambdaQuery()
                        .eq(ProductConfig::getTenantCode, tenantCode).in(ProductConfig::getSkuId, skuidList).list();
                existList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getSkuId()))
                        .distinct().collect(Collectors.toList());
                notExistSkuidList = CollectionUtils.subtract(skuidList, existList);
                existSkuidList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getPpriceid()))
                        .distinct().collect(Collectors.toList());
                break;
            case SPU:
                productConfigList = this.lambdaQuery()
                        .eq(ProductConfig::getTenantCode, tenantCode).in(ProductConfig::getProductCode, skuidList).list();
                existList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getProductCode()))
                        .distinct().collect(Collectors.toList());
                notExistSkuidList = CollectionUtils.subtract(skuidList, existList);
                existSkuidList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getPpriceid()))
                        .distinct().collect(Collectors.toList());
                break;
            case MKC:
                productConfigList = this.lambdaQuery()
                        .eq(ProductConfig::getTenantCode, tenantCode).in(ProductConfig::getMkcId, skuidList).list();
                existList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getMkcId()))
                        .distinct().collect(Collectors.toList());
                notExistSkuidList = CollectionUtils.subtract(skuidList, existList);
                existSkuidList = productConfigList
                        .stream().map((ProductConfig ProductConfig) -> String.valueOf(ProductConfig.getPpriceid()))
                        .distinct().collect(Collectors.toList());
                break;
            default:
        }
        String message = productConfigClearTypeEnum.getMessage();
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)
                && ProductConfigClearTypeEnum.PPID.getCode().equals(clearType)) {
            message = "ppid";
        }
        String result = "成功删除" + existSkuidList.size() + "个" + message + "。";
        if (CollectionUtils.isNotEmpty(notExistSkuidList)) {
            result += message + "：" + CollectionUtil.join(notExistSkuidList, StringPool.COMMA) + "无匹配数据，删除失败，请核对。";
        }
        if (CollectionUtils.isEmpty(existSkuidList)) {
            return R.error(result);
        }
        //日志记录操作
        Integer platFrom = PlatfromEnum.getCodeByName(platCode);
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)) {
            platFrom = PlatfromEnum.DY.getCode();
        }
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_DELETE.getMessage(), "清空部分商品配置数据:" + CollectionUtil.join(existSkuidList, StringPool.COMMA),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_DELETE.getCode(), platFrom, tenantCode);
        List<Integer> mkcUnlockList = productConfigList.stream()
                .filter(p -> ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)
                        && Objects.nonNull(p.getMkcId())
                        && CommonUtil.isNotNullZero(p.getType()) && Boolean.TRUE.equals(p.getLibraryLock()))
                .map(ProductConfig::getMkcId)
                .collect(Collectors.toList());
        List<Integer> idList = productConfigList.stream().map(ProductConfig::getId).collect(Collectors.toList());
        //开启事务 切换写库

        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            // 检查并解锁库存
            if(CollUtil.isNotEmpty(mkcUnlockList)){
                int unLockNum = baseMapper.lockStockBatch(mkcUnlockList, NumberConstant.ZERO, null);
                if(unLockNum < mkcUnlockList.size()){
                    throw new CustomizeException("良品库存解锁失败");
                }
            }
            // 删除商品配置
            remove(new LambdaQueryWrapper<ProductConfig>().eq(ProductConfig::getTenantCode, tenantCode).in(ProductConfig::getId, idList));

        }).commit();
        if (CollectionUtils.isNotEmpty(notExistSkuidList)) {
            return R.error(result);
        }
        return R.success(result);
    }

    /**
     * 数据校验
     */
    private void checkData(ProductConfigAddBOV2 productConfigBO){
        //九机的校验
        if(XtenantJudgeUtil.isJiujiMore()){
            String servicePpid = productConfigBO.getServicePpid();
            if(StringUtils.isNotEmpty(servicePpid)){
                //服务ppid格式不正确
                String regex = "^\\d+(?:,\\d+)*$";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(servicePpid);
                if(!matcher.find()){
                    throw new CustomizeException("服务ppid格式不正确");
                }
                //判断ppid是否存在
                List<Integer> ppidList = Arrays.stream(servicePpid.split(",")).filter(ObjectUtil::isNotEmpty).map(Integer::parseInt).collect(Collectors.toList());
                List<Productinfo> productinfoList = productinfoService.getProductinfoByPpid(ppidList);
                if(CollectionUtils.isEmpty(productinfoList)){
                    throw new CustomizeException("ppid都不存在");
                }
                //productinfoList根据Productinfo里面的ppid收集成为map
                Map<Integer,Productinfo> productinfoMap = productinfoList.stream().collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(),(n1,n2)->n2));
                StringJoiner joiner = new StringJoiner(",");
                for(Integer ppid : ppidList){
                    if(!productinfoMap.containsKey(ppid)){
                        joiner.add(ppid.toString());
                    }
                }
                if(joiner.length()>0){
                    throw new CustomizeException("服务ppid:"+joiner+"不存在");
                }
            }
        }
    }

    /**
     * 新增保存商品配置
     *
     * @param productConfigBO
     * @return
     */
    @Override
    public R<String> saveOrUpdateConfig(ProductConfigAddBOV2 productConfigBO) {
        ProductConfig productConfig = null;
        MeituanJdWorkLog structure = null;
        boolean flag = false;
        Integer platFrom = PlatfromEnum.DYTG.getCode();
        //校验数据
        checkData(productConfigBO);
        if (Objects.nonNull(productConfigBO.getId()) && !Objects.equals(0, productConfigBO.getId())) {
            ProductConfig oldProductConfig = this.getById(productConfigBO.getId());
            if (Objects.isNull(oldProductConfig)) {
                throw new CustomizeException("操作失败，更新失败，商品配置不存在");
            }
            productConfig = toProductConfig(productConfigBO);
            productConfig.setId(oldProductConfig.getId());
            //解决实际不会修改的字段
            productConfig.setProductCode(oldProductConfig.getProductCode());
            productConfig.setSyncLimit(oldProductConfig.getSyncLimit());
            productConfig.setSyncFirst(oldProductConfig.getSyncFirst());

            //数据对比工具
            List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(ProductConfig.class, oldProductConfig, productConfig);
            String logPrefix = "商家sku编码：" + productConfig.getSkuId() + "，本地商品sku：" + productConfig.getPpriceid()+",";
            //添加日志操作
            if (CollectionUtils.isEmpty(fieldModifiedLog)) {
                flag = true;
                fieldModifiedLog.add("无字段更新！");
                fieldModifiedLog.add(logPrefix);
            }else{
                fieldModifiedLog.add(logPrefix);
            }
            structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_UPDATE.getMessage(), fieldModifiedLog.toString(),
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_UPDATE.getCode(), platFrom, productConfig.getTenantCode());
        } else {
            productConfig = toProductConfig(productConfigBO);
            productConfig.setProductCode(productConfigBO.getSkuId());
            //日志记录操作
            structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_ADD.getMessage(), MessageFormat.format("商家SKU编码：{0}", productConfig.getSkuId()),
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_ADD.getCode(), platFrom, productConfig.getTenantCode());
        }
        if (flag) {
            return R.success("操作成功");
        }
        //开启事务 切换写库
        ProductConfig finalProductConfig = productConfig;
        MeituanJdWorkLog finalStructure = structure;
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(finalStructure);
            if (CommonUtil.isNotNullZero(finalProductConfig.getId())) {
                this.lambdaUpdate()
                        .set(StrUtil.isNotBlank(finalProductConfig.getProductCode()),ProductConfig::getProductCode, finalProductConfig.getProductCode())
                        .set(StrUtil.isNotBlank(finalProductConfig.getSkuId()),ProductConfig::getSkuId, finalProductConfig.getSkuId())
                        .set(ProductConfig::getPlatformCost, finalProductConfig.getPlatformCost())
                        .set(Objects.nonNull(finalProductConfig.getPpriceid()),ProductConfig::getPpriceid, finalProductConfig.getPpriceid())
                        .set(Objects.nonNull(finalProductConfig.getPriceSplit()),ProductConfig::getPriceSplit, finalProductConfig.getPriceSplit())
                        .set(ProductConfig::getActualPaymentType, finalProductConfig.getActualPaymentType())
                        .set(ProductConfig::getDiscountAmount, finalProductConfig.getDiscountAmount())
                        .set(ProductConfig::getServicePpid, finalProductConfig.getServicePpid())
                        .set(ProductConfig::getLabel, finalProductConfig.getLabel())
                        .set(ProductConfig::getProductName, finalProductConfig.getProductName())
                        .set(ProductConfig::getCouponPrice, finalProductConfig.getCouponPrice())
                        .set(ProductConfig::getRuleCode, finalProductConfig.getRuleCode())
                        .set(ProductConfig::getSellType, finalProductConfig.getSellType())
                        .set(Objects.nonNull(finalProductConfig.getLibraryLock()),ProductConfig::getLibraryLock, finalProductConfig.getLibraryLock())
                        .set(ProductConfig::getMkcId, finalProductConfig.getMkcId())
                        .set(Objects.nonNull(finalProductConfig.getType()),ProductConfig::getType, finalProductConfig.getType())
                        .set(ProductConfig::getSyncFirst, finalProductConfig.getSyncFirst())
                        .set(ProductConfig::getSyncLimit, finalProductConfig.getSyncLimit())
                        .set(Objects.nonNull(finalProductConfig.getSyncRatio()),ProductConfig::getSyncRatio, finalProductConfig.getSyncRatio())
                        .set(Objects.nonNull(finalProductConfig.getSyncType()),ProductConfig::getSyncType, finalProductConfig.getSyncType())
                        .set(ProductConfig::isSyncOff, finalProductConfig.isSyncOff())
                        .set(ProductConfig::isGiftOff, finalProductConfig.isGiftOff())
                        .eq(ProductConfig::getId, finalProductConfig.getId())
                        .update();
            } else {
                save(finalProductConfig);
            }
        }).commit();
        return R.success("操作成功");
    }

    @Override
    public R exportExcelV2(ProductConfigSearchBOV2 req, HttpServletResponse response) {
        req.setCurrent(NumberConstant.ONE);
        req.setSize(10000);
        Page<ProductConfigVOV2> productConfigPage = listByPageV2(req);
        List<ProductConfigVOV2> records = productConfigPage.getRecords();
        String fileName = "商品数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xls";
        LinkedList<String> titles = new LinkedList<>(Arrays.asList("类型", "商品名称", "优惠券规则码", "平台ppid"));
        //商户编码、商家spu编码、商家sku编码、本地商品sku、价格分摊金额、同步开关、计算方式、同步系数、同步上限、优先同步
        List<List<Object>> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ProductConfigVOV2 record : records) {
                List<Object> export = new LinkedList<>();
                //类型
                export.add(record.getTypeName());
                //商品名称
                export.add(record.getProductName());
                //优惠券规则码
                export.add(record.getRuleCode());
                //平台ppid
                export.add(record.getSkuId());
                results.add(export);
            }
        }
        try {
            ExcelUtils.export(response, results, titles, null,
                    fileName, 1);
        } catch (Exception e) {
            log.warn(String.valueOf(e));
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功!");
    }

    @Override
    public R exportExcel(ProductConfigSearchBO req, HttpServletResponse response) {
        req.setCurrent(NumberConstant.ONE);
        req.setSize(10000);
        Page<ProductConfigVO> productConfigPage = listByPage(req);
        List<ProductConfigVO> records = productConfigPage.getRecords();
        if (Objects.equals(req.getType(), NumberConstant.ONE)) {
            return exportExcelByRecover(records, response);
        }
        String fileName = "商品数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xls";
        LinkedList<String> titles = new LinkedList<>();
        if (Objects.equals(req.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
            fileName = "美团商品数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xls";
            titles.addAll(Arrays.asList("商户编码", "商家spu编码", "商家sku编码", "本地商品sku", "本地商品状态", "本地商品会员价", "平台售价","分类", "商品名称", "规格", "价格分摊金额",
                    "成本价", "成本均价", "限时抢购价", "换新补贴金额", "同步开关", "计算方式", "同步系数", "同步上限", "优先同步"));
        } else if (Objects.equals(req.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY)) {
            titles.addAll(Arrays.asList("商户编码", "商家spu编码", "商家sku编码", "本地PPID", "价格分摊金额", "同步开关",
                    "计算方式", "同步系数", "同步上限", "优先同步"));
            fileName = "抖音新品商品数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xls";
        }
        List<Integer> ppidList = records.stream().map(ProductConfigVO::getPpriceid).collect(Collectors.toList());
        Map<Integer, BigDecimal> inpriceMap = new HashMap<>();
        Map<Integer, ProductActivityPriceVO> activityPriceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ppidList)) {
            Map<Integer, List<Integer>> groupNumToListMap = ppidList.stream().collect(Collectors.groupingBy(s -> (ppidList.indexOf(s)) / 2000));
            Set<Integer> groupNumSet = groupNumToListMap.keySet();
            for (Integer groupNum : groupNumSet) {
                List<Integer> partList = groupNumToListMap.get(groupNum);
                List<ProductKcInpriceVO> temp = this.baseMapper.getInpriceFromProductKc(partList);
                if (CollectionUtils.isNotEmpty(temp)) {
                    Map<Integer, BigDecimal> collect = temp.stream()
                            .filter(x -> Objects.nonNull(x.getPpriceid()) && Objects.nonNull(x.getInprice()))
                            .collect(Collectors.toMap(ProductKcInpriceVO::getPpriceid, ProductKcInpriceVO::getInprice, (v1, v2) -> v1));
                    inpriceMap.putAll(collect);
                }
                R<List<ProductActivityPriceVO>> activityPriceRes = webCloud.activityPrice(partList);
                List<ProductActivityPriceVO> data = activityPriceRes.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    Map<Integer, ProductActivityPriceVO> collect = data.stream()
                            .collect(Collectors.toMap(ProductActivityPriceVO::getPpid, Function.identity(), (v1, v2) -> v1));
                    activityPriceMap.putAll(collect);
                }
            }
        }
        //商户编码、商家spu编码、商家sku编码、本地商品sku、价格分摊金额、同步开关、计算方式、同步系数、同步上限、优先同步
        List<List<Object>> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ProductConfigVO record : records) {
                Integer ppriceid = record.getPpriceid();
                Boolean ismobile = record.getIsmobile();
                List<Object> export = new LinkedList<>();
                //商户编码
                export.add(record.getTenantCode());
                //商家spu编码
                export.add(record.getProductCode());
                //商家sku编码
                export.add(record.getSkuId());
                //本地商品sku
                export.add(ppriceid);
                if (Objects.equals(req.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
                    //本地商品状态
                    export.add(record.getQueName());
                    //本地商品会员价
                    export.add(record.getVipPrice());
                    //平台售价
                    export.add(record.getPlatformCost());
                    //分类
                    export.add(record.getCid());
                    //商品名称
                    export.add(record.getProductName());
                    //规格
                    export.add(record.getProductColor());
                }
                //价格分摊金额
                export.add(record.getPriceSplit());
                if (Objects.equals(req.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
                    String costprice = "";
                    String costAvePrice = "";
                    String limitBuyPrice = "";
                    String accessoryPrice = "";
                    ProductActivityPriceVO productActivityPriceVO = activityPriceMap.get(ppriceid);
                    if (Objects.nonNull(productActivityPriceVO)) {
                        costprice = String.valueOf(productActivityPriceVO.getCostprice());
                        costAvePrice = String.valueOf(productActivityPriceVO.getCostAvePrice());
                        accessoryPrice = String.valueOf(productActivityPriceVO.getAccessoryPrice());
                        limitBuyPrice = String.valueOf(productActivityPriceVO.getLimitBuyPrice());
                    }
                    if (!ismobile) {
                        costprice = "";
                        costAvePrice = String.valueOf(inpriceMap.get(ppriceid));
                    }
                    //成本价
                    export.add(costprice);
                    //成本均价
                    export.add(costAvePrice);
                    //限时抢购价
                    export.add(limitBuyPrice);
                    //换新补贴金额
                    export.add(accessoryPrice);
                }
                //同步开关
                export.add(record.getSyncOff());
                //计算方式
                export.add(record.getSyncType());
                //同步系数
                export.add(record.getSyncRatio());
                //同步上限
                export.add(record.getSyncLimit());
                //优先同步
                export.add(record.getSyncFirst());
                results.add(export);
            }
        }
        try {
            ExcelUtils.export(response, results, titles, null,
                    fileName, 1);
        } catch (Exception e) {
            log.warn(String.valueOf(e));
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功!");
    }

    private static R exportExcelByRecover(List<ProductConfigVO> records, HttpServletResponse response) {
        LinkedList<String> titles = new LinkedList<>(Arrays.asList("商户编码", "商家spu编码", "商家sku编码", "良品MKC_ID", "下单金额", "库存是否锁定",
                "商品状态"));
        List<List<Object>> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ProductConfigVO record : records) {
                List<Object> export = new LinkedList<>();
                //商户编码
                export.add(record.getTenantCode());
                //商家spu编码
                export.add(record.getProductCode());
                //商家sku编码
                export.add(record.getSkuId());
                //本地商品mkc_id
                export.add(record.getMkcId());
                //价格分摊金额
                export.add(record.getPriceSplit());
                //库存是否锁定
                export.add(DecideUtil.iif(Boolean.TRUE.equals(record.getLibraryLock()), "锁定", "未锁定"));
                //商品状态
                export.add(DecideUtil.iif(CommonUtil.isNotNullZero(record.getSellType()), "已售出", "未售出"));
                results.add(export);
            }
        }
        String fileName = "抖音良品商品数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xls";
        try {
            ExcelUtils.export(response, results, titles, null,
                    fileName, 1);
        } catch (Exception e) {
            log.warn(String.valueOf(e));
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功!");
    }

    @Override
    public R<Boolean> updateByDouDian(ProductConfigEditByDouDianBO editBo) {
        ProductConfig pc = getById(editBo.getId());
        if (Objects.isNull(pc)) {
            return R.error(String.format("数据不存在：ID[%s]", editBo.getId()));
        }
        ProductConfig newpc = new ProductConfig();
        BeanUtils.copyProperties(pc, newpc);
        if (editBo.getLibraryLock() != null) {
            newpc.setLibraryLock(editBo.getLibraryLock());
        }
        if (StrUtil.isNotEmpty(editBo.getPriceSplit()) && NumberUtil.isNumber(editBo.getPriceSplit())) {
            newpc.setPriceSplit(Double.valueOf(editBo.getPriceSplit()));
        }
        if (StrUtil.isNotBlank(editBo.getProductCode())) {
            newpc.setProductCode(editBo.getProductCode());
        }
        if (StrUtil.isNotBlank(editBo.getSkuId())) {
            newpc.setSkuId(editBo.getSkuId());
        }
        if (Objects.nonNull(editBo.getLabel())) {
            newpc.setLabel(editBo.getLabel());
        }
        //根据mkcId 查询ppid
        List<SearchProductInfoVO> productNameList = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,() ->productinfoService.getProductNamesByMkcId(Convert.toStr(pc.getMkcId()), false));
        Map<Integer, RecoverMarketsubinfo> outDouYinBasketMap = CollUtil.newHashMap(productNameList.size());
        StringJoiner outErrMsgJoiner = new StringJoiner(StringPool.SLASH);
        handleDouYinBasket(productNameList, outDouYinBasketMap, outErrMsgJoiner);
        //记录抖音的锁定商品id
        productNameList.stream().findFirst().map(SearchProductInfoVO::getToBasketId).map(outDouYinBasketMap::get)
                .map(RecoverMarketsubinfo::getBasketId).map(Convert::toStr).ifPresent(newpc::setRuleCode);

        //无字段更新时不写库
        boolean flag = false;


        //数据对比工具
        List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(ProductConfig.class, pc, newpc);
        String logPrefix = "商家sku编码：" + newpc.getSkuId() + "，本地商品sku：" + newpc.getPpriceid()+",";
        //添加日志操作
        if (CollectionUtils.isEmpty(fieldModifiedLog)) {
            flag = true;
            fieldModifiedLog.add("无字段更新！");
            fieldModifiedLog.add(logPrefix);
        }else{
            fieldModifiedLog.add(logPrefix);
        }
        Integer platFrom = PlatfromEnum.getCodeByName(editBo.getPlatCode());
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(editBo.getPlatCode())) {
            platFrom = PlatfromEnum.DY.getCode();
        }



        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_UPDATE.getMessage(), fieldModifiedLog.toString(),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_UPDATE.getCode(), platFrom, newpc.getTenantCode());
        if (flag) {
            return R.success("操作成功");
        }
        R<Boolean> result = R.success("更新成功！");
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            //校验是否进行库存锁定
            if (!Objects.equals(newpc.getLibraryLock(), pc.getLibraryLock())) {
                boolean isLock = Boolean.TRUE.equals(newpc.getLibraryLock());
                int lockNum = baseMapper.lockStockBatch(Collections.singletonList(pc.getMkcId()),
                        DecideUtil.iif(isLock, 1, 0), outDouYinBasketMap.keySet());
                result.put("lockNum", lockNum);
                if(!isLock && lockNum <=0){
                    throw new CustomizeException("良品库存解锁失败");
                }
                if(isLock && lockNum>0){
                    updateGisGroundByMkcId(Collections.singletonList(pc.getMkcId()), DecideUtil.iif(isLock,
                            GisgroundEnum.NOT_ON_SALE.getCode(), GisgroundEnum.ON_SALE.getCode()));
                }

            }else{
                result.put("lockNum", 1);
            }
            meituanJdWorkLogService.save(structure);
            updateById(newpc);
        }).commit();
        return result;
    }

    /**
     * 导入Excel 抖音良品专用
     *
     * @param file file
     * @param tenantId
     * @return
     */
    @Override
    public R<Boolean> uploadByDouDianData(MultipartFile file, Integer tenantId) {
        Tenant tenant;
        if(tenantId == null || (tenant = tenantService.getById(tenantId)) == null) {
            return R.error("tenantId不能为空");
        }
        List<ProductConfigImportByDouDianBO> list = readProductConfigImportByDouDianFromExcel(file);
        if (CollUtil.isEmpty(list)) {
            return R.error("数据不能为空");
        }
        List<ProductConfig> dataList = list.stream().map(bo -> {
            ProductConfig obj = new ProductConfig();
            BeanUtils.copyProperties(bo, obj);
            obj.setLibraryLock(Convert.toBool(StrUtil.trim(bo.getIsLock())));
            obj.setPlatCode(ThirdPlatformCommonConst.THIRD_PLAT_DY);
            return obj;
        }).collect(Collectors.toList());
        //数据过滤
        dataList = dataList.stream().filter(obj -> {
            obj.setTenantCode(tenant.getTenantCode());
            if (null == obj.getMkcId()) {
                log.info("mkcId 不能为空");
                return false;
            }
            // 上面循环调用数据库需要优化
            if (null == obj.getPriceSplit()) {
                obj.setPriceSplit(0D);
            }
            if (null == obj.getSyncRatio()) {
                obj.setSyncRatio(0D);
            }
            if (0 == obj.getSyncType()) {
                obj.setSyncType(1);
            }
            if (null == obj.getType()) {
                obj.setType(1);
            }
            if(obj.getLibraryLock() == null){
                //默认为锁定库存
                obj.setLibraryLock(Boolean.TRUE);
            }
            return true;
        }).collect(Collectors.toList());
        //查询良品的ppid
        if (CollUtil.isEmpty(dataList)) {
            return R.error("导入失败，参数错误");
        }
        List<Integer> mkcIds = dataList.stream().map(ProductConfig::getMkcId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, RecoverMarketsubinfo> outDouYinBasketMap = CollUtil.newHashMap(mkcIds.size());
        if (!mkcIds.isEmpty()){
            //校验已经有相关的配置
            List<Integer> existMkcConfigs = CommonUtils.bigDataInQuery(mkcIds, ids -> lambdaQuery().in(ProductConfig::getMkcId, ids).select(ProductConfig::getMkcId)
                    .list()).stream().map(ProductConfig::getMkcId).collect(Collectors.toList());
            if(!existMkcConfigs.isEmpty()){
                throw new CustomizeException(StrUtil.format("以下[{}]mkcId已经存在相应的配置", existMkcConfigs.stream()
                        .map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA))));
            }
            List<SearchProductInfoVO> productNamesByMkcId = productinfoService.getProductNamesByMkcId(mkcIds.stream()
                    .map(Object::toString).collect(Collectors.joining(",")), false);
            //获取抖音的配置

            StringJoiner outErrMsgJoiner = new StringJoiner(StringPool.SLASH);
            handleDouYinBasket(productNamesByMkcId, outDouYinBasketMap, outErrMsgJoiner);
            dataList.forEach(da -> productNamesByMkcId.forEach(pr -> {
                if (Objects.equals(da.getMkcId(), pr.getId())) {
                    da.setPpriceid(pr.getPpid());
                    //记录抖音的锁定商品id
                    da.setRuleCode(Optional.ofNullable(pr.getToBasketId()).map(outDouYinBasketMap::get)
                            .map(RecoverMarketsubinfo::getBasketId).map(Convert::toStr).orElse(null));
                }
            }));
        }

        //库存锁定
        List<Integer> isLockMkcTrue = dataList.stream().filter(da -> Boolean.TRUE.equals(Optional.ofNullable(da.getLibraryLock()).orElse(Boolean.FALSE))).map(ProductConfig::getMkcId).collect(Collectors.toList());
        List<Integer> isLockMkcFalse = dataList.stream().filter(da -> Boolean.FALSE.equals(Optional.ofNullable(da.getLibraryLock()).orElse(Boolean.FALSE))).map(ProductConfig::getMkcId).collect(Collectors.toList());
        //日志记录操作
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_IMPORT.getMessage(), MessageFormat.format("导入{0}条数据", dataList.size()),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_IMPORT.getCode(), PlatfromEnum.DY.getCode(), dataList.stream().map(ProductConfig::getTenantCode).findFirst().orElse("未查询到商户"));
        //开启事务 切换写库
        List<ProductConfig> finalDataList = dataList;
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            if (CollUtil.isNotEmpty(isLockMkcTrue)) {
                int lockNum = baseMapper.lockStockBatch(isLockMkcTrue, 1, outDouYinBasketMap.keySet());
                if (lockNum != isLockMkcTrue.size()) {
                    throw new CustomizeException(StrUtil.format("库存锁定{}个,锁定失败{}个", lockNum, isLockMkcTrue.size() - lockNum));
                }
                updateGisGroundByMkcId(isLockMkcTrue, GisgroundEnum.NOT_ON_SALE.getCode());
            }
            if (CollUtil.isNotEmpty(isLockMkcFalse)) {
                int lockNum = baseMapper.lockStockBatch(isLockMkcFalse, 0, outDouYinBasketMap.keySet());
                if (lockNum != isLockMkcFalse.size()) {
                    throw new CustomizeException(StrUtil.format("库存锁定{}个,锁定失败{}个", lockNum, isLockMkcFalse.size() - lockNum));
                }
                updateGisGroundByMkcId(isLockMkcFalse, GisgroundEnum.ON_SALE.getCode());
            }
            meituanJdWorkLogService.save(structure);
            saveMyBatch(finalDataList);
        }).commit();
        return R.success(String.format("导入成功(%d条数据)", dataList.size()), true);
    }

    @SneakyThrows
    private List<ProductConfigImportByDouDianBO> readProductConfigImportByDouDianFromExcel(MultipartFile file) {
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
        return reader.read().stream().skip(NumberConstant.ONE)
                .map(row -> LambdaBuild.create(ProductConfigImportByDouDianBO.class)
                        .set(ProductConfigImportByDouDianBO::setTenantCode, Convert.toStr(CollUtil.get(row, NumberConstant.ZERO)))
                        .set(ProductConfigImportByDouDianBO::setProductCode, Convert.toStr(CollUtil.get(row, NumberConstant.ONE)))
                        .set(ProductConfigImportByDouDianBO::setSkuId, Convert.toStr(CollUtil.get(row, NumberConstant.TWO)))
                        .set(ProductConfigImportByDouDianBO::setMkcId, Convert.toInt(CollUtil.get(row, NumberConstant.THREE)))
                        .set(ProductConfigImportByDouDianBO::setPriceSplit, Convert.toDouble(CollUtil.get(row, NumberConstant.FOUR)))
                        .set(ProductConfigImportByDouDianBO::setIsLock, Convert.toStr(CollUtil.get(row, NumberConstant.FIVE)))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public R<Boolean> updateSellType(List<Integer> mkcIds, Integer sellType) {
        if (baseMapper.updateSellType(mkcIds, sellType) > 0) {
            return R.success("更新成功！");
        }
        return R.error("更新失败！");
    }


    /**
     * 修改良品商品的上架状态
     *
     * @param mkcIds   mkcIds
     * @param sellType sellType
     * @return
     * @see GisgroundEnum
     */
    @Override
    public void updateGisGroundByMkcId(List<Integer> mkcIds, Integer sellType) {
        if (CollUtil.isEmpty(mkcIds)) {
            throw new CustomizeException("mkcId 参数不能为空！");
        }
        JSONObject jsonData = new JSONObject();
        if (Objects.equals(GisgroundEnum.NOT_ON_SALE.getCode(), sellType)) {
            jsonData.put("gispaying", GisgroundEnum.NOT_ON_SALE.getCode());
            jsonData.put("mkcIds", mkcIds);
        } else if (Objects.equals(GisgroundEnum.ON_SALE.getCode(), sellType)) {
            jsonData.put("gispaying", GisgroundEnum.ON_SALE.getCode());
            jsonData.put("mkcIds", mkcIds);
            jsonData.put("gisground", 0);
        }
        if (jsonData.isEmpty()) {
            throw new CustomizeException("构建值错误！");
        }
        amqpTemplate.convertAndSend("oaTopic", "saleGoodsChanged", JSON.toJSONString(jsonData));
    }

    @Override
    @SneakyThrows
    public R<Integer> importPoiCodeSpu(MultipartFile file) {
        if (file == null) {
            return R.error("文件不能为空");
        }
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
        String appId = "APP_ID";
        String appName = "APP_NAME";
        String appPoiCode = "APP_POI_CODE";
        String categoryName = "品类名称";
        String productName = "商品名称";
        String spec = "规格";
        String price = "价格";
        String kc = "库存";
        String shiFouMaiGuang = "是否卖光";
        String appSpuCode = "APP_SPU_CODE";
        String appFoodCode = "APP_FOOD_CODE";
        String skuId = "SKU_ID";
        String upcCode = "UPC码";
        AtomicInteger reCount = new AtomicInteger(0);
        int seconds = 1;
        int maxCount = 50;
        AtomicInteger currCount = new AtomicInteger(50);

        Map<String, List<Dict>> groupAppIdMap = reader.readAll().stream().map(m -> LambdaBuild.create(Dict.create()).set(Dict::putAll, m).build())
                .filter(sku -> ObjectUtil.isAllNotEmpty(sku.get(appId), sku.getStr(appPoiCode), sku.getStr(productName),
                        sku.getStr(categoryName), ObjectUtil.defaultIfBlank(sku.getStr(appSpuCode), sku.getStr(appFoodCode)))
                        // 这两个字段要不都是空,要不都不为空
                        && Stream.of(sku.getStr(spec), sku.getStr(skuId)).filter(Objects::isNull).count() != 1
                )
                .collect(Collectors.groupingBy(map -> map.getStr(appId)));
        StringJoiner msgJoiner = new StringJoiner(" ");
        long starTime = System.currentTimeMillis();
        Dict codeCache = importPoiCodeCache.computeIfAbsent(file.getOriginalFilename(), key -> Dict.create());
        for (Map.Entry<String, List<Dict>> entry : groupAppIdMap.entrySet()) {
            String tenantCode = entry.getKey();
            List<Dict> skuList = entry.getValue();
            //根据商户编码获取对应的 AppId  AppSecret
            Tenant oneTenantBy = SpringUtil.getBean(TenantService.class).getOneTenantBy(tenantCode, ThirdPlatformCommonConst.THIRD_PLAT_MT);
            String meiTuanAppId = oneTenantBy.getTenantCode();
            String meiTuanAppSecret = oneTenantBy.getAppSecret();
            SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
            for (Dict sku : skuList.stream().sorted(Comparator.comparing(sku -> sku.getStr(appPoiCode))).collect(Collectors.toList())) {
                if (Duration.ofMillis(System.currentTimeMillis() - starTime).getSeconds() < seconds && currCount.get() <= 0) {
                    Thread.sleep(seconds * 1000 - (System.currentTimeMillis() - starTime));
                    starTime = System.currentTimeMillis();
                    currCount.set(maxCount);
                }
                //组建请求参数,如有其它参数请补充完整
                RetailUpdateAppFoodCodeByNameAndSpecRequest ruafc = new RetailUpdateAppFoodCodeByNameAndSpecRequest(systemParam);
                ruafc.setApp_poi_code(sku.getStr(appPoiCode));
                ruafc.setName(sku.getStr(productName));
                ruafc.setCategory_name(sku.getStr(categoryName));
                ruafc.setSku_id(sku.getStr(skuId));
                ruafc.setSpec(sku.getStr(spec));
                ruafc.setApp_spu_code(sku.getStr(appSpuCode));
                ruafc.setApp_food_code(sku.getStr(appFoodCode));
                String cacheKey = ObjectUtil.defaultIfBlank(sku.getStr(appSpuCode), sku.getStr(appFoodCode));
                String data = "data";
                Optional<JSONObject> rrOpt = Optional.ofNullable(Convert.toStr(codeCache.get(cacheKey))).map(d -> new JSONObject().fluentPut(data, d));
                if (!rrOpt.isPresent()) {
                    SgOpenResponse sgOpenResponse = ruafc.doRequest();
                    currCount.decrementAndGet();
                    if (log.isDebugEnabled()) {
                        log.debug("[{}]商品美团门店关联商品参数:{} 结果:{}", sku.getStr(productName), JSON.toJSONString(ruafc), sgOpenResponse.getRequestResult());
                    }
                    rrOpt = Optional.ofNullable(sgOpenResponse.getRequestResult())
                            .map(rr -> JSONUtil.isJson(rr) ? rr : "{\"error\": {\"code\": 700,\"msg\": \"系统网络异常\"},\"data\": \"ng\"}")
                            .map(JSON::parseObject);
                    codeCache.put(cacheKey, rrOpt.map(rr -> rr.getString(data)).orElse(OrderRes.RESULT_NG));
                }

                if (rrOpt.filter(rr -> StrUtil.equalsIgnoreCase(rr.getString(data), OrderRes.RESULT_OK)).isPresent()) {
                    reCount.incrementAndGet();
                } else {
                    rrOpt.map(rr -> rr.getJSONObject("error")).map(er -> er.getString("msg"))
                            .map(msg -> StrUtil.format("[{}]更新失败,原因:{}", sku.getStr(productName), msg))
                            .ifPresent(msgJoiner::add);
                }
            }

        }
        log.debug("导入成功数量:{}", reCount.get());
        return LambdaBuild.create(R.success(reCount.get())).set(R::setUserMsg, msgJoiner.toString()).build();
    }

    private ProductConfig toProductConfig(ProductConfigAddBOV2 productConfigBO) {
        ProductConfig productConfig = new ProductConfig();
        productConfig.setTenantCode(productConfigBO.getTenantCode());
        productConfig.setPlatCode(productConfigBO.getPlatCode());
        productConfig.setSkuId(productConfigBO.getSkuId());
        productConfig.setProductName(productConfigBO.getProductName());
        productConfig.setRuleCode(productConfigBO.getRuleCode());
        productConfig.setCouponPrice(productConfigBO.getCouponPrice());
        productConfig.setType(productConfigBO.getType());
        productConfig.setPpriceid(0);
        productConfig.setPriceSplit(0.0);
        productConfig.setSyncOff(false);
        productConfig.setSyncType(0);
        productConfig.setSyncRatio(0.0);
        productConfig.setLibraryLock(false);
        productConfig.setDiscountAmount(productConfigBO.getDiscountAmount());
        productConfig.setServicePpid(productConfigBO.getServicePpid());
        productConfig.setLabel(productConfigBO.getLabel());
        productConfig.setActualPaymentType(productConfigBO.getActualPaymentType());
        return productConfig;
    }
}
