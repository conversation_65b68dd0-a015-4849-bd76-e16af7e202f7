package com.jiuji.oa.oacore.operator.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: gengjiaping
 * @date: 2020/3/19
 */
@Getter
@AllArgsConstructor
public enum RepairAccessoriesKeyEnum implements CodeMessageEnumInterface {
    PPID(1,"ppid"),
    PRODUCT_ID(2,"product_id"),
    PRODUCT_NAME(3,"商品名称"),
    PPID_AND_PRODUCT_ID(4,"ppid+product_id");



    /**
     * 门店
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

    /**
     * 获取包含商品id类型
     * @return
     */
    public static List<Integer> getProductIdList() {
        List<Integer> list = Arrays.asList(PRODUCT_ID.getCode(), PPID_AND_PRODUCT_ID.getCode());
        return  Arrays.stream(values()).filter(item->list.contains(item.getCode()))
                .map(RepairAccessoriesKeyEnum::getCode).collect(Collectors.toList());
    }
}
