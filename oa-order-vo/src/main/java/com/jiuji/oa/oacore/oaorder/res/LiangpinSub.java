package com.jiuji.oa.oacore.oaorder.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/31 10:44
 */
@Data
public class LiangpinSub {
    private int sub_id;
    private String areaGps;
    private int sub_check;
    private int delivery;
    private String subAddress;
    public String subcheckName(int sub_check) {
        if (sub_check == 0){
            return "未确认";
        }else if (sub_check == 1){
            return "已确认";
        }else if (sub_check == 2){
            return "已出库";
        }else if (sub_check == 6){
            return "欠款";
        }else if (sub_check == 5) {
            return "等待确认";
        }else if (sub_check == 3) {
            return "已完成";
        }else if (sub_check == 7) {
            return "待处理";
        }else{
            return "";
        }
    }

    public String deliveryName(int delivery) {
        if (delivery == 1){
            return "到店自取";
        }else if (delivery == 2){
            return "九机快送";
        }else if (delivery == 3) {
            return "自提点";
        } else if (delivery == 4) {
            return "快递运输";
        } else if (delivery == 5) {
            return "加急配送";
        } else {
            return "";
        }
    }

    private int areaid;
    private String areaCode;
    private String departId;
    private String areaName;
    private String areaTel;
    private String areaAddress;
    private boolean isWeb;
    private String sender;
    private String senderHeadImage;
    private String senderGps;
    private String expectTime;
    private BigDecimal yinfum;
    private BigDecimal yifuM;
    private List<ProductInfoForOrders> proList;
    private String addtime;
    private String finishtime;
    private int ispj;
    private int paisongState;
    private String psStatsName;
    private int recoverSubid;
    // 异常订单标识
    private boolean isException;
}
