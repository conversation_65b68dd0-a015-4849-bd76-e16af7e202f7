package com.jiuji.oa.oacore.thirdplatform.baozun.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.service.BasketService;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.OrderOperatorBo;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSubMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.OrderLabelEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantSubService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.BasketTypeEnum;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 宝尊本地订单信息同步服务类
 * <AUTHOR>
 * @since 2022/1/5 16:38
 */
@Service
@Slf4j
public class BzTenantSubServiceImpl implements BzTenantSubService {
    @Autowired
    private SubLogsCloud subLogsCloud;
    @Autowired
    private BzTenantSubMapper bzTenantSubMapper;
    @Override
    public void asyncInfoToSub(Collection<String> transactionNumbers) {
        //必须按订单号进行降序排列,避免死锁
        syncOrderLabelInfoToSub(transactionNumbers);
        //更新销售人员会同时更新订单的收件人信息
        syncOrderOperatorInfoToSub(transactionNumbers);
    }

    @Override
    public int updateSubDealResult(int location, int result, Set<Integer> ids) {
        return bzTenantSubMapper.updateSubDealResult(location, result, ids);
    }

    /**
     * 异步同步订单的执行人信息
     */
    private void syncOrderOperatorInfoToSub(Collection<String> transactionNumbers) {
        CompletableFuture.runAsync(() -> this.handleOrderOperatorWithPage(transactionNumbers)).exceptionally(e->{
            log.error("同步宝尊orderOperator异常",e);
            return null;
        }).join();
    }

    private void invokeWithPage(int pageSize,Function<Integer,Integer> fun){
        //获取一天内未处理的 orderOperator的信息 orderLabel,subId,sn
        boolean hasNextPage = true;
        for (int pageStart=0;hasNextPage;pageStart+=pageSize){
            Integer currPageRecordCount = fun.apply(pageStart);
            //当前小于页大小也没有下一页
            if (currPageRecordCount < pageSize) {
                hasNextPage = false;
            }
        }
    }

    private void handleOrderOperatorWithPage(Collection<String> transactionNumbers) {
        int pageSize = NumberConstant.FIVE_HUNDRED;
        invokeWithPage(pageSize, pageStart -> invokeOrderOperator(pageSize, pageStart, transactionNumbers));
    }

    private int invokeOrderOperator(int pageSize, Integer pageStart, Collection<String> transactionNumbers) {
        List<OrderOperatorBo> orderOperators = MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW
                , () -> bzTenantSubMapper.listOrderOperator(NumberConstant.SEVEN, pageStart, pageSize, transactionNumbers).stream()
                        .filter(operator -> StrUtil.isNotBlank(operator.getCh999Name())).filter(operator->Objects.nonNull(operator.getSubId()))
                        .filter(operator -> Objects.equals(operator.getRank(), NumberConstant.ONE))
                        .map(operator -> operator.setName(CommonUtils.maxLength(operator.getName(),20)))
                        .collect(Collectors.toList()));
        Map<String, List<OrderOperatorBo>> orderOperatorGroupMap = orderOperators.stream().collect(Collectors.groupingBy(operator -> operator.getCh999Name()));
        //批量更新订单的业务人员信息
        SubLogsCloud subLogsCloud = SpringUtil.getBean(SubLogsCloud.class);
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
        orderOperatorGroupMap.forEach((k, operators) -> forEachAndUpdateOrderOperatorInfo(subLogsCloud, result, k, operators));

        return orderOperators.size();
    }

    private void forEachAndUpdateOrderOperatorInfo(SubLogsCloud subLogsCloud, AtomicReference<Boolean> result, String k, List<OrderOperatorBo> operators) {
        MultipleTransaction transaction = MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            int usi = bzTenantSubMapper.updateSubInuserBySubId(k,operators);
            int ubi = bzTenantSubMapper.updateBasketInuserBySubId(k, operators.stream().map(OrderOperatorBo::getSubId).distinct().collect(Collectors.toList()));
            if (usi > 0 && ubi>0) {
                usi = bzTenantSubMapper.updateSalesOrderDealResult(NumberConstant.ONE,NumberConstant.ONE, operators.stream()
                        .map(OrderOperatorBo::getTransactionNumber).collect(Collectors.toSet()));
            }
            if (usi > 0 && ubi>0) {
                //批量添加订单日志记录日志
                R<Boolean> logR = subLogsCloud.addSubLogBatch(operators.stream()
                        .map(operator -> buildSubLogsNewReq(operator.getSubId(), StrUtil.format("交易人员由【{}】更改为【{}】", operator.getInUser(), operator.getCh999Name())))
                        .collect(Collectors.toList()));
                if (logR.isSuccess()) {
                    result.set(Boolean.TRUE);
                }
            }
        });
        if(Boolean.TRUE.equals(result.get())){
            transaction.commit();
        }else{
            transaction.rollback();
        }
    }

    private void syncOrderLabelInfoToSub(Collection<String> transactionNumbers){
        CompletableFuture.runAsync(() -> this.handleOrderLabelWithPage(transactionNumbers)).exceptionally(e->{
            log.error("同步宝尊orderLabel异常",e);
            return null;
        }).join();
    }

    private void handleOrderLabelWithPage(Collection<String> transactionNumbers) {
        //获取一天内未处理的 orderlabel的信息 orderLabel,subId,sn
        int pageSize = NumberConstant.FIVE_HUNDRED;
        invokeWithPage(pageSize,pageStart->{
            List<Dict> orderLabels = MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW
                    ,()-> bzTenantSubMapper.listOrderLabel(NumberConstant.SEVEN, pageStart, pageSize, transactionNumbers));
            forEachAndUpdateOrderLabelInfo(orderLabels);
            return orderLabels.size();
        });
    }

    private void forEachAndUpdateOrderLabelInfo(List<Dict> orderLabels) {
        String idKey = "id";
        String orderLabelKey = "orderLabel";
        String subIdKey = "subId";
        String snKey="sn";
        String transactionNumberKey = "transactionNumber";
        String nameKey = "name";
        String emailKey = "email";
        String mobileKey = "mobile";
        String basketIdKey = "basketId";
        Map<String,List<Dict>> orderLabelGroupMap = orderLabels.stream().filter(dict-> StrUtil.isNotBlank(dict.getStr(orderLabelKey)))
                .collect(Collectors.groupingBy(dict->dict.getStr(orderLabelKey)));
        orderLabelGroupMap.forEach((key,value)->{
            List<String> labelCodes = StrUtil.splitTrim(key, ";");
            String orderLabelComment = labelCodes.stream()
                    .map(code-> Optional.ofNullable(EnumUtil.getEnumByCode(OrderLabelEnum.class,code))
                    .map(OrderLabelEnum::getMessage).orElse(code))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(SignConstant.ZHENG_XIE_GANG));
            boolean isEducationDiscount = labelCodes.contains(OrderLabelEnum.EDUCATION_DISCOUNT.getCode());
            //同一批在同一个事物中
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->{
                List<Integer> basketIds = value.stream().map(dict -> dict.getInt(basketIdKey)).filter(Objects::nonNull).collect(Collectors.toList());
                if(isEducationDiscount && !basketIds.isEmpty()){
                    //打上教育优惠标签
                    SpringUtil.getBean(BasketService.class).lambdaUpdate().in(Basket::getBasketId, basketIds)
                            .set(Basket::getType, BasketTypeEnum.APPLE_EDUCATION_MAIN_PRO.getCode())
                            .update();
                }
                //相同的orderlabel信息进行批量更新sub
                int uscI = bzTenantSubMapper.updateSubCommentBySubId(orderLabelComment, value.stream().map(dict -> dict.getInt(subIdKey)).collect(Collectors.toSet()));
                //批量更新宝尊的order的remark
                int uorI = bzTenantSubMapper.updateOrderRemarkBySubId(orderLabelComment, value.stream().map(dict -> dict.getStr(transactionNumberKey)).collect(Collectors.toSet()));
                //更新处理结果 个位为orderLabel的处理状态 0 未处理 1 处理成功 2处理失败 3 为处理过了可以继续处理
                bzTenantSubMapper.updateSubDealResult(NumberConstant.ONE,NumberConstant.ONE,value.stream().map(dict -> dict.getInt(idKey)).collect(Collectors.toSet()));
                //批量增加串号绑定服务订单日志
                String serviceName = OrderLabelEnum.listBindSnService().stream().filter(ol -> labelCodes.contains(ol.getCode()))
                        .map(OrderLabelEnum::getMessage)
                        .collect(Collectors.joining(SignConstant.ZHENG_XIE_GANG));
                if((uscI>0 || uorI>0) && StrUtil.isNotBlank(serviceName)){
                    List<SubLogsNewReq> logs = value.stream()
                            .filter(dict -> !StrUtil.isAllBlank(dict.getStr(nameKey),dict.getStr(mobileKey),dict.getStr(emailKey)))
                            .map(dict -> {
                                String comment = StrUtil.format("{}序列号绑定{}，客户姓名：{}，手机：{}，邮箱：{}",
                                        dict.getStr(snKey), serviceName, dict.getStr(nameKey), dict.getStr(mobileKey), dict.getStr(emailKey));
                                return buildSubLogsNewReq(dict.getInt(subIdKey), comment);
                            }).collect(Collectors.toList());
                    subLogsCloud.addSubLogBatch(logs);
                }
            }).commit();
        });
    }

    private static SubLogsNewReq buildSubLogsNewReq(Integer subId, String comment) {
        SubLogsNewReq log = new SubLogsNewReq();
        log.setComment(comment);
        log.setDTime(LocalDateTime.now());
        log.setSubId(subId);
        log.setInUser("系统");
        log.setType(1);
        log.setShowType(true);
        return log;
    }
}
