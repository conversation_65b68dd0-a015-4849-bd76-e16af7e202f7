package com.jiuji.oa.oacore.goldseed.service;

import com.jiuji.oa.oacore.goldseed.po.GoldseedDistributionDefconfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.goldseed.vo.GoldseedDistributionDefconfigVo;
import com.jiuji.tc.common.vo.R;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-28
 */
public interface GoldseedDistributionDefconfigService extends IService<GoldseedDistributionDefconfig> {
    /**
     * 保存或编辑分销规则默认配置
     * @return
     */
    R<Boolean> saveOrUpdateDisDefconfig(GoldseedDistributionDefconfig distributionDefconfig);

    /**
     * 删除分销规则默认配置
     * @param id
     * @return
     */
    R<Boolean> delDisDefconfig(Integer id);

    /**
     * 获取分销默认配置了信息
     * @param id
     * @return
     */
    GoldseedDistributionDefconfigVo getDisDefconfigInfo(Integer id);

    /**
     * 获取分销默认配置列表
     * @return
     */
    List<GoldseedDistributionDefconfigVo> getDisDefconfigList();

    /**
     * 启用或停用规则
     * @param id
     * @return
     */
    R<Boolean> enableOrStopConfigRule(Integer id);

    /**
     * 获取默认配置
     * @param sType
     * @param pcid
     * @param areaCode
     * @return
     */
    GoldseedDistributionDefconfig getDisDefConfig(Integer sType,Integer pcid,Integer areaCode);

    /**
     * 获取默认分销数据
     * @param areaCode
     * @return
     */
    Map<String,GoldseedDistributionDefconfig> getDisDefConfigMap(Integer areaCode);
}
