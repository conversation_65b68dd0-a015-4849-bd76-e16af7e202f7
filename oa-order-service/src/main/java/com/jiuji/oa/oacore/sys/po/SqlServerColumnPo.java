package com.jiuji.oa.oacore.sys.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/5/17 10:41
 */
@NoArgsConstructor
@Data
@TableName("INFORMATION_SCHEMA.COLUMNS")
public class SqlServerColumnPo {

    /**
     * tableCatalog
     */
    @ApiModelProperty("TABLE_CATALOG")
    private String tableCatalog;
    /**
     * tableSchema
     */
    @ApiModelProperty("TABLE_SCHEMA")
    private String tableSchema;
    /**
     * tableName
     */
    @ApiModelProperty("TABLE_NAME")
    private String tableName;
    /**
     * columnName
     */
    @ApiModelProperty("COLUMN_NAME")
    private String columnName;
    /**
     * ordinalPosition
     */
    @ApiModelProperty("ORDINAL_POSITION")
    private Integer ordinalPosition;
    /**
     * isNullable
     */
    @ApiModelProperty("IS_NULLABLE")
    private String isNullable;
    /**
     * dataType
     */
    @ApiModelProperty("DATA_TYPE")
    private String dataType;
}
