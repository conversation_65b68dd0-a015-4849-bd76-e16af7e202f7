package com.jiuji.oa.oacore.thirdplatform.yading.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;


/**
 * @Description 亚丁回调处理状态
 * <AUTHOR>
 * @Date   2022/5/10 17:07
 */
@Getter
@RequiredArgsConstructor
public enum YadingNotifyHandleStateEnum {

    /**
     * 亚丁回调处理状态
     */
    UNTREATED(0, "未处理"),
    HANDLE_SUCCESSFUL(1, "处理成功"),

    /**
     * 回调数据异常
     */
    HANDLE_FAILED1(2, "处理失败（数据异常）"),

    /**
     * 程序处理异常
     */
    HANDLE_FAILED2(3, "处理失败（程序异常）"),
    ;

    private final Integer value;

    private final String label;

    public static YadingNotifyHandleStateEnum ofLabel(String str){
        if(StringUtils.isBlank(str)){
            return null;
        }
        for (YadingNotifyHandleStateEnum methodEnum : YadingNotifyHandleStateEnum.values()) {
            if(methodEnum.label.equals(str)){
                return methodEnum;
            }
        }
        return null;
    }

}
