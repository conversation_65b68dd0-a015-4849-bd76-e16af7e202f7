package com.jiuji.oa.afterservice.sub.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.sub.po.SubReturnCheckBefore;
import com.jiuji.oa.afterservice.sub.vo.req.SubReturnCheckReq;
import com.jiuji.oa.afterservice.sub.vo.req.SubReturnCheckBeforeReq;
import com.jiuji.oa.afterservice.sub.vo.req.SubReturnCheckListQueryReq;
import com.jiuji.oa.afterservice.sub.vo.res.ReturnCheckListRes;
import com.jiuji.oa.afterservice.sub.vo.res.SubReturnCheckSectionsRes;
import com.jiuji.tc.common.vo.R;

/**
 * <p>
 * 订单授权审核记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
public interface SubReturnCheckBeforeService extends IService<SubReturnCheckBefore> {

    /**
     * 提交校验审核信息
     *
     * @param req
     * @return
     */
    R<Integer> submitCheckInfo(SubReturnCheckBeforeReq req);

    /**
     * 审核操作
     *
     * @param req
     * @return
     */
    R<Boolean> checkSubReturn(SubReturnCheckReq req);

    /**
     * 验证码授权审核列表查询
     *
     * @param req
     * @return
     */
    R<Page<ReturnCheckListRes>> getReturnCheckListPage(SubReturnCheckListQueryReq req);

    /**
     * 获取枚举值
     * @return
     */
    R<SubReturnCheckSectionsRes> getSections();

    /**
     * 获取最后一次的授权状态
     * @param subId 订单id
     * @param subType 订单类型
     * @param operateType
     * @param createUserId
     * @return
     */
    Integer getLastCheckStatus(Long subId,Integer subType,Integer operateType,Integer createUserId);
}
