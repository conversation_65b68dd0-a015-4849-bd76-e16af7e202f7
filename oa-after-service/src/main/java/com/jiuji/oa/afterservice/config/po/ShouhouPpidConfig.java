package com.jiuji.oa.afterservice.config.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_ppid_config")
public class ShouhouPpidConfig extends Model<ShouhouPpidConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ppid
     */
    private Integer ppid;

    /**
     * 配件名称
     */
    private String name;

    /**
     * 租户
     */
    private Integer xtenant;

    /**
     * 是否启用 1 启用 0 禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 是否允许负库存出库
     */
    private Boolean negative;


    public static final String ID = "id";

    public static final String PPID = "ppid";

    public static final String NAME = "name";

    public static final String XTENANT = "xtenant";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATE_USER = "create_user";

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
