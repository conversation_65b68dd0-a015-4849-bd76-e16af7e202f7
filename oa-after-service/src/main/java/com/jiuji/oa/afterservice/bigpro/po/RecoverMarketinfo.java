package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description ${comments}
 * 
 * <AUTHOR> quan
 * @date 2020-06-11 16:27:17
 */
@Data
@TableName("recover_marketInfo")
public class RecoverMarketinfo implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * $column.comments
	 */
	@TableId
	private Long subId;

	/**
	 * $column.comments
	 */
	private LocalDateTime subDate;

	/**
	 * $column.comments
	 */
	private Integer subCheck;

	/**
	 * $column.comments
	 */
	private String subTo;

	/**
	 * $column.comments
	 */
	private String subTel;

	/**
	 * $column.comments
	 */
	private Integer subPay;

	/**
	 * $column.comments
	 */
	private String comment;

	/**
	 * $column.comments
	 */
	private String inuser;

	/**
	 * $column.comments
	 */
	private String subMobile;

	/**
	 * $column.comments
	 */
	private Integer printxcount;

	/**
	 * $column.comments
	 */
	private String area;

	/**
	 * $column.comments
	 */
	private Integer zitidianid;

	/**
	 * $column.comments
	 */
	private Long userid;

	/**
	 * $column.comments
	 */
	private String onlinePay;

	/**
	 * $column.comments
	 */
	private Integer marketingid;

	/**
	 * $column.comments
	 */
	private Boolean ispiao;

	/**
	 * $column.comments
	 */
	private Integer subtype;

	/**
	 * $column.comments
	 */
	private Integer delivery;

	/**
	 * $column.comments
	 */
	private BigDecimal yingfum;

	/**
	 * $column.comments
	 */
	private BigDecimal yifum;

	/**
	 * $column.comments
	 */
	private BigDecimal feem;

	/**
	 * $column.comments
	 */
	private BigDecimal youhui1m;

	/**
	 * $column.comments
	 */
	private BigDecimal shouxum;

	/**
	 * $column.comments
	 */
	private BigDecimal jidianm;

	/**
	 * $column.comments
	 */
	private LocalDateTime tradedate;

	/**
	 * $column.comments
	 * 交易完成时间
	 */
	private LocalDateTime tradedate1;

	/**
	 * $column.comments
	 * 退货时间
	 */
	@TableField("returnDate")
	private LocalDateTime returnDate;

	/**
	 * $column.comments
	 */
	private BigDecimal dingjing;

	/**
	 * $column.comments
	 */
	private Integer subpid;

	/**
	 * $column.comments
	 */
	private String trader;

	/**
	 * $column.comments
	 */
	private BigDecimal fankuan;

	/**
	 * $column.comments
	 */
	@ApiModelProperty("销售类型 1 转售单(渠道) 0 良品 ")
	private Integer saletype;

	/**
	 * $column.comments
	 */
	private Integer areaid;

	/**
	 * $column.comments
	 */
	private String kuaididan;

	/**
	 * $column.comments
	 */
	private BigDecimal coinm;

	/**
	 * $column.comments
	 */
	private Integer islock;

	/**
	 * $column.comments
	 */
	private String qudaoname;

	/**
	 * $column.comments
	 */
	private Integer isfahuo;

	/**
	 * $column.comments
	 */
	private LocalDateTime expecttime;

	/**
	 * $column.comments
	 */
	private Integer newsubid;

}
