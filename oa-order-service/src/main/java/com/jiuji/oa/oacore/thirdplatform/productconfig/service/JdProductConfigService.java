package com.jiuji.oa.oacore.thirdplatform.productconfig.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductConfigAddBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductConfigSearchBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.JdProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.*;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商户配置接口类
 *
 * <AUTHOR>
 */
public interface JdProductConfigService extends IService<JdProductConfig> {

    /**
     * 根据输入条件查询产品配置
     *
     * @param tenantCode
     * @param spuid
     * @param skuid
     * @param ppid
     * @return
     */
    JdProductConfig getOneProductConfigBy(String tenantCode, String spuid, String skuid, Integer ppid);

    /**
     * 新增保存产品配置
     *
     * @param productConfigBO
     * @return
     */
    R saveData(ProductConfigAddBO productConfigBO);

    /**
     * 分页查询商品配置信息
     *
     * @param search
     * @return
     */
    Page<ProductConfigVO> listByPage(ProductConfigSearchBO search);

    /**
     * 导入商品
     *
     * @param file
     * @return
     */
    R<Boolean> uploadData(MultipartFile file);

    /**
     * 清空配置数据
     * @param tenant 商户编码
     * @return
     */
    R<Boolean> clearData(String tenant);

    /**
     * 查询带库存的产品列表
     * @param storeCode
     * @return
     */
    List<ProductConfigVO> selectListWithStock(String storeCode);

    /**
     * 导出商品配置
     * @param req 搜索条件
     * @param response response
     * @return
     */
    R exportExcel(ProductConfigSearchBO req, HttpServletResponse response);

    R<Boolean> clearDataV2(ProductConfigClearReq req);

    Boolean syncCost(ProductConfigCostReq req);

    Page<JdProductTyingConfigPageVO>  listJdProductTyingConfigByPage(ProductConfigSearchBO search);

    R saveOrUpdateJdProductTyingConfig(JdProductTyingConfigVO productConfigBO);

    R saveOrUpdateJdProductTyingConfigBatch(List<JdProductTyingConfigVO> productConfigBO);

    R deleteJdProductTyingConfig(JdProductTyingConfigVO productConfigBO);

}
