package com.jiuji.oa.afterservice.cloud.service;

import com.alibaba.fastjson.JSONObject;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.vo.req.DisplayProductAddReq;
import com.jiuji.oa.afterservice.cloud.fallbackfactory.StockCloudFallbackFactory;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallOldProTransformDisplayReq;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@FeignClient(value = "OA-STOCK-SERVICE", path = "oa-stock", fallbackFactory = StockCloudFallbackFactory.class)
public interface StockCloud {

    /**
     * 小件旧件转陈列库存接口
     *
     * @param req
     * @return
     */
    @PostMapping("/api/stock/display-productinfo/add/v1")
    Result<String> smallOldProTransformDisplay(@RequestBody SmallOldProTransformDisplayReq req);


    /**
     * 小件旧件转陈列库存接口
     *
     * @param req
     * @return
     */
    @PostMapping("/api/stock/display-productinfo/add/v2")
    Result<DisplayProductAddReq> smallOldProTransformDisplayV2(@RequestBody SmallOldProTransformDisplayReq req);

    /**
     * 添加物流单
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/api/logistics-order/oa-add")
    R<Integer> addLogisticsOrder(@RequestHeader("Authorization") String authorization,@RequestBody JSONObject jsonObject);

}
