package com.jiuji.oa.afterservice.other.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wxsms")
public class Wxsms extends Model<Wxsms> {


    private static final long serialVersionUID = -7053452238959981287L;

    @TableId(value = "mainKey", type = IdType.AUTO)
    private Integer mainKey;

    private String openid;

    private LocalDateTime sendtime;

    private String msg;

    private Integer delivery;

    private String inuser;

    private String url;

    @TableField("type_")
    private Integer type;

    @TableField("subTitle")
    private String subTitle;

    private Integer userid;

    private Integer subId;

    private String mobile;

    @TableField("smsChannel")
    private Integer smsChannel;

    private Integer wxid;

    @Override
    protected Serializable pkVal() {
        return this.mainKey;
    }

}
