package com.jiuji.oa.afterservice.bigpro.statistics.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.statistics.bo.BrandCategoryBo;
import com.jiuji.oa.afterservice.bigpro.statistics.po.BrandCategory;
import com.jiuji.tc.common.vo.R;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
public interface BrandCategoryService extends IService<BrandCategory> {

    /**
     * 获取品牌分类
     * @return
     */
    @Cached(name = "com.jiuji.oa.afterservice.bigpro.service.statistics.getBrandCategoryList",expire = 200,timeUnit =
            TimeUnit.MINUTES)
    List<BrandCategoryBo> getBrandCategoryList();

    /**
     * 根据分类加载品牌
     * @param cids
     * @return
     */
    R<List<BrandCategoryBo>> loadBrandByCids(List<Integer> cids);

}
