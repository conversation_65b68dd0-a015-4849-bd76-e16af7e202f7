package com.jiuji.oa.oacore.operator.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.operator.po.OperatorExportLog;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date ${Date} 9:38
 * @Description
 */
@Mapper
public interface OperatorExportLogMapper extends BaseMapper<OperatorExportLog> {
    int batchInsert(@Param("list") List<OperatorExportLog> list);
}