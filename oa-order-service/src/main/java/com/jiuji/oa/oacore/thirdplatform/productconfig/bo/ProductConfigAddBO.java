/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.bo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商品关系配置新增业务实体
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "商品关系配置新增业务实体")
public class ProductConfigAddBO extends Model<ProductConfigAddBO> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    @ApiModelProperty(value = "商户编码")
    @NotNull(message = "商户编码不能为空")
    private String tenantCode;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    @ApiModelProperty(value = "平台编码")
    @NotNull(message = "平台编码不能为空")
    private String platCode;

    @ApiModelProperty(value = "商品明细")
    @NotNull(message = "商品明细不能为空")
    private List<ProductConfigItemBO> products;

    @Data
    @Accessors(chain = true)
    public static class ProductConfigItemBO implements Serializable {
        private static final long serialVersionUID = 2L;

        @ApiModelProperty(value = "平台商品skuid")
        public String productCode;

        /**
         * 平台商品skuid
         */
        @ApiModelProperty(value = "平台skuid")
        public String skuId;

        /**
         * 支持多个，多个之间有逗号间隔
         */
        @ApiModelProperty(value = "本地商品ppid")
        public String ppriceid;

        /**
         * 支持多个，多个之间有逗号间隔
         */
        @ApiModelProperty(value = "本地良品的mkcId")
        public Integer mkcId;

        /**
         * 配置标签
         */
        private Integer label;

        /**
         * 商户编码
         */
        private String tenantCode;
    }

}
