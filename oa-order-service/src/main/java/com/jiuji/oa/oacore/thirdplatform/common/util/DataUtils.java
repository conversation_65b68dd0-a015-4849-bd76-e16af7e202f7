package com.jiuji.oa.oacore.thirdplatform.common.util;

import cn.hutool.core.text.StrSpliter;
import com.ch999.common.util.utils.DateUtil;
import com.jiuji.oa.oacore.thirdplatform.common.enums.WeekDayEnum;
import com.jiuji.oa.oacore.thirdplatform.order.bo.BusinessHoursDTO;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import jodd.util.StringPool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DataUtils {

    private static final String COLON = ":";
    private static final String COMPAREENDTIME = "21:30";

    /**
     * 获取结束时间  判断结束时间是否大于21:30如果过大于这个时间取值为21:30
     *
     * @param endTime
     * @return
     */
    public static String getEndTime(String endTime) {
        Date dayWithTime = DateUtil.createDayWithTime(new Date(), endTime);
        Date compareEndTime = DateUtil.createDayWithTime(new Date(), COMPAREENDTIME);
        if (dayWithTime.before(compareEndTime)) {
            return endTime;
        } else {
            return COMPAREENDTIME;
        }
    }


    /**
     * 计算工作时间相关字段
     *
     * @param hours 单个工作时间段
     * @return 单个工作时间段实体
     */
    public static BusinessHoursDTO computeHourField(String hours) {
        List<BusinessHoursDTO> businessHoursDTOList = new ArrayList<>();
        String hourPattern = "(((周)|(星期))(?<WeekStart>[一二三四五六日1234567])\\s*[-至]\\s*((周)|(星期))(?<WeekEnd>[一二三四五六日1234567])\\s*)?((早)?(：)?(:)?\\s*(?<HourStart>\\d+)\\s*[:：]\\s*(?<MinuteStart>\\d+))\\s*[至-]\\s*((晚)?(?<HourEnd>\\d+)\\s*[:：]\\s*(?<MinuteEnd>\\d+))";
        List<String> hourList = getChecks(Pattern.CASE_INSENSITIVE, hours, hourPattern);
        if (CollectionUtils.isEmpty(hourList)) {
            return new BusinessHoursDTO();
        }
        for (String singleHour : hourList) {
            BusinessHoursDTO businessHoursDTO = new BusinessHoursDTO();
            String weekPattern = "((周)|(星期))([一二三四五六日1234567])";
            List<String> weekList = getChecks(Pattern.CASE_INSENSITIVE, singleHour, weekPattern);
            if (CollectionUtils.isNotEmpty(weekList) && weekList.size() == 2) {
                businessHoursDTO.setWeekStart(weekList.get(0));
                businessHoursDTO.setWeekEnd(weekList.get(1));
            }
            String workTimePattern = "((\\d+)\\s*[:：]\\s*(\\d+))";
            List<String> workTimeList = getChecks(Pattern.CASE_INSENSITIVE, singleHour, workTimePattern);
            if (CollectionUtils.isNotEmpty(workTimeList) && workTimeList.size() == 2) {
                String startTime = workTimeList.get(0);
                String[] splitStartTime = startTime.split(COLON);
                if (splitStartTime.length < 2) {
                    continue;
                }
                String startHour = splitStartTime[0];
                String startMinute = splitStartTime[1];
                if (startHour.length() < 2) {
                    //补全小时字符串长度
                    startHour = StringUtils.leftPad(startHour, 2, "0");
                }
                businessHoursDTO.setStartTime(startHour + COLON + startMinute);
                String endTime = workTimeList.get(1);
                String[] splitEndTime = endTime.split(COLON);
                String endHour = splitEndTime[0];
                String endMinute = splitEndTime[1];

                if (endHour.length() < 2) {
                    //补全小时字符串长度
                    endHour = StringUtils.leftPad(endHour, 2, "0");
                }
                businessHoursDTO.setEndTime(endHour + COLON + endMinute);
            }
            businessHoursDTOList.add(businessHoursDTO);
        }
        if (businessHoursDTOList.size() == NumberConstant.ONE) {
            return businessHoursDTOList.get(NumberConstant.ZERO);
        } else {
            //如果时间段有多个那就需要取闭店时间最晚的一个
            return getBusinessHoursDTO(businessHoursDTOList);
        }
    }


    /**
     * 计算工作时间相关字段
     *
     * @param hours 单个工作时间段
     * @return 单个工作时间段实体
     */
    public static boolean isOnBusinessHours(String hours) {
        //9:00-22:00
        // 未营业
        // 早9：00至晚9：00
        // 周日-周四10:00-22:00  周五-周六10:00-22:30
        // 周一至周日 10:00-22:00
        List<BusinessHoursDTO> workWeek = new ArrayList<>();
        String hourPattern = "(((周)|(星期))(?<WeekStart>[一二三四五六日1234567])\\s*[-至]\\s*((周)|(星期))(?<WeekEnd>[一二三四五六日1234567])\\s*)?((早)?(：)?(:)?\\s*(?<HourStart>\\d+)\\s*[:：]\\s*(?<MinuteStart>\\d+))\\s*[至-]\\s*((晚)?(?<HourEnd>\\d+)\\s*[:：]\\s*(?<MinuteEnd>\\d+))";
        List<String> hourList = getChecks(Pattern.CASE_INSENSITIVE, hours, hourPattern);
        if (CollectionUtils.isEmpty(hourList)) {
            return false;
        }
        for (String singleHour : hourList) {
            BusinessHoursDTO businessHoursDTO = computeHourFieldSingle(singleHour);
            String weekStart = businessHoursDTO.getWeekStart();
            String weekEnd = businessHoursDTO.getWeekEnd();
            String startTime = businessHoursDTO.getStartTime();
            String endTime = businessHoursDTO.getEndTime();
            int[] weekList;
            if (StringUtils.isEmpty(weekStart)
                    && StringUtils.isEmpty(weekEnd)) {
                // 全周上班
                weekList = new int[]{0, 1, 2, 3, 4, 5, 6};
            } else {
                //时间段上班
                weekList = getWeekFromRange(weekStart, weekEnd);
            }
            for (int t : weekList) {
                BusinessHoursDTO temp = new BusinessHoursDTO();
                temp.setWeek(t);
                temp.setStartTime(startTime);
                temp.setEndTime(endTime);
                workWeek.add(temp);
            }
        }
        //判断当前是否在营业中
        LocalDateTime now = LocalDateTime.now();
        int week = now.getDayOfWeek().getValue();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = dateFormatter.format(now);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        BusinessHoursDTO businessHoursDTO = workWeek.stream()
                .filter((BusinessHoursDTO x) -> String.valueOf(week).equals(x.getWeek().toString()) || week == DayOfWeek.SUNDAY.getValue() && Objects.equals(x.getWeek(),0))
                .findFirst().orElse(null);
        if (!Objects.isNull(businessHoursDTO)) {
            String startTime = String.format("%s %s:00", date, businessHoursDTO.getStartTime());
            LocalDateTime startTimeLocalDateTime = LocalDateTime.parse(startTime, dateTimeFormatter);
            String endTime = String.format("%s %s:00", date, businessHoursDTO.getEndTime());
            LocalDateTime endTimeLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);
            if (now.isAfter(startTimeLocalDateTime) && now.isBefore(endTimeLocalDateTime)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 计算工作时间相关字段
     *
     * @param singleHour 单个工作时间段
     * @return 单个工作时间段实体
     */
    private static BusinessHoursDTO computeHourFieldSingle(String singleHour) {
        BusinessHoursDTO businessHoursDTO = new BusinessHoursDTO();
        String weekPattern = "((周)|(星期))([一二三四五六日1234567])";
        List<String> weekList = getChecks(Pattern.CASE_INSENSITIVE, singleHour, weekPattern);
        if (CollectionUtils.isNotEmpty(weekList) && weekList.size() == 2) {
            businessHoursDTO.setWeekStart(weekList.get(0));
            businessHoursDTO.setWeekEnd(weekList.get(1));
        }
        String workTimePattern = "((\\d+)\\s*[:：]\\s*(\\d+))";
        List<String> workTimeList = getChecks(Pattern.CASE_INSENSITIVE, singleHour, workTimePattern);
        if (CollectionUtils.isNotEmpty(workTimeList) && workTimeList.size() == 2) {
            Function<String, List<String>> timeSplitFun = timeStr -> StrSpliter.splitByRegex(timeStr, "[:：]", -1, true, true);
            String startTime = workTimeList.get(0);
            List<String> splitStartTime = timeSplitFun.apply(startTime);
            String startHour = splitStartTime.get(0);
            String startMinute = splitStartTime.get(1);
            if (startHour.length() < 2) {
                //补全小时字符串长度
                startHour = StringUtils.leftPad(startHour, 2, "0");
            }
            businessHoursDTO.setStartTime(startHour + StringPool.COLON + startMinute);
            String endTime = workTimeList.get(1);
            List<String> splitEndTime = timeSplitFun.apply(endTime);
            String endHour = splitEndTime.get(0);
            String endMinute = splitEndTime.get(1);

            if (endHour.length() < 2) {
                //补全小时字符串长度
                endHour = StringUtils.leftPad(endHour, 2, "0");
            }
            businessHoursDTO.setEndTime(endHour + StringPool.COLON + endMinute);
        }
        return businessHoursDTO;
    }


    /**
     * 从周范围获取周列表（比如 周日至周五==》[0,1,2,3,4,5]
     *
     * @param weekStart 开始
     * @param weekEnd   结束
     * @return 周列表
     */
    private static int[] getWeekFromRange(String weekStart, String weekEnd) {
        weekStart = weekStart.substring(weekStart.length() - 1);
        weekEnd = weekEnd.substring(weekEnd.length() - 1);
        List<Integer> weekList = new ArrayList<>();
        WeekDayEnum weekStartEnum = EnumUtil.getEnumByMessage(WeekDayEnum.class, weekStart);
        WeekDayEnum weekEndEnum = EnumUtil.getEnumByMessage(WeekDayEnum.class, weekEnd);

        if (Objects.isNull(weekStartEnum) || Objects.isNull(weekEndEnum)) {
            return new int[0];
        }

        LinkedList<String> weekLabelLinkedList = EnumUtil.toEnumVOList(WeekDayEnum.class).stream().map(EnumVO::getLabel).collect(Collectors.toCollection(LinkedList::new));
        int index = weekLabelLinkedList.indexOf(weekStart);
        for (int i = 0; i < index; i++) {
            weekLabelLinkedList.offer(weekLabelLinkedList.poll());
        }

        Iterator<String> iterator = weekLabelLinkedList.iterator();

        while (iterator.hasNext()) {
            String next = iterator.next();
            WeekDayEnum nextEnum = EnumUtil.getEnumByMessage(WeekDayEnum.class, next);
            if (Objects.isNull(nextEnum)) {
                break;
            }
            weekList.add(nextEnum.getCode());
            if (next.equals(weekEnd)) {
                break;
            }
        }


        return weekList.stream().mapToInt(Integer::valueOf).toArray();
    }


    /**
     * 获取闭店时间最短营业时间
     *
     * @param businessHoursDTOList
     * @return
     */
    private static BusinessHoursDTO getBusinessHoursDTO(List<BusinessHoursDTO> businessHoursDTOList) {
        for (BusinessHoursDTO item : businessHoursDTOList) {
            String endTime = item.getEndTime();
            Date dayWithTime = DateUtil.createDayWithTime(new Date(), endTime);
            item.setSortValue(dayWithTime);
        }
        List<BusinessHoursDTO> collect = businessHoursDTOList.stream()
                .sorted(Comparator.comparing(BusinessHoursDTO::getSortValue))
                .collect(Collectors.toList());
        return collect.get(0);
    }


    /**
     * 获取字符串中满足正则的所有字符串
     *
     * @param content the content
     * @param reg     the reg
     * @return checks checks
     */
    public static List<String> getChecks(int patternType, String content, String reg) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(reg)) {
            return new ArrayList<>();
        }
        Pattern r = Pattern.compile(reg, patternType);
        Matcher m = r.matcher(content);
        List<String> resullt = new ArrayList<>();
        while (m.find()) {
            resullt.add(m.group());
        }
        return resullt;
    }

}
