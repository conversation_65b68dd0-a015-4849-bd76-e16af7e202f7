
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=**************
sqlserver.ch999oanew.port=1433
sqlserver.ch999oanew.dbname=ch999oanew__10054
sqlserver.ch999oanew.username=ch999oanew__10054
sqlserver.ch999oanew.password=ch999oanewkGCVE
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=**************
sqlserver.ch999oanewReport.port=1433
sqlserver.ch999oanewReport.dbname=ch999oanew__10054
sqlserver.ch999oanewReport.username=ch999oanew__10054
sqlserver.ch999oanewReport.password=ch999oanewkGCVE

## sqlserver:ershou
sqlserver.ershou.host=sqlserver.serv.xn.saas.ch999.cn
sqlserver.ershou.port=1433
sqlserver.ershou.dbname=ershou__10054
sqlserver.ershou.username=ershou__10054
sqlserver.ershou.password=ershouFjgMx

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=**************
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oanew__10054
sqlserver.ch999oanewHis.username=ch999oanew__10054
sqlserver.ch999oanewHis.password=ch999oanewkGCVE

## sqlserver:office
sqlserver.office.host=**************
sqlserver.office.port=1433
sqlserver.office.dbname=office__10054
sqlserver.office.username=office__10054
sqlserver.office.password=officeCKBvC

## sqlserver:officeWrite
sqlserver.officeWrite.host=**************
sqlserver.officeWrite.port=1433
sqlserver.officeWrite.dbname=office__10054
sqlserver.officeWrite.username=office__10054
sqlserver.officeWrite.password=officeCKBvC

## sqlserver:smallpro_write
sqlserver.smallpro_write.host=**************
sqlserver.smallpro_write.port=1433
sqlserver.smallpro_write.dbname=ch999oanew__10054
sqlserver.smallpro_write.username=ch999oanew__10054
sqlserver.smallpro_write.password=ch999oanewkGCVE

## mysql:oa_core
mysql.oa_core.url=**************:3306
mysql.oa_core.dbname=oa_core__10054
mysql.oa_core.username=oa_core__10054
mysql.oa_core.password=oa_coreOKE

## midl
consul.host=**************
consul.port=8500

## redis
redis.oa.host=**************
redis.oa.port=6382
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

rabbitmq.master.url=**************
rabbitmq.master.port=5672
rabbitmq.master.vhost=oaAsync__10054
rabbitmq.master.username=oaAsync__10054
rabbitmq.master.password=HtVWQ



logging.config.path=classpath:log/log4j2-saas.xml
## image
image.upload.url=http://**************:9333
image.del.url=http://**************:5083
image.select.url=https://img.dyph.vip/
jiuji.sys.xtenant=10054
spring.cloud.consul.discovery.instance-zone=10054
instance-zone=10054

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100


sms.url=http://sms.dyph.vip/?test=
sms.send.email.url=http://sms.dyph.vip/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx
mongodb.url1=*********************************************************************

jiuji.seata.enabled=false
jiuji.seata.config.consul.address=xxx
jiuji.seata.config.consul.prefix=xxx
jiuji.seata.registry.consul.address=xxx
jiuji.seata.registry.consul.cluster=xxx