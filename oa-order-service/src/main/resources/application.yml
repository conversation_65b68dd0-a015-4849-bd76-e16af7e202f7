server:
  port: 11005
  servlet:
    context-path: /orderservice
  tomcat:
    connection-timeout: 180000

eureka:
  client:
    enabled: false

spring:
  profiles:
    active: iteng
  application:
    name: orederservice
  messages:
    basePackages: com.jiuji.oa.oacore.common.source
    basename: i18n/url
  #    basename: ${spring.messages.basename}
  main:
    allow-bean-definition-overriding: true
  cloud:
    inetutils:
      ignored-interfaces:
        - docker0
        - veth.*
      timeout-seconds: 5
    consul:
      enable: true
      config:
        enable: true
        prefix: seata-config
        default-context: orderservice
        profile-separator: '-'
        watch:
          enabled: true
          # 每间隔5秒刷新
          delay: 5000
      discovery:
        register: true #注册到consul
        prefer-ip-address: true
        instance-group: green
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.instance-zone}-${spring.cloud.consul.discovery.instance-group}-${spring.cloud.client.hostname}-${server.port}
        #健康检查失败多久强制取消服务注册
        health-check-critical-timeout: 120s
        #heartbeat:
        #  enabled: true
        tags: traefik.frontend.rule=Host:oa-orderservice
        health-check-url: http://${spring.cloud.client.ipaddress}:${server.port}/${server.servlet.context-path}/actuator/info
        default-query-tag: zone=${spring.cloud.consul.discovery.instance-zone}
        instance-zone: ${instance-zone}
      host: ${consul.host}
      port: ${consul.port}

  datasource:
    default:
      url: jdbc:sqlserver://${sqlserver.ch999oanew.host}:${sqlserver.ch999oanew.port};databaseName=${sqlserver.ch999oanew.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanew.username}
      password: ${sqlserver.ch999oanew.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      max-pool-size: ${sqlserver.datasource.max-pool-size}
      data-source-type: com.jiuji.oa.oacore.common.config.db.MyDynamicDataSource
    hikari:
      maximum-pool-size: 50
      minimum-idle: 5
      connection-test-query: SELECT 1
  redis:
    host: ${redis.oa.host}
    port: ${redis.oa.port}
    password: ${redis.oa.password}
    jedis:
      pool:
        max-active: 128
        max-idle: 16
        min-idle: 0
    timeout: 10s
  servlet:
    multipart:
      max-request-size: 300MB
      max-file-size: 200MB

sqlserver:
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    type: com.zaxxer.hikari.HikariDataSource
mysql:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource

# mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  typeAliasesPackage: com.jiuji.oa.oacore.**.po
  #    typeEnumsPackage: com.ch999.web.jiuji.entity.enums
  global-config:
    db-config:
      id-type: none
      field-strategy: not_empty
      table-underline: true #新项目用true好点，由于历史原因这里用false
      logic-delete-value: 1
      logic-not-delete-value: 0
    sql-parser-cache: true #这个要开启，否则@SqlParser无效
  configuration:
    map-underscore-to-camel-case: true #新项目用true好点，由于历史原因这里用false
    cache-enabled: false
# 数据库配置
dynamic:
  datasource:
    oacore:
      url: jdbc:mysql://${mysql.oa_core.url}/${mysql.oa_core.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${mysql.oa_core.username}
      password: ${mysql.oa_core.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}
    oa_nc:
      url: jdbc:mysql://${mysql.oa_nc.url}/${mysql.oa_nc.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${mysql.oa_nc.username}
      password: ${mysql.oa_nc.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}
    oa_log:
      url: jdbc:mysql://${mysql.oa_log.url}/${mysql.oa_log.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${mysql.oa_log.username}
      password: ${mysql.oa_log.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}
    oanew:
      url: jdbc:sqlserver://${sqlserver.ch999oanew.host}:${sqlserver.ch999oanew.port};databaseName=${sqlserver.ch999oanew.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanew.username}
      password: ${sqlserver.ch999oanew.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    oanew2:
      url: jdbc:sqlserver://${sqlserver.ch999oanew2.host:}:${sqlserver.ch999oanew2.port:};databaseName=${sqlserver.ch999oanew2.dbname:};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanew2.username:}
      password: ${sqlserver.ch999oanew2.password:}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      is-enable: ${sqlserver.ch999oanew2.is-enable:false}
    oanewWrite:
      url: jdbc:sqlserver://${sqlserver.oanewWrite.host}:${sqlserver.oanewWrite.port};databaseName=${sqlserver.oanewWrite.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.oanewWrite.username}
      password: ${sqlserver.oanewWrite.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      max-pool-size: ${sqlserver.datasource.max-pool-size}
    oanew_his:
      url: jdbc:sqlserver://${sqlserver.ch999oanewHis.host}:${sqlserver.ch999oanewHis.port};databaseName=${sqlserver.ch999oanewHis.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanewHis.username}
      password: ${sqlserver.ch999oanewHis.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      is-enable: ${sqlserver.ch999oanewHis.is-enable:false}
    officeWrite:
      url: jdbc:sqlserver://${sqlserver.officeWrite.host}:${sqlserver.officeWrite.port};databaseName=${sqlserver.officeWrite.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.officeWrite.username}
      password: ${sqlserver.officeWrite.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    smallpro_write:
      url: jdbc:sqlserver://${sqlserver.smallpro_write.host}:${sqlserver.smallpro_write.port};databaseName=${sqlserver.smallpro_write.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.smallpro_write.username}
      password: ${sqlserver.smallpro_write.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    office:
      url: jdbc:sqlserver://${sqlserver.office.host}:${sqlserver.office.port};databaseName=${sqlserver.office.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.office.username}
      password: ${sqlserver.office.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      max-pool-size: ${sqlserver.datasource.max-pool-size}
    office2:
      url: jdbc:sqlserver://${sqlserver.office2.host:}:${sqlserver.office2.port:};databaseName=${sqlserver.office2.dbname:};applicationName=java_${spring.application.name}
      username: ${sqlserver.office2.username:}
      password: ${sqlserver.office2.password:}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      max-pool-size: ${sqlserver.datasource.max-pool-size}
      is-enable: ${sqlserver.office2.is-enable:false}
    oanewHis:
      url: jdbc:sqlserver://${sqlserver.oanewHis.host}:${sqlserver.oanewHis.port};databaseName=${sqlserver.oanewHis.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.oanewHis.username}
      password: ${sqlserver.oanewHis.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      is-enable: ${sqlserver.oanewHis.is-enable:false}
    oanew_his_write:
      url: jdbc:sqlserver://${sqlserver.oanewHisWrite.host:}:${sqlserver.oanewHisWrite.port:};databaseName=${sqlserver.oanewHisWrite.dbname:};applicationName=java_${spring.application.name}
      username: ${sqlserver.oanewHisWrite.username:}
      password: ${sqlserver.oanewHisWrite.password:}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      max-pool-size: ${sqlserver.datasource.max-pool-size}
      is-enable: ${sqlserver.oanewHisWrite.is-enable:false}
    web999_other:
      url: jdbc:sqlserver://${sqlserver.web999_other.host}:${sqlserver.web999_other.port};databaseName=${sqlserver.web999_other.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.web999_other.username}
      password: ${sqlserver.web999_other.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    Financial:
      url: jdbc:sqlserver://${sqlserver.Financial.host}:${sqlserver.Financial.port};databaseName=${sqlserver.Financial.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.Financial.username}
      password: ${sqlserver.Financial.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    ch999oanewReport:
      url: jdbc:sqlserver://${sqlserver.ch999oanewReport.host}:${sqlserver.ch999oanewReport.port};databaseName=${sqlserver.ch999oanewReport.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanewReport.username}
      password: ${sqlserver.ch999oanewReport.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    ershou:
      url: jdbc:sqlserver://${sqlserver.ershou.host}:${sqlserver.ershou.port};databaseName=${sqlserver.ershou.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ershou.username}
      password: ${sqlserver.ershou.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    starRocks:
      username: ${starrocks.username}
      password: ${starrocks.password}
      url: jdbc:mysql://${starrocks.url}/${starrocks.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      driver-class-name: ${mysql.datasource.driver-class-name}
      type: ${mysql.datasource.type}
      max-pool-size: ${mysql.datasource.max-pool-size}
    web999:
      url: jdbc:sqlserver://${sqlserver.web999.host}:${sqlserver.web999.port};databaseName=${sqlserver.web999.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.web999.username}
      password: ${sqlserver.web999.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
# jetcache缓存配置
jetcache:
  statIntervalMinutes: 60
  areaInCacheName: false
  hiddenPackages: com.jiuji
  local:
    default:
      type: caffeine
      limit: 100
      keyConvertor: fastjson
      expireAfterWriteInMillis: 3600000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: kryo
      valueDecoder: kryo
      expireAfterWriteInMillis: 21600000
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      uri: redis://${redis.oa.url}/

#图片服务器地址
image:
  uploadImgUrl: ${image.upload.url}
  delImgUrl: ${image.del.url}
  selectImgUrl: ${image.select.url}

logging:
  config: ${logging.config.path}

# Ribbon
ribbon:
  ReadTimeout: 120000 # 请求处理的超时时间
  ConnectTimeout: 5000 # 请求连接的超时时间

# MQ配置
rabbitmq:
  multiple:
    oaAsync:
      host: ${rabbitmq.master.url}
      port: ${rabbitmq.master.port}
      username: ${rabbitmq.master.username}
      password: ${rabbitmq.master.password}
      virtual-host: ${rabbitmq.master.vhost}
    oa:
      host: ${rabbitmq.oa.url:${rabbitmq.master.url}}
      port: ${rabbitmq.oa.port:${rabbitmq.master.port}}
      username: ${rabbitmq.oa.username:${rabbitmq.master.username}}
      password: ${rabbitmq.oa.password:${rabbitmq.master.password}}
      virtual-host: ${rabbitmq.oa.vhost:${rabbitmq.master.vhost}}

#jvm监控数据上报配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    tags:
      application: ${spring.application.name}

# mongodb
mongodb:
  url:
    ch999oa: ${mongodb.url1}

# 应用配置
#9机短信接口地址
sms:
  url: ${sms.url}
  sendEmailUrl: ${sms.send.email.url}
  sendInUrl: ${sms.send.in.url}
  #短信平台地址
  platformUrl: https://www.9xun.com

mqtt:
  host: tcp://************:2883
  topic: oa2/nc-segments
  clientinid: nc-segments-${random.value}
  qoslevel: 1
  username: 9xunyun
  password: rPvvDZM9
  timeout: 10000
  keepalive: 20
  adminUrl: http://************:8081/api/v4
  adminUser: admin
  adminPassword: public
meituan:
  shangou:
    appId:${meituan.shangou.appId}
    appSecret:${meituan.shangou.appSecret}

seata:
  enabled: ${jiuji.seata.enabled}
  enable: ${jiuji.seata.enabled}
  enable-auto-data-source-proxy: false
  tx-service-group: jiuji_tx_group
  config:
    type: file
  registry:
    type: consul
    consul:
      cluster: ${jiuji.seata.registry.consul.cluster}
      server-addr: ${jiuji.seata.registry.consul.address}
  service:
    vgroup-mapping:
      jiuji_tx_group: ${jiuji.seata.registry.consul.cluster}

app:
  id: oa-orderService
apollo:
  meta: ${apollo.url:}
  bootstrap:
    enabled: true
    namespaces: ${apollo.file:}
#延迟队列配置 生产环境
lmstfy:
  host: "lmstfy.service.ch999.cn"
  namespace:
  token:
  # 重试次数
  retryTimes: 2
  # 重试时间间隔（单位：毫秒）
  retryIntervalMilliseconds: 1000
  # 读 超时时间（单位：秒）
  readTimeoutSecond: 5
  # 写 超时时间（单位：秒）
  writeTimeoutSecond: 5
  # 连接 超时时间（单位：秒）
  connectTimeoutSecond: 5
  mult:
    first-lmstfy-client:
      namespace: oa_order
      token: "01H9FPJXCSFJ25D2EX3DZB1HR2"

active-instance-zone: ${active-zone:${instance-zone}}


# 汇机保url
huiji:
  url: https://openapi.huijb.cn
  appKey: jjtest2
  appSecret: f54a0ce634ee524b2bd2aaccf937d688
feign:
  hystrix:
    enabled: true
    exclude-client-names:
  client:
    config:
      default:
        connectTimeout: 120000
        readTimeout: 120000

hystrix:
  command:
    default:
      execution:
        timeoutInMilliseconds: 120000
        isolation:
          strategy: SEMAPHORE
          maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 120000

taobao:
  api_url: https://eco.taobao.com/router/rest
  msg_url: ws://mc.api.taobao.com/

