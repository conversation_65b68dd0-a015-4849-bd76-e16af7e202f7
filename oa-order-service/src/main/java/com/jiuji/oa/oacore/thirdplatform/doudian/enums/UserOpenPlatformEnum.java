/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.oa.oacore.thirdplatform.doudian.enums;

/**
 * 用户三方平台枚举.
 */
public enum UserOpenPlatformEnum {
    MeiTuan(1, "<PERSON>Tuan"),
    TIK<PERSON><PERSON>(2, "Tik<PERSON>"),
    ;

    private int type;
    private String name;

    UserOpenPlatformEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static UserOpenPlatformEnum getByName(String name) {
        for (UserOpenPlatformEnum platformPortEnum : values()) {
            if (platformPortEnum.name.equals(name)) {
                return platformPortEnum;
            }
        }
        return null;
    }
}
