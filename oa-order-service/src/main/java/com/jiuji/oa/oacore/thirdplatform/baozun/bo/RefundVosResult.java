package com.jiuji.oa.oacore.thirdplatform.baozun.bo;

import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 退款VO结果封装对象
 * <AUTHOR>
 * @since 2024/07/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RefundVosResult {
    /**
     * 当前三方原路径退款列表
     */
    private List<ThirdOriginRefundVo> thirdOriginRefundVos;
    
    /**
     * 当前刷卡原路径退款列表
     */
    private List<CardOriginRefundVo> cardOriginRefundVos;
    
    /**
     * 当前其他退款列表
     */
    private List<OtherRefundVo> otherRefundVos;
}
