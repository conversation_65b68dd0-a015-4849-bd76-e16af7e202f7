<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.vo.OrderVO">
        <id column="id" property="id"/>
        <result column="plat_code" property="platCode"/>
        <result column="order_id" property="orderId"/>
        <result column="order_time" property="orderTime"/>
        <result column="org_code" property="tenantCode"/>
        <result column="area_id" property="areaId"/>
        <result column="area_code2" property="areaCode"/>
        <result column="store_code" property="storeCode"/>
        <result column="buyer_pin" property="buyerPin"/>
        <result column="buyer_name" property="buyerName"/>
        <result column="buyer_address" property="buyerAddress"/>
        <result column="buyer_mobile" property="buyerMobile"/>
        <result column="buyer_tel" property="buyerTel"/>
        <result column="trade_time" property="tradeTime"/>
        <result column="order_time" property="orderTime"/>
        <result column="total_money" property="totalMoney"/>
        <result column="discount_money" property="discountMoney"/>
        <result column="good_money" property="goodMoney"/>
        <result column="point_money" property="pointMoney"/>
        <result column="freight_money" property="freightMoney"/>
        <result column="payable_money" property="payableMoney"/>
        <result column="buyer_city" property="buyerCity"/>
        <result column="buyer_city_name" property="buyerCityName"/>
        <result column="buyer_country" property="buyerCountry"/>
        <result column="buyer_country_name" property="buyerCountryName"/>
        <result column="buyer_remark" property="buyerRemark"/>
        <result column="order_status" property="orderStatus"/>
        <result column="sub_id" property="subId"/>
        <result column="sub_message" property="subMessage"/>
        <result column="pay_status" property="payStatus"/>
        <result column="vender_money" property="venderMoney"/>
        <result column="plat_money" property="platMoney"/>
        <result column="sub_check" property="subCheck"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="area_name" property="areaName"/>
        <result column="cancel_reason" property="cancelReason"/>
    </resultMap>

    <update id="updateStatusAndMessageById">
        update third_platform_order
        <set>
            <if test="payStatus != null">
                pay_status = #{payStatus},
            </if>
            <if test="subId != null">
                sub_id = #{subId},
            </if>
            sub_message = #{message},
        </set>
        where id=#{id}
    </update>

    <update id="updateSubAddress">
        update SubAddress set paisongState = 4 where sub_id = #{subId} and paisongState &lt;&gt; 4
    </update>

    <select id="orderList" resultMap="BaseResultMap">
        SELECT t.* ,s.sub_check,a.area as area_code2,a.area_name,b.tenant_name FROM third_platform_order t WITH(NOLOCK)
         LEFT JOIN sub s with(nolock) ON t.sub_id =s.sub_id
         LEFT JOIN areainfo a WITH(NOLOCK) ON a.id=t.area_id
         LEFT JOIN third_platform_tenant b WITH(NOLOCK) ON b.tenant_code=t.org_code
        <where>
            <if test="search.platCode != null ">
                AND t.plat_code = #{search.platCode}
            </if>
            <if test="search.subCheck!=null">
                AND s.sub_check = #{search.subCheck}
            </if>
            <if test="search.startTime!=null">
                AND t.order_time >= #{search.startTime}
            </if>
            <if test="search.endTime!=null">
                AND t.order_time &lt;= #{search.endTime}
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 1">
                AND ISNULL(t.cancel_check,0) = 1
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 0">
                AND ISNULL(t.cancel_check,0) != 1
            </if>
            <if test="search.type != null">
                AND ISNULL(t.type,0) = #{search.type}
            </if>
            <!-- 1-商户号 2-平台订单号 3-平台门店 4-本地订单号 -->
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND t.org_code = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 2">
                        AND t.order_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 3">
                        AND t.store_code  = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 4">
                        AND t.sub_id  = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 5">
                        AND t.buyer_remark  like CONCAT('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 0">
                AND t.sub_id is null and DATEDIFF(MINUTE,t.order_time ,GETDATE())>5 and isnull(t.generate_manual_sub_flag,0) = 0
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 1">
                AND t.sub_id is not null and isnull(t.generate_manual_sub_flag,0) = 1
            </if>
        </where>
    </select>
    <select id="queryUserXSelectYCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT b.sub_id) FROM dbo.basket_activity_log l WITH(NOLOCK)
                                                 INNER JOIN dbo.basket b WITH(NOLOCK) ON l.basket_id = b.basket_id
                                                 INNER JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        WHERE s.userid = #{userId} AND l.act_id = #{actId} AND ISNULL(b.isdel,0) = 0 AND s.sub_check IN (0,1,2,3,5,6,7)
    </select>

    <select id="queryUserXSelectYCountList" resultType="com.jiuji.oa.oacore.thirdplatform.order.vo.UserXSelectYActInfo">
        SELECT
            l.act_id as actId,
            COUNT ( DISTINCT b.sub_id ) countNum
        FROM
            dbo.basket_activity_log l WITH ( NOLOCK )
            INNER JOIN dbo.basket b WITH ( NOLOCK ) ON l.basket_id = b.basket_id
            INNER JOIN dbo.sub s WITH ( NOLOCK ) ON s.sub_id = b.sub_id
        WHERE
            s.userid = #{userId}
            AND l.act_id IN
                <foreach item="id" collection="actIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            AND ISNULL( b.isdel, 0 ) = 0
            AND s.sub_check IN ( 0, 1, 2, 3, 5, 6, 7 )
        GROUP BY
            l.act_id
    </select>


    <select id="getRealInventory" resultType="com.jiuji.oa.oacore.thirdplatform.order.bo.ProductKcBO">
        SELECT k.areaid as areaId,k.leftCount,k.ppriceid FROM dbo.product_kc k WITH ( NOLOCK ) WHERE k.areaid in
        <foreach item="areaid" collection="areaIds"  open="(" separator="," close=")">
            #{areaid}
        </foreach>
        AND k.ppriceid IN
        <foreach item="ppid" collection="ppriceids" open="(" separator="," close=")">
            #{ppid}
        </foreach>
        UNION ALL
        SELECT k.areaid as areaId,k.leftCount, k.ppriceid FROM
        (SELECT k.areaid, k.ppriceid, COUNT ( * ) leftCount FROM dbo.product_mkc k WITH ( NOLOCK )
        WHERE kc_check = 3 AND isnull( k.mouldFlag, 0 ) = 0 and k.basket_id is null
        AND NOT EXISTS (SELECT 1 FROM dbo.xc_mkc xc WITH ( NOLOCK )
        WHERE xc.mkc_id= k.id
        AND isnull( xc.isLock, 0 ) = 1 )
        GROUP BY k.areaid,k.ppriceid ) k
        WHERE k.areaid in
        <foreach item="areaid" collection="areaIds" open="(" separator="," close=")">
            #{areaid}
        </foreach>
        AND k.ppriceid IN
        <foreach item="ppid" collection="ppriceids" open="(" separator="," close=")">
            #{ppid}
        </foreach>
    </select>

    <select id="getPersonneList" resultType="java.lang.Integer">
        select top 20 u.ch999_id,u.zhiwu
        from ch999_user u WITH(NOLOCK)
        LEFT JOIN zhiwu z WITH(NOLOCK) ON u.zhiwuid = z.id where u.iszaizhi = 1
        and ISNULL(u.isshixi, 0) &lt;&gt; 4
        and u.area1id = #{areaId}
        and u.zhiwu in('店长','主管', '副店长','门店主管')
        order by z.leve, u.ch999_id
    </select>

    <select id="getAllPersonneList" resultType="java.lang.Integer">
        select top 20 u.ch999_id,u.zhiwu
        from ch999_user u WITH(NOLOCK)
        LEFT JOIN zhiwu z WITH(NOLOCK) ON u.zhiwuid = z.id where u.iszaizhi = 1
        and ISNULL(u.isshixi, 0) &lt;&gt; 4
        and u.area1id = #{areaId}
        order by z.leve, u.ch999_id
    </select>

    <select id="recoverOrderList" resultMap="BaseResultMap">
        SELECT t.* ,s.sub_check,a.area as area_code2,a.area_name,b.tenant_name FROM third_platform_order t WITH(NOLOCK)
        LEFT JOIN recover_marketInfo s with(nolock) ON t.sub_id =s.sub_id
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id=t.area_id
        LEFT JOIN third_platform_tenant b WITH(NOLOCK) ON b.tenant_code=t.org_code
        <where>
            <if test="search.platCode != null ">
                AND t.plat_code = #{search.platCode}
            </if>
            <if test="search.subCheck!=null">
                AND s.sub_check = #{search.subCheck}
            </if>
            <if test="search.startTime!=null">
                AND t.order_time >= #{search.startTime}
            </if>
            <if test="search.endTime!=null">
                AND t.order_time &lt;= #{search.endTime}
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 1">
                AND ISNULL(t.cancel_check,0) = 1
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 0">
                AND ISNULL(t.cancel_check,0) != 1
            </if>
            <if test="search.type != null">
                AND ISNULL(t.type,0) = #{search.type}
            </if>
            <!-- 1-商户号 2-平台订单号 3-平台门店 4-本地订单号 -->
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND t.org_code = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 2">
                        AND t.order_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 3">
                        AND t.store_code  = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 4">
                        AND t.sub_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 5">
                        AND t.buyer_remark like CONCAT('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 0">
                AND t.sub_id is null and DATEDIFF(MINUTE,t.order_time ,GETDATE())>5 and isnull(t.generate_manual_sub_flag,0) = 0
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 1">
                AND t.sub_id is not null and isnull(t.generate_manual_sub_flag,0) = 1
            </if>
        </where>
    </select>

    <select id="getListOrderById" resultType="com.jiuji.oa.oacore.thirdplatform.order.entity.Order">
        select *, org_code as tenantCode
        from third_platform_order WITH(NOLOCK)
        where order_id = #{orderId} and plat_code = 'DY'
    </select>

    <select id="getGiftPpidList" resultType="java.lang.Integer">
        select ppriceids from productGift pg with (nolock)
        <where> pg.ppriceid IN
            <foreach item="id" collection="ppriceIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="getLogisticsByOrderId"
            resultType="com.jiuji.oa.oacore.thirdplatform.order.bo.OrderLogisticsBO">
        select top 1 tpo.order_id orderId,
               tpo.id,
               tpo.biz_order_id bizOrderId,
               tpo.store_code storeId,
               tpo.area_id areaId,
               tpo.org_code tenantCode,
               tpo.sub_id subId,
               s.sub_check subCheck,
               s.delivery,
               w.id wuliuId,
               w.com,
               w.nu
        from third_platform_order tpo with(nolock)
        left join sub s with(nolock) on tpo.sub_id = s.sub_id
        left join wuliu w with(nolock) on s.sub_id = w.danhaobind and w.wutype in (4,6) and w.stats in (1,2,3,7)
        where tpo.order_id = #{orderId}
        and tpo.plat_code = #{platCode}
    </select>
    <select id="getImeiListBySubId" resultType="java.lang.String">
        select m.imei from sub s with(nolock)
        left join basket b with(nolock) on s.sub_id = b.sub_id
        left join product_mkc m with(nolock) on m.basket_id = b.basket_id
        where s.sub_id = #{subId}
          and m.imei is not null
    </select>
    <select id="getScalperWarning" resultType="com.jiuji.oa.oacore.thirdplatform.order.vo.ScalperWarningBO">
        SELECT top 1 p.product_id productId,
            p.product_name productName,
            SUM(tpoi.sku_count) subCount,
            STRING_AGG(tpo.sub_id, ',') AS subIdListStr
        FROM
        third_platform_order tpo WITH (NOLOCK, index(idx_order_time))
        inner join third_platform_order_items tpoi with(nolock) on tpoi.outjdid = tpo.id
        inner join productinfo p with(nolock) on p.ppriceid = tpoi.ppriceid and p.ismobile1 = 1
            <!-- 如果ppids不为空, p.ppriceid in(ppids)-->
            <if test="productIds != null and !productIds.isEmpty()">
                and p.product_id in
                <foreach item="productId" collection="productIds" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
        where
            tpo.plat_code = #{platCode}
            AND tpo.order_time >= #{startTime}
            <!--like %@#address-->
            AND tpo.buyer_address like CONCAT('%@#',#{address})
            and exists(select 1 from sub s with(nolock) where s.sub_id = tpo.sub_id and s.sub_check in (0,1,2,3,5,6,7))
            GROUP BY p.product_id,p.product_name
            HAVING SUM(tpoi.sku_count) >= 3
    </select>
    <select id="getLossSum" resultType="java.math.BigDecimal">
        select ISNULL(s.yifuM-ISNULL(sum(b.inprice*b.basket_count),0),0) from sub s with(nolock)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        where s.sub_id = #{subId}
        group by s.sub_id,s.yifuM
    </select>
    <select id="getLogisticsPtInfoByWuliuId"
            resultType="com.jiuji.oa.oacore.thirdplatform.order.bo.LogisticsPtInfoBO">
        select w.id wuliuId,
               w.com,
               w.nu,
               isnull(w.pt_user_name,cu.ch999_name) ptUserName,
               isnull(w.pt_user_mobile,cu.mobile) ptUserMobile
        from wuliu w with(nolock)
        left join ch999_user cu with(nolock) on w.paijianren = cu.ch999_name
        where w.id = #{wuliuId}
    </select>
    <select id="getLossSumJd" resultType="java.math.BigDecimal">
        select SUM(t.lossPrice) as totalLossPrice from
            (select (b.price + (o.plat_money * (b.price/s.yingfuM)) ) * (case when i.brandid = 1 then 0.95 else 0.96 end) - b.inprice * b.basket_count as lossPrice from sub s with(nolock)
        INNER  join jingdong_order o  with(nolock) on  o.sub_id = s.sub_id
                left join basket b with(nolock) on b.sub_id = s.sub_id
                 left join productinfo i with(nolock) on i.ppriceid = b.ppriceid
             where s.sub_id =  #{subId} ) t
    </select>
</mapper>
