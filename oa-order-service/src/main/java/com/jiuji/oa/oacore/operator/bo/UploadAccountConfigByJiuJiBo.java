package com.jiuji.oa.oacore.operator.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 19:33
 * @Description Excel导入
 */
@Data
@Accessors(chain = true)
public class UploadAccountConfigByJiuJiBo implements Serializable {
    private static final long serialVersionUID = 5228870489087207552L;
    /**
     * 归属渠道id
     */
    @ApiModelProperty(value = "归属渠道id")
    private Integer channel;
    /**
     * 归属渠道名称
     */
    @ApiModelProperty(value = "归属渠道名称")
    private String channelName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String account;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "渠道标识")
    private String channelMark;

    @ApiModelProperty(value = "渠道名称")
    private String quDaoName;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    private Integer subject;

    /**
     * 工号使用人
     */
    @ApiModelProperty(value = "工号使用人")
    private Integer staffId;
    /**
     * 地区
     */
    @ApiModelProperty(value = "地区")
    private String areaIds;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private Integer category;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private Integer categoryName;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer rank;
    /**
     * 是否是主工号
     */
    @ApiModelProperty(value = "是否是主工号")
    private String isMainJobStr;
    /**
     * 绑定主工号
     */
    @ApiModelProperty(value = "绑定主工号")
    private String mainJobName;
}
