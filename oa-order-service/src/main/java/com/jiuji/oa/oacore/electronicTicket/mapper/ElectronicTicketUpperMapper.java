package com.jiuji.oa.oacore.electronicTicket.mapper;


import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.electronicTicket.dto.CustomerInformation;
import com.jiuji.oa.oacore.electronicTicket.dto.Renewalsubsidy;
import com.jiuji.oa.oacore.electronicTicket.dto.TaxPiao;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
/**
 * <AUTHOR>
 */
@Mapper
public interface ElectronicTicketUpperMapper {

    /**
     * 获取会员等级
     * @param userId
     * @return
     */
    @Select("select userclass,userName,mobile from BBSXP_Users with (nolock) where ID=#{userId}")
    CustomerInformation getUserClass(Long userId);



    @Select(" select c.sub_id as subId,n.Total as total from cardLogs c with(nolock),dbo.NumberCard n with(nolock)" +
            " where c.cardid=n.ID and c.sub_id=#{subId}" +
            " and exists (select 1 from sysconfig cf with(nolock) cross apply F_SPLIT(cf.value,',') f where f.split_value=n.CardID)")
    List<Renewalsubsidy> getRenewalsubsidyBySubId(Integer subId);

    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select("select top 1 sub_id,flag from dbo.tax_piao with(nolock)  where type_=0 and sub_id=#{subId} order by dtime desc")
    TaxPiao getTaxPiao(Long subId);


    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select(" select distinct s.userid from sub s with(nolock)\n" +
            "        right join basket b with (nolock ) on b.sub_id=s.sub_id\n" +
            "        right join ServiceRecord sr with (nolock ) on sr.userid=s.userid and sr.basket_idBind=b.basket_id\n" +
            "        where s.sub_check=3\n" +
            "           and  sr.ServiceType = 21\n" +
            "           and isnull(sr.isdel,0) = 0\n" +
            "           and s.tradeDate1 between '2021-09-01 00:00:00' and '2022-08-31 23:59:59'\n" +
            "           and b.ppriceid in (select ppriceid from productinfo with(nolock ) where product_id in(85432,85464,85435,100827))")
    List<Integer> selectPurchaseCareAndIphone();

    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select(" select distinct s.userid\n" +
            " from sub s with (nolock)\n" +
            "         right join ServiceRecord sr with (nolock) on sr.sub_id = s.sub_id\n" +
            " where s.sub_check = 3\n" +
            "  and sr.ServiceType = 21\n" +
            "  and isnull(sr.isdel, 0) = 0\n" +
            "  and s.tradeDate1 between '2021-01-01 00:00:00' and '2022-08-31 23:59:59'")
    List<String> selectPurchaseCareAndIphoneV2();

    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select(" select distinct s.userid\n" +
            " from sub s with (nolock)\n" +
            "         right join ServiceRecord sr with (nolock) on sr.sub_id = s.sub_id\n" +
            " where s.sub_check = 3\n" +
            "  and sr.ServiceType = 21\n" +
            "  and isnull(sr.isdel, 0) = 0\n" +
            "  and s.tradeDate1 <= '2024-09-09 23:59:59'")
    List<String> selectPurchaseCareAndIphoneV3();

    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select("SELECT  top 1  a.receiptTip FROM  productBenefits a WITH(NOLOCK)\n" +
            "            WHERE a.isdel = 0\n" +
            "                AND (\n" +
            "                    (a.type = 1 AND CHARINDEX(',' + CAST(#{ppid} AS VARCHAR) + ',', ',' + a.productIds + ',') > 0 and (a.xtenants is null or  CHARINDEX(',' + CAST(#{xtenant} AS VARCHAR) + ',', ',' + a.xtenants + ',') > 0)) \n" +
            "                    OR (a.type = 2 AND CHARINDEX(',' + CAST(#{pid} AS VARCHAR) + ',', ',' + a.productIds + ',') > 0 and (a.xtenants is null or  CHARINDEX(',' + CAST(#{xtenant} AS VARCHAR) + ',', ',' + a.xtenants + ',') > 0)) \n" +
            "                   )\n" +
            "            ORDER BY  CASE  WHEN a.type = 1 THEN 1  WHEN a.type = 2 THEN 2 END")
    String selectReceiptTipConfig(Integer pid, Integer ppid, Integer xtenant);

    /**
     * 优先根据订单出货门店对应体系进行校验，没有出货门店使用订单所在门店
     * @param subId
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    @Select("select \n" +
            "             a.xtenant\n" +
            "             from sub s with(nolock) \n" +
            "             left join areainfo a with(nolock) on a.id = isnull(s.kcAreaid, s.areaid) " +
            "where s.sub_id = #{subId}")
    Integer selectXtenantBySub(Integer subId);


}
