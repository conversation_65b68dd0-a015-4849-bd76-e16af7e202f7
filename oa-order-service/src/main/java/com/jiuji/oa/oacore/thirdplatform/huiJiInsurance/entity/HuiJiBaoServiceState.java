package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.enums.HuiJiBaoRegisterStateEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("huiJiBao_service_state")
public class HuiJiBaoServiceState extends Model<HuiJiBaoServiceState> {

    /**
     * t_after_services_buy主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 保险注册状态，默认0为未注册
     * @see HuiJiBaoRegisterStateEnum
     */
    @TableField("register_state")
    private Integer registerState;

    /**
     * 注册时间
     */
    @TableField("register_submit_time")
    private LocalDateTime registerSubmitTime;

    /**
     * 审核时间
     */
    @TableField("register_check_time")
    private LocalDateTime registerCheckTime;

    /**
     * 汇机保开始时间
     */
    @TableField("efficient_start_date")
    private LocalDate efficientStartDate;

    /**
     * 汇机保结束时间
     */
    @TableField("efficient_end_date")
    private LocalDate efficientEndDate;

    @TableField("register_remark")
    private String registerRemark;

    /**
     * 保单编号
     */
    @TableField("insurance_policy_no")
    private String insurancePolicyNo;


    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String ID = "id";

    public static final String AFTER_SERVICES_BUY_ID = "after_services_buy_id";

    public static final String REGISTER_STATE = "register_state";

    public static final String IS_DELETE = "is_delete";

}
