package com.jiuji.oa.oacore.weborder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 赠品
 * @Author: WangKai
 * @Date: 2019/10/25
 */
@Data
public class ProductGiftListVO {
    /**
     * pid
     */
    private Integer productId;
    /**
     * 套餐ID/赠品PPID
     */
    private Integer id;
    /**
     * 商品名/套餐名
     */
    private String name;
    /**
     * 自定义商品名
     */
    private String diyName;
    /**
     * 上下架
     */
    private Boolean display;
    /**
     * 规格
     */
    private String standard;
    /**
     * 排序
     */
    private Integer rank;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 适用地区
     */
    private String cityIds;
    /**
     * 赠品分类 0-普通；1-多选1；2-多选2；3-多选3
     */
    private Integer giftClass;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否是套餐
     */
    private Boolean isPackage;

    /**
     * 配置是否生效
     */
    private Integer effective;

    /**
     * 赠品生效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectStartTime;

    /**
     * 赠品生效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectEndTime;

    /**
     * 适用会员等级
     */
    private String memberLevel;
}
