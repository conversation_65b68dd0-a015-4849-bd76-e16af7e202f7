package com.jiuji.oa.oacore.thirdplatform.doudian.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductChangeEventEnum implements IEnum<Long>, CodeMessageEnumInterface {
    PRODUCT_CREATED(1L, "商品创建"),
    PRODUCT_SAVED(2L, "商品保存"),
    PRODUCT_SUBMITTED_FOR_REVIEW(3L, "商品提交审核"),
    PRODUCT_APPROVED(4L, "商品审核通过"),
    PRODUCT_REJECTED(5L, "商品审核不通过"),
    PRODUCT_DELETED(6L, "商品被删除"),
    PRODUCT_RESTORED_FROM_RECYCLE_BIN(7L, "商品从回收站恢复"),
    PRODUCT_BANNED(8L, "商品封禁"),
    PRODUCT_UNBANNED(9L, "解除商品封禁"),
    PRODUCT_OFFLINE(10L, "下架商品"),
    PRODUCT_ONLINE(11L, "上架商品"),
    PRODUCT_SOLD_OUT(12L, "商品售空"),
    PRODUCT_ABORTED_REVIEW(13L, "终止审核商品"),
    PRODUCT_APPROVED_PENDING_PUBLISH(14L, "审核通过待上架");

    private final Long code;
    private final String message;

    @Override
    public Long getValue() {
        return code;
    }
}
