//package com.jiuji.oa.oacore.oaorder.schedule;
//
//import com.jiuji.oa.oacore.oaorder.service.ZLFSubService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///**
// * @Description:当前时间节点数据保存到临时表中
// * @author: <PERSON>
// * @date: 2020/4/7 13:46
// */
//@Component
//@EnableScheduling
//public class HisSubSaveTask {
//
//    @Autowired
//    private ZLFSubService zlfSubService;
//
//    @Scheduled(cron = "0 */10 * * * ?")
//    public void saveHisData() {
//        //每隔10分钟存一次历史数据
//        zlfSubService.saveLog();
//    }
//}
