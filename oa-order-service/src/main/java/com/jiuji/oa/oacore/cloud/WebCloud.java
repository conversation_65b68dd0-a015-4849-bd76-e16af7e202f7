package com.jiuji.oa.oacore.cloud;

import com.jiuji.oa.oacore.cloud.bo.MiniFileBo;
import com.jiuji.oa.oacore.cloud.bo.OrderRecommendBO;
import com.jiuji.oa.oacore.cloud.bo.OrderRecommendQueryVO;
import com.jiuji.oa.oacore.cloud.fallback.WebCloudFallbackFactory;
import com.jiuji.oa.oacore.promocode.bo.FamilyMainUserIdBo;
import com.jiuji.oa.oacore.promocode.bo.FamilyUserInfo;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductActivityPriceVO;
import com.jiuji.oa.oacore.weborder.vo.ProductGiftListVO;
import com.jiuji.oa.oacore.weborder.vo.req.DiySaasOrderPageBO;
import com.jiuji.tc.common.vo.R;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2019/12/4
 */
@FeignClient(value = "WEB", fallbackFactory = WebCloudFallbackFactory.class)
public interface WebCloud {

    @RequestMapping("/web/api/open/upload/v1")
    R<MiniFileBo> uploadFile(@RequestParam(value = "file", required = false) MultipartFile file, @RequestParam(value = "collection") String collection);

    /**
     * 获取亲情号主账号接口
     * @param userIds
     * @return
     */
    @GetMapping("/web/api/family/number/getFamilyMainUseId/v1")
    R<List<FamilyMainUserIdBo>> getFamilyMainUseId(@RequestParam(value = "userIds") List<Integer> userIds);

    /**
     * 获取亲情号用户的ID集合接口
     * @param userId
     * @return
     */
    @GetMapping("/web/api/family/number/getFamilyUserList/v1")
    R<FamilyUserInfo> getFamilyUserList(@RequestParam(value = "userId") Integer userId);

    /**
     * DIY订单导出
     *
     * @return
     */
    @GetMapping("/web/api/diy_saas/order/export/v1")
    Response exportDiyOrderV1(@SpringQueryMap DiySaasOrderPageBO orderPageParam);

    /**
     * 获取oa订单搭售商品
     *
     * @param queryVO 查询参数
     * @return
     */
    @PostMapping("/web/api/product/opening/getOaRecommendSale/v1")
    R<OrderRecommendBO> getOaRecommendSale(@RequestBody @Valid OrderRecommendQueryVO queryVO, @RequestHeader("xtenant") Long xtenant);


    /**
     * 获取赠品列表接口
     * @param ppid
     * @return
     */
    @GetMapping("/web/adm/productGift/list/v2")
    R<List<ProductGiftListVO>> getGiftByPPid(@RequestParam(value = "ppid") Integer ppid);


    /**
     * 获取商品活动价格
     * @param ppid
     * @return
     */
    @GetMapping("/web/api/product/opening/activityPrice/v1")
    R<List<ProductActivityPriceVO>> activityPrice(@RequestBody List<Integer> ppid);
}
