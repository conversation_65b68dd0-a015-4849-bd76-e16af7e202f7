package com.jiuji.oa.oacore.thirdplatform.yading.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;


/**
 * @Description 亚丁回调业务类型枚举
 * <AUTHOR>
 * @Date   2022/5/10 17:07
 */
@Getter
@RequiredArgsConstructor
public enum YadingCallbackMethodEnum {
    /**
     * 亚丁回调业务类型枚举
     */
    REGISTER_SUBMIT(1, "注册提交成功"),
    REGISTER_CHECK(2, "注册单审核"),
    CLAIM_CHECK(3, "理赔单审核"),
    RENEW_CHECK(4, "换机完成审核"),
    CHANGE_IMEI_CHECK(5, "更换IMEI审核"),
    RESUPPLY_CHECK(6, "照片补交审核"),
    ;

    private final Integer value;

    private final String label;

    public static YadingCallbackMethodEnum ofLabel(String str){
        if(StringUtils.isBlank(str)){
            return null;
        }
        for (YadingCallbackMethodEnum methodEnum : YadingCallbackMethodEnum.values()) {
            if(methodEnum.label.equals(str)){
                return methodEnum;
            }
        }
        return null;
    }

}
