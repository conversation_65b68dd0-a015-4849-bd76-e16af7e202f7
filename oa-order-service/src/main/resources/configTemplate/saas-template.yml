consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://{{.WeedFSUrl}}:5083
  select:
    url: https://{{.DomainImg.Domain}}/
  upload:
    url: http://{{.WeedFSUrl}}:9333
instance-zone: {{.TenantId}}
jiuji:
  sys:
    moa: https://{{.DomainMoa.Domain}}
    pc: https://{{.DomainOa.Domain}}
    web: https://{{.DomainPC.Domain}}
    inWcf: http://{{.DomainInWcf.Domain}}
    oaWcf: http://{{.DomainOaWcf.Domain}}
    xtenant: {{.TenantId}}
  xtenant: {{.XTenant}}
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__{{.TenantId}}:{{.MongoCh999Oa.Pwd}}@{{.MongoCh999Oa.Host}}/ch999oa__{{.TenantId}}
  url1: mongodb://ch999oa__{{.TenantId}}:{{.MongoCh999Oa.Pwd}}@{{.MongoCh999Oa.Host}}/ch999oa__{{.TenantId}}
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: {{.DBManageTraining.Name}}
    password: {{.DBManageTraining.Pwd}}
    url: {{.DBManageTraining.Host}}:3306
    username: {{.DBManageTraining.Username}}
  oa_core:
    dbname: {{.DBOaCore.Name}}
    password: {{.DBOaCore.Pwd}}
    url: {{.DBOaCore.Host}}:3306
    username: {{.DBOaCore.Username}}
  oa_nc:
    dbname: {{.DBOaNc.Name}}
    password: {{.DBOaNc.Pwd}}
    url: {{.DBOaNc.Host}}:3306
    username: {{.DBOaNc.Username}}
  oa_log:
    dbname: {{.DBOaLog.Name}}
    password: '{{.DBOaLog.Pwd}}'
    url: {{.DBOaLog.Host}}:{{.DBOaLog.Port}}
    username: {{.DBOaLog.Username}}
office:
  sys:
    xtenant: {{.TenantId}}
rabbitmq:
  master:
    password: {{.RabbitMQOaAsync.Pwd}}
    port: {{.RabbitMQOaAsync.Port}}
    url: {{.RabbitMQOaAsync.Host}}
    username: {{.RabbitMQOaAsync.Username}}
    vhost: {{.RabbitMQOaAsync.VHost}}
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: {{.RabbitMQOa.Pwd}}
    port: {{.RabbitMQOa.Port}}
    url: {{.RabbitMQOa.Host}}
    username: {{.RabbitMQOa.Username}}
    vhost: {{.RabbitMQOa.VHost}}
  oaAsync:
    password: {{.RabbitMQOaAsync.Pwd}}
    port: {{.RabbitMQOaAsync.Port}}
    url: {{.RabbitMQOaAsync.Host}}
    username: {{.RabbitMQOaAsync.Username}}
    vhost: {{.RabbitMQOaAsync.VHost}}
  printer:
    password: {{.RabbitMQPrinter.Pwd}}
    port: {{.RabbitMQPrinter.Port}}
    url: {{.RabbitMQPrinter.Host}}
    username: {{.RabbitMQPrinter.Username}}
    vhost: {{.RabbitMQPrinter.VHost}}
redis:
  oa:
    host: {{.RedisOA.MasterHost}}
    password: {{.RedisOA.Pwd}}
    port: {{.RedisOA.MasterPort}}
    url: {{.RedisOA.Pwd}}@{{.RedisOA.Master}}
sms:
  send:
    email:
      url: http://{{.DomainSms.Domain}}/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://{{.DomainSms.Domain}}/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: {{.TenantId}}
sqlserver:
  after_write:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: '{{.DBCh999OaNew.Pwd}}'
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oanew:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: '{{.DBCh999OaNew.Pwd}}'
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ch999oanewHis:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: '{{.DBCh999OaNew.Pwd}}'
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: '{{.DBOffice.Pwd}}'
    port: 1433
    username: {{.DBOffice.Username}}
  oanewWrite:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: '{{.DBCh999OaNew.Pwd}}'
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  office:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: '{{.DBOffice.Pwd}}'
    port: 1433
    username: {{.DBOffice.Username}}
  officeWrite:
    dbname: {{.DBOffice.Name}}
    host: {{.DBOffice.Host}}
    password: '{{.DBOffice.Pwd}}'
    port: 1433
    username: {{.DBOffice.Username}}
  smallpro_write:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  web999:
    dbname: {{.DBWeb999.Name}}
    host: {{.DBWeb999.Host}}
    password: {{.DBWeb999.Pwd}}
    port: 1433
    username: {{.DBWeb999.Username}}
  ch999oanewReport:
    dbname: {{.DBCh999OaNew.Name}}
    host: {{.DBCh999OaNew.Host}}
    password: "{{.DBCh999OaNew.Pwd}}"
    port: 1433
    username: {{.DBCh999OaNew.Username}}
  ershou:
    dbname: {{.DBErShou.Name}}
    host: {{.DBErShou.Host}}
    password: "{{.DBErShou.Pwd}}"
    port: 1433
    username: {{.DBErShou.Username}}
url:
  delImgUrl: http://{{.WeedFSUrl}}:5083
  oa-push-info: http://{{.DomainInWcf.Domain}}/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://{{.DomainImg.Domain}}/
  source:
      path: i18n/url
  uploadImgUrl: http://{{.WeedFSUrl}}:9333
autoCalculateSalary:
  xtenant: {{.TenantId}}

# 亚丁保险对接地址url参数等
yading:
  # 同步员工信息地址
  sync-user-url: https://mapi.cdydkj.cn/jiuji/User/synchronizaMember
  # 注册保险的地址
  insurance-register-url: http://jiuji.cdydkj.cn/RightApply/addApply
  # 接口加签的签名key
  sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa


apollo:
  url: {{.ApolloConfig.Meta}}
  file: application-order.yml
