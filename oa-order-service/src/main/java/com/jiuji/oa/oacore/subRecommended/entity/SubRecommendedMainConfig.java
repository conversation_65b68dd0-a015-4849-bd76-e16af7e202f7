package com.jiuji.oa.oacore.subRecommended.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Data
@Accessors(chain = true)
@TableName("sub_recommended_main_config")
public class SubRecommendedMainConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("is_enabled")
    private Integer isEnabled;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    /**
     * 配置创建归属地
     */
    @TableField("create_area_id")
    private Integer createAreaId;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("xtenant")
    private Integer xtenant;

    @TableLogic
    @TableField("is_del")
    private Integer isDel;


    /**
     * 获取开始默认时间
     * @return
     */
    public static LocalDateTime getDefaultStartTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return  LocalDateTime.parse("2000-10-24 00:00:00", formatter);
    }

    /**
     * 获取借宿默认时间
     * @return
     */
    public static LocalDateTime getDefaultEndTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse("3000-05-19 00:00:00", formatter);
    }


    /**
     * 获取生效时间
     * @param startTime
     * @param endTime
     * @return
     */
    public static String createEffectiveTime(LocalDateTime startTime,LocalDateTime endTime){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        startTime=Optional.ofNullable(startTime).orElse(getDefaultStartTime());
        endTime=Optional.ofNullable(endTime).orElse(getDefaultEndTime());
        if(startTime.compareTo(getDefaultStartTime())==0 && endTime.compareTo(getDefaultEndTime())==0){
            return "永久";
        } else if(startTime.compareTo(getDefaultStartTime())!=0 && endTime.compareTo(getDefaultEndTime())!=0){
            return startTime.format(formatter)+"~"+endTime.format(formatter);
        } else if(startTime.compareTo(getDefaultStartTime())!=0 && endTime.compareTo(getDefaultEndTime())==0){
            return startTime.format(formatter)+"~";
        } else if(startTime.compareTo(getDefaultStartTime())==0 && endTime.compareTo(getDefaultEndTime())!=0){
            return "~"+endTime.format(formatter);
        }
        return "";
    }
}
