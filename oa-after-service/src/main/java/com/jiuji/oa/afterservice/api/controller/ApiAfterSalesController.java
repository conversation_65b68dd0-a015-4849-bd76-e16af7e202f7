package com.jiuji.oa.afterservice.api.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.cloud.after.vo.req.AfterCountReq;
import com.jiuji.cloud.after.vo.req.QueryAfterSalesCouponVo;
import com.jiuji.cloud.after.vo.req.UseCouponVO;
import com.jiuji.cloud.after.vo.req.XiaojianSubReqVO;
import com.jiuji.cloud.after.vo.res.AfterCountVO;
import com.jiuji.cloud.after.vo.res.AfterSalesCouponVO;
import com.jiuji.cloud.after.vo.res.XiaojianSubResVO;
import com.jiuji.oa.afterservice.api.bo.GuobuBo;
import com.jiuji.oa.afterservice.api.bo.GuobuResultBo;
import com.jiuji.oa.afterservice.api.service.ApiAfterSalesService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRiskNotificationService;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.oa.afterservice.refund.service.ZheJiaPayService;
import com.jiuji.oa.afterservice.shouhou.vo.req.MaintenanceReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouRiskNotificationReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.SignShouhouRiskNotificationReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.MaintenanceRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationInfoRes;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import jodd.typeconverter.Convert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/apiAfterSalesController")
@Slf4j
public class ApiAfterSalesController {

    @Resource
    private ApiAfterSalesService apiAfterSalesService;
    @Resource
    private ShouhouRiskNotificationService shouhouRiskNotificationService;
    @Resource
    private SmallproDetailsExService smallproDetailsExService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;


    /**
     * 维修单优惠码查询
     * @param queryAfterSalesCouponVo
     * @return
     */
    @PostMapping("/getAfterSalesCoupon")
    public R<List<AfterSalesCouponVO>> getAfterSalesCoupon(@RequestBody QueryAfterSalesCouponVo queryAfterSalesCouponVo) {
        queryAfterSalesCouponVo.setUserId(ObjectUtil.defaultIfNull(Convert.toLong(currentRequestComponent.getWebUserId()), queryAfterSalesCouponVo.getUserId()));
        log.warn("web维修单优惠码查询,传入参数{}", JSONUtil.toJsonStr(queryAfterSalesCouponVo));
        return apiAfterSalesService.selectAfterSalesCoupon(queryAfterSalesCouponVo);
    }


    /**
     * 进行售后处理查询
     * @param afterCountReq
     * @return
     */
    @PostMapping("/selectAfterCount")
    public R<AfterCountVO> selectAfterCount(@RequestBody AfterCountReq afterCountReq) {
        log.warn("进行售后处理查询,传入参数{}", JSONUtil.toJsonStr(afterCountReq));
        AfterCountVO afterCountVO = apiAfterSalesService.selectAfterCount(afterCountReq);
        log.warn("进行售后处理查询,返回结果{}", JSONUtil.toJsonStr(afterCountVO));
        return R.success(afterCountVO);
    }


    /**
     * 维修单使用优惠券
     * @param useCouponVO
     * @return
     */
    @PostMapping("/useAfterSalesCoupon")
    public R<Boolean> useAfterSalesCoupon(@RequestBody UseCouponVO useCouponVO) {
        log.warn("web维修单优惠码使用，传入参数{}",JSONUtil.toJsonStr(useCouponVO));
        return apiAfterSalesService.useAfterSalesCoupon(useCouponVO);
    }

    /**
     * 查询风险告知书详情
     * @param req
     * @return
     */
    @PostMapping("/getShouhouRiskNotification/v1")
    public R<ShouhouRiskNotificationInfoRes> getShouhouRiskNotification(@RequestBody ShouhouRiskNotificationReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return R.success(shouhouRiskNotificationService.getShouhouRiskNotificationInfo(req));
    }

    /**
     * 签署风险告知书
     * @param req
     * @return
     */
    @PostMapping("/signShouhouRiskNotification/v1")
    public R<String> signShouhouRiskNotification(@RequestBody SignShouhouRiskNotificationReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        boolean flag = shouhouRiskNotificationService.signShouhouRiskNotification(req);
        if (flag) {
            return R.success("操作成功");
        }
        return R.success("操作失败");
    }


    /**
     * 维修单查询
     * @param req
     * @return
     */
    @PostMapping("/selectMaintenanceOrder/v1")
    public R<MaintenanceRes> selectMaintenanceOrder(@RequestBody MaintenanceReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        log.warn("对外提供维修单查询传入参数：{}",JSONUtil.toJsonStr(req));
        MaintenanceRes maintenanceRes = apiAfterSalesService.selectMaintenanceOrder(req);
        log.warn("对外提供维修单查询返回结果：{}",JSONUtil.toJsonStr(maintenanceRes));
        return R.success(maintenanceRes);
    }

    /**
     * 小件维修单查询
     * @param req
     * @return
     */
    @PostMapping("/getXiaojianSub/v1")
    public R<XiaojianSubResVO> getXiaojianSub(@RequestBody XiaojianSubReqVO req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return R.success(apiAfterSalesService.getXiaojianSub(req));
    }

    /**
     * 判断小件单是否服务商品
     * @return
     */
    @GetMapping("/getSmallproIsServiceProduct/v1")
    public R<Boolean> getSmallproIsServiceProduct(@RequestParam(value = "smallproId") Integer smallproId) {
        return R.success(smallproDetailsExService.getSmallproIsServiceProduct(smallproId));
    }

    @ApiOperation(value = "是否国补订单")
    @GetMapping("/getZheJiaPayEnum/v1")
    public R<Boolean> getZheJiaPayEnum(@RequestParam(value = "orderId") @Valid Integer orderId) {
        boolean result = false;
        ZheJiaPayEnum zheJiaPayEnum = SpringUtil.getBean(ZheJiaPayService.class).getZheJiaPayEnum(orderId, TuihuanKindEnum.TPJ);
        if((ZheJiaPayEnum.GUO_JIA_BUTIE.equals(zheJiaPayEnum))){
            result = true;
        }
        return R.success(result);
    }

    @ApiOperation(value = "是否国补订单")
    @PostMapping("/getZheJiaPayEnumBatch/v1")
    public R<List<GuobuResultBo>> getZheJiaPayEnumBatch(@RequestBody @Valid GuobuBo bo) {
        List<GuobuResultBo> result = SpringUtil.getBean(ZheJiaPayService.class).getZheJiaPayEnumBatch(bo, TuihuanKindEnum.TPJ);
        return R.success(result);
    }
}
