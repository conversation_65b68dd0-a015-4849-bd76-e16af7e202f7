package com.jiuji.oa.oacore.thirdplatform.yading.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceMapping;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingAppleProTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingPhoneTypeEnum;

import java.math.BigDecimal;

/**
 * <p>
 * 九讯服务与亚丁服务对应关系表， 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
public interface IYadingServiceMappingService extends IService<YadingServiceMapping> {


    /**
     * @Description 根据skuId，安卓还是苹果、是否pro找到唯一的一条对应关系
     * @Param skuId
     * @Param phoneTypeEnum 安卓还是苹果
     * @Param proTypeEnum 是否pro
     * @Param androidPrice 安卓机才传，苹果不传
     * @return YadingServiceMapping
     * <AUTHOR>
     * @Date 10:29 2022/5/12
     **/
    YadingServiceMapping getYadingServiceConfig(Integer skuId, YadingPhoneTypeEnum phoneTypeEnum, YadingAppleProTypeEnum proTypeEnum,
                                                BigDecimal androidPrice);



    YadingServiceMapping getYadingServiceConfig(Integer skuId);

}
