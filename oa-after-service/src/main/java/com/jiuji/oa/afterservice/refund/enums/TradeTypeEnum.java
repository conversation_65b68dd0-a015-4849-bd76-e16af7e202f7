package com.jiuji.oa.afterservice.refund.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易类型 1、新机 2、优品 3、良品
 * <AUTHOR>
 * @since 2022/12/22 17:43
 */
@Getter
@AllArgsConstructor
public enum TradeTypeEnum implements CodeMessageEnumInterface {
    NEW_SUB(1, "新机")
    ,YOU_PIN_SUB(2, "优品")
    ,LIANG_PIN_SUB(3, "良品")
    ;
    private Integer code;

    private String message;
}
