package com.jiuji.oa.oacore.thirdplatform.doudian.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.msg.instantShopping_DeliveryChange.param.InstantShoppingDeliveryChangeParam;
import com.jiuji.oa.oacore.oaorder.service.SubService;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.DoudianTagEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.InstantShoppingDeliveryChangeMessageService;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物流变更信息监听
 * <AUTHOR>
 * @since 2023/11/13 9:36
 */
@Service
@Slf4j
public class InstantShoppingDeliveryChangeMessageServiceImpl extends ParentMessageServiceImpl<DoudianOpMsgParamRecord<InstantShoppingDeliveryChangeParam>>
        implements InstantShoppingDeliveryChangeMessageService {
    @Resource
    private OrderService orderService;
    @Resource
    private SubService subService;
    @Resource
    private DoudianFactory doudianFactory;
    @Resource
    private DefaultDouDianService defaultDouDianService;

    @Override
    public DoudianTagEnum acceptTag() {
        return DoudianTagEnum.DOUDIAN_INSTANT_SHOPPING_DELIVERY_CHANGE;
    }

    @Override
    protected R protectedHandleMessage(DoudianOpMsgParamRecord<InstantShoppingDeliveryChangeParam> paramRecord) throws Exception {
        //获取推送配送状态
        InstantShoppingDeliveryChangeParam shoppingDeliveryChangeParam = paramRecord.getData();
        if(shoppingDeliveryChangeParam == null || shoppingDeliveryChangeParam.getShopOrderId() == null){
            return R.error("物流信息和单号都不能为空");
        }
        List<Order> orderList = orderService.lambdaQuery().in(Order::getOrderId, Convert.toList(String.class, shoppingDeliveryChangeParam.getShopOrderId()))
                .orderByDesc(Order::getId).list();
        if(CollUtil.isEmpty(orderList)){
            return R.error(StrUtil.format("订单[{}]没有同步", shoppingDeliveryChangeParam.getShopOrderId()));
        }
        List<Long> subIdList = orderList.stream().filter(order -> order.getSubId() != null && Objects.equals(order.getType(), Order.OrderTypeEnum.NEW_ORDER.getCode()))
                .map(Order::getSubId).collect(Collectors.toList());
        //平台运力配送
        if (Objects.equals(4L, shoppingDeliveryChangeParam.getBussinessMode())) {
            try {
                defaultDouDianService.deliverySynchronization(shoppingDeliveryChangeParam, subIdList);
            } catch (Exception e) {
                log.error("平台运力推送业务处理异常：", e);
            }
        }
        return R.success(null);
    }
}
