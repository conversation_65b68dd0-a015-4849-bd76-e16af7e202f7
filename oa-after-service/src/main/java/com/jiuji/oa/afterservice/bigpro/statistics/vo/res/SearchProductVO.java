package com.jiuji.oa.afterservice.bigpro.statistics.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.common.vo.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @since 2021/5/12 16:34
 */
@Data
@ApiModel("搜索产品结果")
@EqualsAndHashCode(of={"pid"})
public class SearchProductVO {
    @ApiModelProperty(value = "产品id",example = "47525")
    private Integer pid;
    @ApiModelProperty(value = "规格id")
    private Integer ppid;
    @ApiModelProperty(value = "产品规格")
    private String  productColor;
    @ApiModelProperty(value = "产品名称",example = "Apple iPhone 12 全网通5G版")
    private String productName;
    @ApiModelProperty(value = "检索内容",hidden = true)
    @JSONField(serialize = false)
    private String text;
    @ApiModelProperty(value = "销量")
    private Integer sales;

    @Data
    @ApiModel("搜索条件")
    @Accessors(chain = true)
    public static class Req extends PageReq {
        @ApiModelProperty("关键词(如p30/商品id/skuid)")
        private String world;
        @ApiModelProperty(hidden = true,value = "数值类型的关键词")
        private Integer numWorld;
        /**
         * 查询类型 1、数据排序调整为按照近半年维修量进行排序
         */
        private Integer type;
    }
}
