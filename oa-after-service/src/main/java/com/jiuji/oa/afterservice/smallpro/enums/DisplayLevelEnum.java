package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/1/12
 * @description 小件品类统计：展陈级别
 */
@Getter
@AllArgsConstructor
public enum DisplayLevelEnum implements CodeMessageEnumInterface {

    /**
     * 展陈级别
     */
    A(1, "展陈A类"),
    B(2, "展陈B类"),
    C(3,"展陈C类"),
    D(4, "展陈D类");

    /**
     * key
     */
    private final Integer code;
    /**
     * value
     */
    private final String message;

}
