package com.jiuji.oa.oacore.thirdplatform.doudian.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.msg.trade_TradeLogisticsChanged.param.LogisticsMsg;
import com.doudian.open.msg.trade_TradeLogisticsChanged.param.TradeTradeLogisticsChangedParam;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.DoudianTagEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.LogisticsChangedMessageService;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;

/**
 * 物流变更信息监听
 * <AUTHOR>
 * @since 2023/11/13 9:36
 */
@Service
@Slf4j
public class LogisticsChangedMessageServiceImpl extends ParentMessageServiceImpl<DoudianOpMsgParamRecord<TradeTradeLogisticsChangedParam>>
        implements LogisticsChangedMessageService {
    @Resource
    private OrderService orderService;
    @Override
    public DoudianTagEnum acceptTag() {
        return DoudianTagEnum.DOUDIAN_TRADE_TRADE_LOGISTICS_CHANGED;
    }

    @Override
    protected R protectedHandleMessage(DoudianOpMsgParamRecord<TradeTradeLogisticsChangedParam> paramRecord) throws Exception {
        //获取订单信息
        TradeTradeLogisticsChangedParam logisticsChangedParam = paramRecord.getData();
        if(logisticsChangedParam == null || logisticsChangedParam.getPId() == null){
            return R.error("物流信息和单号都不能为空");
        }
        List<Order> orderList = orderService.lambdaQuery().in(Order::getOrderId, Convert.toList(String.class, logisticsChangedParam.getSIds()))
                .orderByDesc(Order::getId).list();
        if(CollUtil.isEmpty(orderList)){
            return R.error(StrUtil.format("订单[{}]没有同步", logisticsChangedParam.getPId()));
        }
        LogisticsMsg logisticsMsg = logisticsChangedParam.getLogisticsMsg();
        if (logisticsMsg == null) {
            return R.error(StrUtil.format("订单[{}]物流信息为空", logisticsChangedParam.getPId()));
        }
        StringJoiner logisticsLogJoiner = new StringJoiner(" ").add("发货物流,")
                .add("快递公司:").add(logisticsMsg.getExpressCompanyId())
                .add("物流单号:").add(logisticsMsg.getLogisticsCode());
        Optional.ofNullable(logisticsChangedParam.getReceiverMsg()).ifPresent(receiverMsg -> logisticsLogJoiner
                .add("收货地址:").add(receiverMsg.getAddr())
                .add("收货人:").add(receiverMsg.getName())
                .add("收货人手机号:").add(receiverMsg.getTel())
        );

        StringJoiner errMsgJoiner = new StringJoiner(StringPool.SLASH);
        for (Order order : orderList) {
            if (order.getSubId() == null) {
                errMsgJoiner.add(StrUtil.format("订单[{}]本地oa单号为空", order.getOrderId()));
                continue;
            }
            //记录物流变更信息到日志中
            SubLogsNewReq subLog = LambdaBuild.create(new SubLogsNewReq())
                    .set(SubLogsNewReq::setSubId, Convert.toInt(order.getSubId()))
                    .set(SubLogsNewReq::setComment, logisticsLogJoiner.toString())
                    .set(SubLogsNewReq::setDTime, LocalDateTime.now())
                    .set(SubLogsNewReq::setInUser, "抖音")
                    .set(SubLogsNewReq::setShowType, Boolean.FALSE)
                    .set(SubLogsNewReq::setType, 1).build();
            if (Objects.equals(order.getType(), Order.OrderTypeEnum.lP_ORDER.getCode())) {
                //良品单
                SpringUtil.getBean(SubLogsCloud.class).addLpSubLog(subLog);
            } else {
                //新机单
                SpringUtil.getBean(SubLogsCloud.class).addSubLog(subLog);
            }
        }
        if(errMsgJoiner.length()>0){
            return R.error(errMsgJoiner.toString());
        }
        return R.success(null);
    }
}
