package com.jiuji.oa.afterservice.sys.dao;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/12 11:33
 */
@Mapper
public interface RepairDataMapper {
    int updateShouhouStat(@Param("shouhouIds") List<Integer> shouhouIds);

    /**
     *
     * @return
     */
    IPage<Dict> selectShouhouStatId(Page<Dict> page);
}
