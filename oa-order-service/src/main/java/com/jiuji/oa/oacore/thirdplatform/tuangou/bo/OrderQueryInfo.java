package com.jiuji.oa.oacore.thirdplatform.tuangou.bo;

import com.dianping.openapi.sdk.api.base.AbstractAPI;
import com.dianping.openapi.sdk.api.order.entity.OrderQueryorderRequest;
import com.dianping.openapi.sdk.api.order.entity.OrderQueryorderResponse;
import com.dianping.openapi.sdk.enums.AuthenticateType;
import com.dianping.openapi.sdk.enums.HttpMethodEnum;

/**
 * Created by autosdk on 2019-08-13 10:43:33.
 * )
 */
public class OrderQueryInfo extends AbstractAPI<OrderQueryInfoResponse> {

    public OrderQueryInfo() {
    }

    public OrderQueryInfo(OrderQueryInfoRequest request) {
        this.apiRequest = request;
    }

    @Override
    public HttpMethodEnum getHttpMethod() {
        return HttpMethodEnum.POST;
    }

    @Override
    public String getHttpUrl() {
        return "https://openapi.dianping.com/router/order/query/info";
    }

    @Override
    public AuthenticateType getAuthenticateType() {
        return AuthenticateType.SIGN;
    }

    @Override
    public Class getResponseClass() {
        return OrderQueryInfoResponse.class;
    }

    @Override
    public Class getRequestClass() {
        return OrderQueryInfoRequest.class;
    }

    @Override
    public String getMethod() {
        return "order.query.info";
    }
}
