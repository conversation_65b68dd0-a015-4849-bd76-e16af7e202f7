package com.jiuji.oa.oacore.salary.entity.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: qiweiqing
 * @Date: 2021/01/26/20:06
 * @Description:
 */
@Data
public class OrderExpenseCostLimitBO implements Serializable {

    @ApiModelProperty("费用上限")
    private BigDecimal expenseUp;
    @ApiModelProperty("费用下限")
    private BigDecimal expenseLow;
    @ApiModelProperty("成本上限")
    private BigDecimal costUp;
    @ApiModelProperty("成本下限")
    private BigDecimal costLow;

}
