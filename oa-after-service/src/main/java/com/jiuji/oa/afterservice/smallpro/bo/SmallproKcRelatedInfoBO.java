package com.jiuji.oa.afterservice.smallpro.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <小件库存关联ID信息BO>
 * translation: <Small inventory related ID information BO>
 *
 * <AUTHOR>
 * @date 2020/4/26
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproKcRelatedInfoBO {

    /**
     * 关联Id
     */
    private Integer basketId;
    /**
     * 库存出入库信息备注
     */
    private String comment;
}
