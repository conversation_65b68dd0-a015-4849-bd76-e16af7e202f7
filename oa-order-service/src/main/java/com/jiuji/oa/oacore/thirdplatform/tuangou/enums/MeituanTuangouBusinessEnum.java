package com.jiuji.oa.oacore.thirdplatform.tuangou.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MeituanTuangouBusinessEnum implements CodeMessageEnumInterface {
    BUSINESS_58(58, "到店综合"),
    BUSINESS_59(59, "到店综合(客户)"),
    ;
    private final Integer code;
    private final String message;
}
