package com.jiuji.oa.oacore.oaorder.dao;

import com.jiuji.oa.oacore.oaorder.bo.BasketProductBO;
import com.jiuji.oa.oacore.oaorder.po.Shouhou;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Mapper
public interface ShouhouMapper extends BaseMapper<Shouhou> {
    List<BasketProductBO> getShouhouListByShouhouIds(@Param("shouhouIds") List<Integer> shouhouIds);

    /**
     * 根据单号获取售后-整体订单相关员工
     * @param subId 单号
     * @return 员工id
     */
    Integer getShouHouAllUserId(Integer subId);

    /**
     * 根据单号获取售后-整体订单相关员工
     * @param subId 单号
     * @return 员工姓名
     */
    String getShouHouAllUsername(Integer subId);

    String getInUser(Integer subId);
}
