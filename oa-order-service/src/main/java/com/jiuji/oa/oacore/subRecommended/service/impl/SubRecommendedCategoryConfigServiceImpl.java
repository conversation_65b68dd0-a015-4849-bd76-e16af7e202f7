package com.jiuji.oa.oacore.subRecommended.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedCategoryConfig;
import com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedCategoryConfigMapper;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedCategoryConfigService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

@Service
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class SubRecommendedCategoryConfigServiceImpl extends ServiceImpl<SubRecommendedCategoryConfigMapper, SubRecommendedCategoryConfig> implements SubRecommendedCategoryConfigService {
}
