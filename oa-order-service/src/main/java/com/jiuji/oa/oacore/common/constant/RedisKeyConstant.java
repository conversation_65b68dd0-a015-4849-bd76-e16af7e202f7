package com.jiuji.oa.oacore.common.constant;

/**
 * @author: gengjiaping
 * @date: 2019/11/18
 */
public class RedisKeyConstant {
    /**
     * 物流价格配置  缓存半天
     */
    public static final String wuliuPriceConfigureKey = "wuliuPriceConfigureKey";
    /**
     * 国补附件下载
     */
    public static final String GB_ATTACHEMNT_DOWNLOAD="gbDown:";
    /**
     * 商品信息前缀
     */
    public static final String PRODUCTINFO_PREFIX = "urn:productinfomodel:";

    // 所有在职员工
    public static final String CH999USER = "Ch999UserKey";

    /**
     * 贵州串号调拨-门店 key
     */
    public static final String GZ_IMEI_STORE_KEY = "gz:imei:store";

    /**
     * 贵州串号调拨-门店 (24h) key
     */
    public static final String GZ_IMEI_STORE_24H_KEY = "gz:imei:store:24h";

    /**
     * 自动计算提成key
     */
    public static final String AUTO_CALCULATE_SALARY_KEY = "order:auto:calculate:salary:ch999Ids";

    /**
     * 薪酬每日结存报表
     */
    public static final String CUSTOM_SALARY_REPORT = "order:custom:salary:report";

    /**
     * 自动计算提成标记
     */
    public static final String AUTO_CALCULATE_SALARY_FLAG = "order:auto:calculate:salary:flag";
    /**
     * ip相关信息缓存key，缓存7天
     */
    public static final String IP_REDIS_KEY = "order:ip:info:";
    /**
     * ip相关信息缓存key，缓存7天 v2 原先的时间太长，更换了逻辑清除不完，就直接换一个新的key
     */
    public static final String IP_REDIS_KEY_V2 = "order:ip:info:v2:";

    /** 服务器文件缓存*/
    public static final String EXCEL_CACHE_META = "orderservice:excelCacheMeta:{}";

    /** 美图同步上一次库存量  参数为平台编码 美团除外, 值为 Meituan*/
    public static final String SYNC_MEITUAN_PRODUCT_COUNT = "orderservice:sync{}ProductCount:{}";

    /** 平台增量同步上一次最大时间 参数为平台编码 美团除外, 值为 meituan*/
    public static final String MEITUAN_INCREMENTAL_LAST_TIME = "orderservice:{}Incremental:lastTime";

    /** 美团取消订单通知缓存 确保同一订单只通知一次 */
    public static final String MEITUAN_CANCEL_ORDER_NOTICE = "orderservice:meituanCancelOrderNotice:{}";

    /** 指定url 打印请求参数 方便用户问题复现, 例如: /orderservice/api/orderManage/getMySubList*/
    public static final String PRINT_URL_REQ_PARAM = "print_url_req_param:{}";

    /** 宝尊最近一次oa接口调用耗时 */
    public static final String BAOZUN_S47_LAST_COST_TIME = "orderservice:baozunS47:createOaSub:lastCostTime";

    /** 宝尊s47接口1s并发量 */
    public static final String BAOZUN_S47_CURRENT_CONCURRENCY = "orderservice:baozunS47:currentConcurrency";

    /** 美团团购token缓存key */
    public static final String MEITUAN_DIANPIN_TOKEN = "orderservice:meituan:dianpin:token";
    /** 美团团购token缓存key */
    public static final String MEITUAN_TUANGOU_TOKEN = "orderservice_meituan_tuangou_token_{}_{}";
    /** 美团团购token缓存key */
    public static final String MEITUAN_TUANGOU_TOKEN1 = "orderservice_meituan_tuangou_token_{}_{}_{}";
    /** 美团团购token缓存key */
    /** 抖音团购token缓存key */
    public static final String DOUYIN_TUANGOU_TOKEN = "orderservice:douyin:tuangou:token";
    /** 抖音团购token缓存key */
    public static final String MEITUAN_DIANPIN_OPENSHOPUUID = "orderservice:meituan:dianpin:openshopuuid:";
    /** 快手团购token缓存key */
    public static final String KUAI_SHOU_TOKEN = "orderservice:kuai:shou:token";
    /** 美团账单header缓存key */
    public static final String MEITUAN_FINANCE_TOKEN = "orderservice:meituan:finance:token:{}";
    /** 最后一次美团账单处理时间 */
    public static final String MEITUAN_FINANCE_LAST_BILL_DATE = "orderservice:meituan:finance:lastbilldate:{}";
    /** 淘宝token缓存key */
    public static final String TAO_BAO_TOKEN = "orderservice:tao:bao:token:{}";
    /**抖音小时达token缓存key*/
    public static final String DOUDIAN_ACCESS_TOKEN_SHOP_ID = "doudian_access_token_shop_id_{}_v1";

    /** 售后评价的缓存key */
    public static final String EVALUATE_SHOUHOU_KEY = "orderservice:evaluate:shouhou";

    /**库表最大键值 变量 dbname table id列名 类型列名 类型值*/
    public static final String SQLSERVER_DB_TABLE_MAX_ID_KEY = "sqlserver_db_{}_table_{}_max_{}_{}_{}";

    /**是否在历史库中 变量 dbname table id列名 类型列名 类型值*/
    public static final String CH999OANEW_TABLE_ID_IS_HISTORY_KEY = "ch999oanew_table_{}_{}_{}_{}_{}_is_history";

    /**终止重试的key*/
    public static final String STOP_BAOZUN_RETRY = "orderservice:stop_baozun_retry";
}
