# data1
sqlserver.data1.host=master.web.sqlserver.service.ch999.cn
sqlserver.data1.port=1433
## \u6570\u636E\u5E93 sqlserver
sqlserver.data2.host=data2.ch999.cn
sqlserver.data2.port=1433
## business db
sqlserver.data7.host=data7.ch999.cn
sqlserver.data7.port=1433
# data6 sqlserver
sqlserver.data6.host=data6.ch999.cn
sqlserver.data6.port=1433
## oareadnodes db
sqlserver.oareadnodes.host=readnodes.oamssql.serv.ch999.cn
sqlserver.oareadnodes.port=1433
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanew.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=ch999oanewGreen
sqlserver.ch999oanew.password=SMns^!N@)!@y@#$dOpV3
## sqlserver:ch999oanew2
sqlserver.ch999oanew2.host=*************
sqlserver.ch999oanew2.port=1433
sqlserver.ch999oanew2.dbname=ch999oanew
sqlserver.ch999oanew2.username=oaUserChwM
sqlserver.ch999oanew2.password=o!@$^#!OnFkMgf
sqlserver.ch999oanew2.is-enable=true

## sqlserver:office2
sqlserver.office2.host=*************
sqlserver.office2.port=1433
sqlserver.office2.dbname=office
sqlserver.office2.username=officeUserCg
sqlserver.office2.password=P$%*Jgd@#%!Bj$
sqlserver.office2.is-enable=true
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanewReport.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=ch999oanewGreen
sqlserver.ch999oanewReport.password=SMns^!N@)!@y@#$dOpV3

## sqlserver:ershou
sqlserver.ershou.host=${sqlserver.data1.host}
sqlserver.ershou.port=${sqlserver.data1.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=ch999hsDataGreen
sqlserver.ershou.password=T5z#2u$8M^A9b%1nW4@j

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=his.sqlserver.ch999.cn
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oahis
sqlserver.ch999oanewHis.username=ch999oahis
sqlserver.ch999oanewHis.password=VS35!Q7@CDGwFF91
sqlserver.ch999oanewHis.is-enable=true

## sqlserver:office
sqlserver.office.host=${sqlserver.oareadnodes.host}
sqlserver.office.port=${sqlserver.oareadnodes.port}
sqlserver.office.dbname=office
sqlserver.office.username=officeGreen
sqlserver.office.password=MhS@#$KhEW#$Hgs#$WsA
## sqlserver:oanewHis
sqlserver.oanewHis.host=his.sqlserver.ch999.cn
sqlserver.oanewHis.port=${sqlserver.data7.port}
sqlserver.oanewHis.dbname=ch999oahis
sqlserver.oanewHis.username=ch999oahis
sqlserver.oanewHis.password=VS35!Q7@CDGwFF91
sqlserver.oanewHis.is-enable=true
## sqlserver:oanewHisWrite
sqlserver.oanewHisWrite.host=his.sqlserver.ch999.cn
sqlserver.oanewHisWrite.port=${sqlserver.data7.port}
sqlserver.oanewHisWrite.dbname=ch999oahis
sqlserver.oanewHisWrite.username=ch999oahisManage
sqlserver.oanewHisWrite.password=ch999oahisManage
sqlserver.oanewHisWrite.is-enable=true
## mysql
mysql.url=master.main.mysql8.service.ch999.cn:3306
## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=oa_core00&*(
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc#$%
## mysql:oa_log
mysql.oa_log.url=main.tidb.ch999.cn:9383
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=ueSCNX3dIoM

## sqlserver:officeWrite
sqlserver.officeWrite.host=*************
sqlserver.officeWrite.port=${sqlserver.data2.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=oaOffice
sqlserver.officeWrite.password=o#!@$DMas

## sqlserver:oanewWrite
sqlserver.oanewWrite.host=${sqlserver.data6.host}
sqlserver.oanewWrite.port=${sqlserver.data6.port}
sqlserver.oanewWrite.dbname=ch999oanew
sqlserver.oanewWrite.username=ch999oanewGreen
sqlserver.oanewWrite.password=SMns^!N@)!@y@#$dOpV3

## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data6.host}
sqlserver.smallpro_write.port=${sqlserver.data6.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=9jioa
sqlserver.smallpro_write.password=9ji!D#%!$AWWFG!#

## sqlserver:web999_other
sqlserver.web999_other.host=${sqlserver.data1.host}
sqlserver.web999_other.port=${sqlserver.data1.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=web999_other
sqlserver.web999_other.password=#*@(@aoe8@)!#**

## StarRocks
starrocks.url=dwcluster.ch999.cn:19030
starrocks.dbname=ods_jiuji
starrocks.username=dev_operation
starrocks.password=osPIRH2dT47

redis.oa.host=master.main.redis.service.ch999.cn
redis.oa.port=6379
redis.oa.password=JiujiOa2020
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## \u65E5\u5FD7
logging.config.path=classpath:log/log4j2-jiuji.xml
## mongoDB
mongodb.url1=*************************************************************************
## image
image.upload.url=http://*************:9333
image.del.url=http://*************:5083
image.select.url=https://img2.ch999img.com/

#jiuji.weborder.enableHis=true
# rabbitMq-oaAsync
rabbitmq.master.url=*************
rabbitmq.master.port=5672
rabbitmq.master.username=oaAsync
rabbitmq.master.password=oaAsyncpwd
rabbitmq.master.vhost=oaAsync
# rabbitMq-oa
rabbitmq.oa.url=master.main.rabbitmq.service.ch999.cn
rabbitmq.oa.port=5672
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999
rabbitmq.oa.vhost=oa

consul.host=${spring.cloud.client.hostname}
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
spring.messages.basename=i18n/url
instance-zone=9ji

sqlserver.datasource.max-pool-size=50
mysql.datasource.max-pool-size=50


sms.url=http://sms.ch999.com.cn/?test=
sms.send.email.url=http://sms.ch999.com.cn/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx


jiuji.seata.enabled=false
jiuji.seata.config.consul.address=*************:8500
jiuji.seata.config.consul.prefix=seata-config/orderservice
jiuji.seata.registry.consul.address=*************:8500
jiuji.seata.registry.consul.cluster=seata-jiuji


## sqlserver:Financial
sqlserver.Financial.host=${sqlserver.oareadnodes.host}
sqlserver.Financial.port=${sqlserver.oareadnodes.port}
sqlserver.Financial.dbname=Financial
sqlserver.Financial.username=jiahai_Financial
sqlserver.Financial.password=PR$COmlE#oEhpOtm

jiuji.sys.moa=https://moa.9ji.com
jiuji.sys.pc=https://oa.9ji.com
jiuji.sys.web=https://www.9ji.com
jiuji.sys.inWcf=http://inwcf.ch999.cn
jiuji.sys.oaWcf=http://oawcf2.ch999.cn

autoCalculateSalary.xtenant=10000

# ÑÇ¶¡±£ÏÕ¶Ô½ÓµØÖ·url²ÎÊýµÈ
# Í¬²½Ô±¹¤ÐÅÏ¢µØÖ·
yading.sync-user-url: http://yading.viphfkj.com/jiuji/User/synchronizaMember
# ×¢²á±£ÏÕµÄµØÖ·
yading.insurance-register-url: http://train.9000ji.com/RightApply/addApply
# ½Ó¿Ú¼ÓÇ©µÄÇ©Ãûkey
yading.sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa


apollo.url=http://*************:8010,http://**************:8010,http://**************:8010
apollo.file=application-order.yml

