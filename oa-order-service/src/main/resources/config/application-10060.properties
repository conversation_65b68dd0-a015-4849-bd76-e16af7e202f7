## sqlserver:ch999oan<PERSON>
sqlserver.ch999oanew.host=**************
sqlserver.ch999oanew.port=1433
sqlserver.ch999oanew.dbname=ch999oanew__10060
sqlserver.ch999oanew.username=ch999oanew__10060
sqlserver.ch999oanew.password=ch999oanewBVOyJ
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=**************
sqlserver.ch999oanewReport.port=1433
sqlserver.ch999oanewReport.dbname=ch999oanew__10060
sqlserver.ch999oanewReport.username=ch999oanew__10060
sqlserver.ch999oanewReport.password=ch999oanewBVOyJ
## sqlserver:office
sqlserver.office.host=**************
sqlserver.office.port=1433
sqlserver.office.dbname=office__10060
sqlserver.office.username=office__10060
sqlserver.office.password=officeGmuGA
## sqlserver:oaOffice
sqlserver.oaOffice.host=**************
sqlserver.oaOffice.port=1433
sqlserver.oaOffice.dbname=office__10060
sqlserver.oaOffice.username=office__10060
sqlserver.oaOffice.password=officeGmuGA
## sqlserver:officeWrite
sqlserver.officeWrite.host=**************
sqlserver.officeWrite.port=1433
sqlserver.officeWrite.dbname=office__10060
sqlserver.officeWrite.username=office__10060
sqlserver.officeWrite.password=officeGmuGA
## sqlserver:after_write
sqlserver.after_write.host=**************
sqlserver.after_write.port=1433
sqlserver.after_write.dbname=ch999oanew__10060
sqlserver.after_write.username=ch999oanew__10060
sqlserver.after_write.password=ch999oanewBVOyJ
## sqlserver:web99
sqlserver.web999.host=**************
sqlserver.web999.port=1433
sqlserver.web999.dbname=web999__10060
sqlserver.web999.username=web999__10060
sqlserver.web999.password=web999hpHQK
## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=**************
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oanew__10060
sqlserver.ch999oanewHis.username=ch999oanew__10060
sqlserver.ch999oanewHis.password=ch999oanewBVOyJ
## sqlserver:oanewwrite
sqlserver.oanewWrite.host=**************
sqlserver.oanewWrite.port=1433
sqlserver.oanewWrite.dbname=ch999oanew__10060
sqlserver.oanewWrite.username=ch999oanew__10060
sqlserver.oanewWrite.password=ch999oanewBVOyJ
## sqlserver:smallpro_write
sqlserver.smallpro_write.host=**************
sqlserver.smallpro_write.port=1433
sqlserver.smallpro_write.dbname=ch999oanew__10060
sqlserver.smallpro_write.username=ch999oanew__10060
sqlserver.smallpro_write.password=ch999oanewBVOyJ
## mysql:oa_nc
mysql.oa_nc.url=**************:3306
mysql.oa_nc.dbname=oa_nc__10060
mysql.oa_nc.username=oa_nc__10060
mysql.oa_nc.password=oa_ncoMD
## mysql:oa_core
mysql.oa_core.url=**************:3306
mysql.oa_core.dbname=oa_core__10060
mysql.oa_core.username=oa_core__10060
mysql.oa_core.password=oa_corenxm
## mysql
mysql.manage_training.url=**************:3306
mysql.manage_training.dbname=manage_training__10060
mysql.manage_training.username=manage_training__10060
mysql.manage_training.password=manage_trainingdAe
## midl
consul.host=**************
consul.port=8500

## redis
redis.oa.host=**************
redis.oa.port=6396
redis.oa.password=google99
redis.oa.url=google99@**************:6396
## rabbitmq
rabbitmq.printer.url=**************
rabbitmq.printer.port=5672
rabbitmq.printer.vhost=printer__10060
rabbitmq.printer.username=printer__10060
rabbitmq.printer.password=caSeu

rabbitmq.oaAsync.url=**************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync__10060
rabbitmq.oaAsync.username=oaAsync__10060
rabbitmq.oaAsync.password=rMkWl

rabbitmq.oa.url=**************
rabbitmq.oa.port=5672
rabbitmq.oa.vhost=oa__10060
rabbitmq.oa.username=oa__10060
rabbitmq.oa.password=MeLxU

rabbitmq.msgcenter.url=storemq.ch999.cn
rabbitmq.msgcenter.port=35672
rabbitmq.msgcenter.vhost=msgcenter
rabbitmq.msgcenter.username=msgcenter
rabbitmq.msgcenter.password=ch999

rabbitmq.master.url=**************
rabbitmq.master.port=5672
rabbitmq.master.vhost=oaAsync__10060
rabbitmq.master.username=oaAsync__10060
rabbitmq.master.password=rMkWl
## mongo
mongodb.url1=*********************************************************************
mongodb.ch999oa.url=ch999oa__10060:KZN1ZHvE@**************:27017/ch999oa__10060
url.source.path=i18n/url
logging.config.path=classpath:log/log4j2-saas.xml
messages.basename=i18n/abstractInfo,i18n/saas
## image
image.upload.url=http://**************:9333
image.del.url=http://**************:5083
image.select.url=https://img.jsjinzhen.com/
jiuji.sys.xtenant=10060
jiuji.sys.moa=https://moa.jsjinzhen.com
jiuji.sys.pc=https://oa.jsjinzhen.com
jiuji.xtenant=60000
spring.cloud.consul.discovery.instance-zone=10060
instance-zone=10060
sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100
sms.url=http://sms.hwx360.com/?test=
sms.send.email.url=http://sms.jsjinzhen.com/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx
url.uploadImgUrl=http://data3:9333
url.delImgUrl=http://data3:5083
url.selectImgUrl=https://img.jsjinzhen.com/
url.oa-push-info=http://inwcf.jsjinzhen.com:2988/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s


jiuji.seata.enabled=false
jiuji.seata.config.consul.address=xxx
jiuji.seata.config.consul.prefix=xxx
jiuji.seata.registry.consul.address=xxx
jiuji.seata.registry.consul.cluster=xxx