package com.jiuji.oa.oacore.thirdplatform.tuangou.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TuangouPlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.constant.DouyinLifeConstant;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.service.DouyinLifeService;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.utils.DouyinLifeUtil;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.vo.DouyinLifeClientToken;
import com.jiuji.oa.oacore.thirdplatform.tuangou.service.DouyinTuangouService;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.CertificateQueryData;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.TuangouDataRes;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 抖音生活服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class DouyinTuangouServiceImpl implements DouyinTuangouService {
    @Resource
    private DouyinLifeService douyinLifeService;

    @Override
    public boolean isMyPlatfrom(String platfrom) {
        return TuangouPlatfromEnum.DYTG.getPlatfromCode().equals(platfrom);
    }

    @Override
    public String certificatePrepare (CertificatePrepareReq req) {
        log.info("调用验券准备接口req={}", JsonUtils.toJson(req));
        DouyinLifeClientToken accessToken = douyinLifeService.getAccessToken(req.getAreaId());
        Map<String, Object> params = new HashMap<>();
        params.put("encrypted_data", req.getEncryptedData());
        params.put("code", req.getCode());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CERTIFICATE_PREPARE_URL;
        return DouyinLifeUtil.get(url, params, accessToken.getAccessToken());
    }

    @Override
    public String certificateVerify (CertificateVerifyReq req) {
        log.info("调用验券准备接口req={}", JsonUtils.toJson(req));
        DouyinLifeClientToken accessToken = douyinLifeService.getAccessToken(req.getAreaId());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CERTIFICATE_VERIFY_URL;
        req.setAreaId(null);
        return DouyinLifeUtil.post(url, JsonUtils.toJson(req), accessToken.getAccessToken());
    }

    @Override
    public String certificateCancel (CertificateCancelReq req) {
        log.info("调用撤销核券接口req={}", JsonUtils.toJson(req));
        DouyinLifeClientToken accessToken = douyinLifeService.getAccessToken(req.getAreaId());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CERTIFICATE_CANCEL_URL;
        req.setAreaId(null);
        return DouyinLifeUtil.post(url, JsonUtils.toJson(req), accessToken.getAccessToken());
    }

    @Override
    public String certificateGet (CertificateGetReq req) {
        log.info("调用券状态查询接口req={}", JsonUtils.toJson(req));
        Map<String, Object> params = new HashMap<>();
        params.put("encrypted_code", req.getEncryptedCode());
        DouyinLifeClientToken accessToken = douyinLifeService.getAccessToken(req.getAreaId());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CERTIFICATE_GET_URL;
        return DouyinLifeUtil.get(url, params, accessToken.getAccessToken());
    }

    @Override
    public String certificateQuery (CertificateQueryReq req) {
        log.info("调用券状态批量查询接口req={}", JsonUtils.toJson(req));
        Map<String, Object> params = new HashMap<>();
        params.put("encrypted_code", req.getEncryptedCode());
        params.put("order_id", req.getOrderId());
        DouyinLifeClientToken accessToken = douyinLifeService.getAccessToken(req.getAreaId());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CERTIFICATE_QUERY_URL;
        String result = DouyinLifeUtil.get(url, params, accessToken.getAccessToken());
        TuangouDataRes<CertificateQueryData> res = JsonUtils.fromJson(result, new TypeReference<TuangouDataRes<CertificateQueryData>>() {
        });
        if (Objects.nonNull(res) && Objects.nonNull(res.getData())) {
            res.getData().setCancelLimitTime(NumberConstant.SIXTY);
        }
        return JsonUtils.toJson(res);
    }
}
