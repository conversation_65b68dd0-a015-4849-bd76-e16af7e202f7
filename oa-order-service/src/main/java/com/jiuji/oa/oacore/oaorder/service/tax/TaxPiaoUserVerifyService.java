package com.jiuji.oa.oacore.oaorder.service.tax;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.oaorder.po.tax.TaxPiaoUserVerifyBo;
import com.jiuji.oa.oacore.oaorder.req.tax.TaxPiaoVerifyReq;
import com.jiuji.oa.oacore.oaorder.res.tax.TaxPiaoUserVerifyRes;
import com.jiuji.tc.common.vo.R;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发票验证用户服务类
 * <AUTHOR>
 * @since 2022/7/6 14:34
 */
public interface TaxPiaoUserVerifyService extends IService<TaxPiaoUserVerifyBo> {
    /**
     * 通过订单号保存发票验证信息
     * @param subId
     * @param type 订单类型 0 新机单 1 售后单 2 良品单 {@link com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes.TaxSubTypeEnum}
     * @return
     */
    @RepeatSubmitCheck(argIndexs = {0})
    @Transactional(rollbackFor = {Exception.class})
    R<TaxPiaoUserVerifyRes> saveBySubId(Integer subId, Integer type);

    /**
     * 获取题目信息
     * 1. 如果不存在直接创建题目信息
     * 2. 由创建方法控制并发的情况
     * @param subId
     * @param type 订单类型 0 新机单 1 售后单 2 良品单 {@link com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes.TaxSubTypeEnum}
     * @return
     */
    R<TaxPiaoUserVerifyRes> getTaxPiaoUserVerifyInfo(Integer subId, Integer type);

    /**
     * 根据订单号和类型获取单条数据
     * @param subId
     * @param type 订单类型 0 新机单 1 售后单 2 良品单 {@link com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes.TaxSubTypeEnum}
     * @return
     */
    TaxPiaoUserVerifyBo getOne(Integer subId, Integer type);

    /**
     * 校验提交
     * @param taxPiaoVerifyReq
     * @return
     */
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{taxPiaoVerifyReq.id}")
    @Transactional(rollbackFor = {Exception.class})
    R<Boolean> answer(TaxPiaoVerifyReq taxPiaoVerifyReq);
}
