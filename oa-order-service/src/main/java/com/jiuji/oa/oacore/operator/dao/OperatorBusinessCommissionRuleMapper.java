package com.jiuji.oa.oacore.operator.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.operator.po.OperatorBusinessCommissionRule;
import com.jiuji.oa.oacore.operator.vo.req.OperatorCommissionQueryReq;
import com.jiuji.oa.oacore.operator.vo.res.OperatorCommissionRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 运营商提点配置(OperatorBusinessCommissionRule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-06 15:31:30
 */
@Mapper
public interface OperatorBusinessCommissionRuleMapper extends BaseMapper<OperatorBusinessCommissionRule> {

    /**
     * 分页查询运营商提点配置
     * @param req
     * @return
     */
    Page<OperatorCommissionRes> selectByPage(@Param("page") Page<OperatorCommissionRes> page,
                                             @Param("req") OperatorCommissionQueryReq req);
}

