package com.jiuji.oa.oacore.thirdplatform.tuangou.service;

import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.QueryCouponCodeReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.UpdateCouponStatusReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.QueryCouponCodeRes;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.UpdateCouponStatusRes;
import com.jiuji.tc.common.vo.R;

import java.util.List;

public interface TuangouCouponCodeService {
    /**
     * 订单查询团购券信息
     * @param req
     * @return
     */
    List<QueryCouponCodeRes> getCouponCodeListBySub(QueryCouponCodeReq req);

    /**
     * 修改团购券状态
     * @param req
     * @return
     */
    R<UpdateCouponStatusRes> updateCouponStatusById(UpdateCouponStatusReq req);
}
