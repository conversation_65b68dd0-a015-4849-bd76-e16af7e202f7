
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=**************
sqlserver.ch999oanew.port=1433
sqlserver.ch999oanew.dbname=ch999oanew__10053
sqlserver.ch999oanew.username=ch999oanew__10053
sqlserver.ch999oanew.password=ch999oanewtlwgE
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=**************
sqlserver.ch999oanewReport.port=1433
sqlserver.ch999oanewReport.dbname=ch999oanew__10053
sqlserver.ch999oanewReport.username=ch999oanew__10053
sqlserver.ch999oanewReport.password=ch999oanewtlwgE

## sqlserver:ershou
sqlserver.ershou.host=sqlserver.serv.xn.saas.ch999.cn
sqlserver.ershou.port=1433
sqlserver.ershou.dbname=ershou__10053
sqlserver.ershou.username=ershou__10053
sqlserver.ershou.password=ershouoWCxx

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=**************
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oanew__10053
sqlserver.ch999oanewHis.username=ch999oanew__10053
sqlserver.ch999oanewHis.password=ch999oanewtlwgE

## sqlserver:office
sqlserver.office.host=**************
sqlserver.office.port=1433
sqlserver.office.dbname=office__10053
sqlserver.office.username=office__10053
sqlserver.office.password=officeHTHmR

## sqlserver:officeWrite
sqlserver.officeWrite.host=**************
sqlserver.officeWrite.port=1433
sqlserver.officeWrite.dbname=office__10053
sqlserver.officeWrite.username=office__10053
sqlserver.officeWrite.password=officeHTHmR

## sqlserver:smallpro_write
sqlserver.smallpro_write.host=**************
sqlserver.smallpro_write.port=1433
sqlserver.smallpro_write.dbname=ch999oanew__10053
sqlserver.smallpro_write.username=ch999oanew__10053
sqlserver.smallpro_write.password=ch999oanewtlwgE

## mysql:oa_core
mysql.oa_core.url=**************:3306
mysql.oa_core.dbname=oa_core__10053
mysql.oa_core.username=oa_core__10053
mysql.oa_core.password=oa_coreWYg

## midl
consul.host=**************
consul.port=8500

## redis
redis.oa.host=**************
redis.oa.port=6380
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

rabbitmq.master.url=**************
rabbitmq.master.port=5672
rabbitmq.master.vhost=oaAsync__10053
rabbitmq.master.username=oaAsync__10053
rabbitmq.master.password=xQEhD

mongodb.url1=*********************************************************************

logging.config.path=classpath:log/log4j2-saas.xml
## image
image.upload.url=http://**************:9333
image.del.url=http://**************:5083
image.select.url=https://img.hlto2o.com/
jiuji.sys.xtenant=10053
spring.cloud.consul.discovery.instance-zone=10053
instance-zone=10053

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100

sms.url=http://sms.hlto2o.com/?test=
sms.send.email.url=http://sms.hlto2o.com/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx

jiuji.seata.enabled=false
jiuji.seata.config.consul.address=xxx
jiuji.seata.config.consul.prefix=xxx
jiuji.seata.registry.consul.address=xxx
jiuji.seata.registry.consul.cluster=xxx