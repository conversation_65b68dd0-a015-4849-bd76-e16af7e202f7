package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.util;

import java.util.HashMap;
import java.util.Map;

public class CommonConstant {


    public static final Map<Integer,String> HUIJB_SKUID_MAPPING;
    public static final String SERVICE_NAME="汇机保";
    public static final String CONTENT_TYPE_JSON = "application/json";

    static {
        // 汇机保
        HUIJB_SKUID_MAPPING = new HashMap<>();
        HUIJB_SKUID_MAPPING.put(406674,"PD23032417594785637432");
        HUIJB_SKUID_MAPPING.put(406675,"PD23032418010133383795");
        HUIJB_SKUID_MAPPING.put(406677,"PD23031723283222460867");
        HUIJB_SKUID_MAPPING.put(406678,"PD23031723285516493631");
        HUIJB_SKUID_MAPPING.put(406679,"PD23031723291227756105");
        HUIJB_SKUID_MAPPING.put(406680,"PD23031723292849913584");
        HUIJB_SKUID_MAPPING.put(406681,"PD23031723294441209726");
        HUIJB_SKUID_MAPPING.put(391803,"PD23031609291134727683");
        HUIJB_SKUID_MAPPING.put(406502,"PD23031609495354126516");
        HUIJB_SKUID_MAPPING.put(406503,"PD23031609543816863733");
        HUIJB_SKUID_MAPPING.put(406504,"PD23031609570396584526");
        HUIJB_SKUID_MAPPING.put(406505,"PD23031615502634825833");
        HUIJB_SKUID_MAPPING.put(406512,"PD23031616110761781343");
        HUIJB_SKUID_MAPPING.put(406513,"PD23031616114845922211");
        HUIJB_SKUID_MAPPING.put(406514,"PD23031616132130155899");
        HUIJB_SKUID_MAPPING.put(406506,"PD23031616004401365510");
        HUIJB_SKUID_MAPPING.put(406507,"PD23031616011738874821");
        HUIJB_SKUID_MAPPING.put(406508,"PD23031616022073899230");
        HUIJB_SKUID_MAPPING.put(406509,"PD23031616031749249533");
        HUIJB_SKUID_MAPPING.put(406510,"PD23031616034245104532");
        HUIJB_SKUID_MAPPING.put(411133,"PD532906177");
        HUIJB_SKUID_MAPPING.put(411126,"PD323593436");
        HUIJB_SKUID_MAPPING.put(411127,"PD931206517");
        HUIJB_SKUID_MAPPING.put(411128,"PD415745616");
        HUIJB_SKUID_MAPPING.put(411129,"PD735353618");
        HUIJB_SKUID_MAPPING.put(411130,"PD914623243");
        HUIJB_SKUID_MAPPING.put(411131,"PD570407281");
        HUIJB_SKUID_MAPPING.put(411132,"PD717613366");
        HUIJB_SKUID_MAPPING.put(411134,"PD989895640");
        HUIJB_SKUID_MAPPING.put(411135,"PD893594592");
        HUIJB_SKUID_MAPPING.put(411136,"PD694678631");
        HUIJB_SKUID_MAPPING.put(411137,"PD097480630");
        HUIJB_SKUID_MAPPING.put(411138,"PD567141521");
        HUIJB_SKUID_MAPPING.put(411139,"PD183804170");
        HUIJB_SKUID_MAPPING.put(411140,"PD865289231");
        HUIJB_SKUID_MAPPING.put(433729,"PD316887475");
        HUIJB_SKUID_MAPPING.put(442257,"PD132641768");
        HUIJB_SKUID_MAPPING.put(462436,"PD220560157");
        HUIJB_SKUID_MAPPING.put(462437,"PD746731235");
        HUIJB_SKUID_MAPPING.put(462438,"PD192077813");
        HUIJB_SKUID_MAPPING.put(462439,"PD432854015");
        HUIJB_SKUID_MAPPING.put(462440,"PD867738104");
    }


    /**
     * map 反转
     * @param originalMap
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> Map<V, K> reverseMap(Map<K, V> originalMap) {
        Map<V, K> reversedMap = new HashMap<>();
        for (Map.Entry<K, V> entry : originalMap.entrySet()) {
            K key = entry.getKey();
            V value = entry.getValue();
            reversedMap.put(value, key);
        }
        return reversedMap;
    }
}
