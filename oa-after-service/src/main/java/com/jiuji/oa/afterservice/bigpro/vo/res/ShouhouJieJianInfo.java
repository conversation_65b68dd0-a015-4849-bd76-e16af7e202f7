package com.jiuji.oa.afterservice.bigpro.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ShouhouJieJianInfo {

    @ApiModelProperty(value = "维修单号")
    private Integer shouhouId;

    @ApiModelProperty(value = "接件门店ID")
    private Integer areaId;

    @ApiModelProperty(value = "接件门店")
    private String area;

    @ApiModelProperty(value = "接件人")
    private String inUser;

    @ApiModelProperty(value = "机型")
    private String productColor;
}
