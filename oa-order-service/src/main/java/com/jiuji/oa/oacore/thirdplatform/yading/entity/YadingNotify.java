package com.jiuji.oa.oacore.thirdplatform.yading.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.enums.InsuranceCompanyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("yading_notify")
public class YadingNotify extends Model<YadingNotify> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单明细id
     */
    @TableField("detail_id")
    private Long detailId;

    /**
     * 保险服务状态记录表t_yading_service_state的主键id
     */
    @TableField("service_state_id")
    private Long serviceStateId;

    /**
     * 处理状态，默认为0:未处理，1为已处理（处理成功），2为处理失败（数据异常），3为处理失败（程序异常）
     * @see com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingNotifyHandleStateEnum
     */
    @TableField("handle_state")
    private Integer handleState;

    /**
     * 通知类型
     */
    @TableField("notify_type")
    private Integer notifyType;
    /**
     * 原始回调信息
     */
    @TableField("original_msg")
    private String originalMsg;


    /**
     * 消息处理重试次数
     */
    @TableField("retries")
    private Integer retries;

    /**
     * 失败原因
     */
    @TableField("fail_cause")
    private String failCause;


    /**
     * 逻辑删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String DETAIL_ID = "detail_id";

    public static final String HANDLE_STATE = "handle_state";

    public static final String NOTIFY_TYPE = "notify_type";

    public static final String ORIGINAL_MSG = "original_msg";

    public static final String IS_DELETE = "is_delete";

}
