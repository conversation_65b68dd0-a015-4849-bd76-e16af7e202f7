package com.jiuji.oa.oacore.thirdplatform.baozun.vo.res;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.oacore.common.util.FieldModified;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date ${Date} 18:37
 * @Description 宝尊平台接入
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "宝尊子商户查询实体类")
public class BzTenantAppRes implements Serializable {
    private static final long serialVersionUID = 7554453001271572347L;
    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id")
    private Integer fkTenantId;

    /**
     * 平台id
     */
    @ApiModelProperty(value = "子商户id")
    private Integer id;

    /**
     * 子商户渠道
     */
    @ApiModelProperty(value = "子商户渠道名称")
    private String childMerchantName;

    /**
     * 下单会员id
     */
    @ApiModelProperty(value = "下单会员id")
    private Integer userId;

    /**
     * 平台补贴收款科目
     */
    @ApiModelProperty(value = "平台补贴收款科目")
    private String platformKemu;

    /**
     * 客户实付收款科目
     */
    @ApiModelProperty(value = "客户实付收款科目")
    private String customerKemu;

    /**
     * 货到付款收款科目
     */
    @ApiModelProperty(value = "货到付款收款科目")
    private String goodsArrivalKemu;

    /**
     * 退款方式
     */
    @ApiModelProperty(value = "退款方式")
    private String refundType;
}