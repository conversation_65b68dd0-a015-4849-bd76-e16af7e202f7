package com.jiuji.oa.oacore.subRecommended.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


@Data
@Accessors(chain = true)
@TableName("sub_recommended_package_config")
public class SubRecommendedPackageConfig {

    /**
     * 自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 关联配置(sub_recommended_main_config)id
     */
    @TableField("fk_config_id")
    private Integer fkConfigId;

    @TableField(exist = false)
    private String uuid;

    /**
     * 套餐名称
     */
    @TableField("name")
    private String name;

    /**
     * 是否主要选项
     */
    @TableField("is_main")
    private Integer isMain;

    /**
     * 排序
     */
    @TableField("seq_num")
    private Integer seqNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除
     */
    @TableField("is_del")
    @TableLogic
    private Boolean isDel;


}
