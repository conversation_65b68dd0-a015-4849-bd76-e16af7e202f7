package com.jiuji.oa.oacore.thirdplatform.douyinlife.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/4 16:27
 */
@Data
public class CertificateCancelReq {
    /**
     * 代表券码一次核销的唯一标识（验券时返回）(次卡撤销多次时请填0)
     */
    @JSONField(name = "verify_id")
    @JsonProperty("verify_id")
    private String verifyId;
    /**
     * 代表一张券码的标识（验券时返回）
     */
    @JSONField(name = "certificate_id")
    @JsonProperty("certificate_id")
    private String certificateId;
    /**
     * 取消核销总次数（多次卡商品可传，优先级低于verify_id）
     */
    @JSONField(name = "times_card_cancel_count")
    @JsonProperty("times_card_cancel_count")
    private Integer timesCardCancelCount;
    /**
     * 撤销核销幂等操作，主要针对次卡，避免因超时等原因在短时间内重复请求导致撤销多次（幂等有效期1小时）
     */
    @JSONField(name = "cancel_token")
    @JsonProperty("cancel_token")
    private String cancelToken;

    private Integer areaId;
}
