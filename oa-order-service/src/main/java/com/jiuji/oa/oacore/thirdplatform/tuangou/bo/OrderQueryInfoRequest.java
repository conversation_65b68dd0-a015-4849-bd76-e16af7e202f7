package com.jiuji.oa.oacore.thirdplatform.tuangou.bo;

import com.dianping.openapi.sdk.api.base.request.BaseSignRequest;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import java.util.List;
import java.util.Map;

/**
 * Created by autosdk on 2019-08-13 10:43:33.
 */
public class OrderQueryInfoRequest extends BaseSignRequest {

    /**
     * 订单ID
     */
    private String order_id ;

    /**
     * 统一订单ID
     */
    private String unified_order_id ;

    /**
     * 需要查询的信息，1:订单，2:券码
     */
    private List<Integer> type ;

    private Gson gson = new Gson();

    public OrderQueryInfoRequest(String app_key, String app_secret, String session) {
        super(app_key, app_secret, session);
    }

    public OrderQueryInfoRequest(String app_key, String app_secret, String session , String order_id , String unified_order_id , List<Integer> type) {
        super(app_key, app_secret, session);
        this.order_id = order_id;
        this.unified_order_id = unified_order_id;
        this.type = type;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getUnified_order_id() {
        return unified_order_id;
    }

    public void setUnified_order_id(String unified_order_id) {
        this.unified_order_id = unified_order_id;
    }

    public List<Integer> getType() {
        return type;
    }

    public void setType(List<Integer> type) {
        this.type = type;
    }

    @Override
    public Map<String, Object> toParams() {
        Map<String, Object> params = Maps.newHashMap();
        if(order_id != null ){
            params.put("order_id", order_id);
        }
        if(unified_order_id != null ){
            params.put("unified_order_id", unified_order_id);
        }
        if(type != null ){
            params.put("type", type);
        }
        return params;
    }

    @Override
    public String toString() {
        return gson.toJson(this);
    }
}
