package com.jiuji.oa.oacore.oaorder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退货单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("return_sub")
@ApiModel(value = "ReturnSub对象", description = "退货单")
public class ReturnSub extends Model<ReturnSub> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前状态【提交、审核、处理、完成】")
    @TableField("states")
    private Integer states;

    @ApiModelProperty(value = "审核步骤，当前已经过多少个审核")
    @TableField("checkState")
    private Integer checkState;

    @TableField("finishTime")
    private LocalDateTime finishTime;

    @TableField("delTime")
    private LocalDateTime delTime;

    @TableField("title")
    private String title;

    @TableField("dtime")
    private LocalDateTime dtime;

    @TableField("type_")
    private Integer type;

    @TableField("piqianhao")
    private Long piqianhao;

    @TableField("insourceid")
    private Integer insourceid;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("area")
    private String area;

    @TableField("pjType")
    private Integer pjType;

    @TableField("realPrice")
    private Double realPrice;

    @TableField("isSend")
    private Boolean send;

    @TableField("mainsubid")
    private Integer mainsubid;

    @TableField("pzid")
    private Integer pzid;

    @TableField("outTime")
    private LocalDateTime outTime;

    @TableField("areaid")
    private Integer areaid;

    @TableField("inuser")
    private String inuser;

    @ApiModelProperty(value = "退货单物流单号")
    @TableField("wuliuid")
    private Integer wuliuid;

    @ApiModelProperty(value = "渠道商是否收到货标记")
    @TableField("isReceive")
    private Boolean received;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
