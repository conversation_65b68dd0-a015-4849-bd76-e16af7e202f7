package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceReport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 10:41
 * @Description
 */
@Data
@Accessors(chain = true)
@ApiModel("售后质保查询接口")
public class ShouhouServiceReportRes implements Serializable {
    private static final long serialVersionUID = -3207204162416107024L;

    /**
     * imei(串号)
     */
    @ApiModelProperty(value = "imei(串号)")
    private String imei;
    /**
     * 设备名称(型号加规格)
     */
    @ApiModelProperty(value = "设备名称(型号加规格)")
    private String deviceName;
    /**
     * 购买单号(订单ID)
     */
    @ApiModelProperty(value = "购买单号(订单ID)")
    private Integer subId;
    /**
     * 购买类型(新机/优品/良品)
     * @see ShouhouServiceReport.PurchaseTypeEnum
     */
    @ApiModelProperty(value = "购买类型(新机/优品/良品)")
    private Integer purchaseType;
    /**
     * 购买类型(新机/优品/良品)
     * @see ShouhouServiceReport.PurchaseTypeEnum
     */
    @ApiModelProperty(value = "购买类型文字")
    private String purchaseTypeName;
    /**
     * 参数
     */
    @ApiModelProperty(value = "详细参数")
    private List<ServiceReportData> serviceReportData;
    /**
     * 枚举
     */
    @ApiModelProperty(value = "枚举值")
    private List<ServiceReportEnum> purchaseTypeEnum;
    /**
     * 枚举
     */
    @ApiModelProperty(value = "检测结果枚举值")
    private List<ServiceReportEnum> faultResultEnum;

    @Data
    @Accessors(chain = true)
    @ApiModel("售后质保详细数据")
    public static class ServiceReportData implements Serializable {
        private static final long serialVersionUID = 8014565732406839531L;
        /**
         * 自增长
         */
        @ApiModelProperty(value = "编号")
        private Integer id;
        /**
         * 检测时间
         */
        @ApiModelProperty(value = "检测时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime checkTime;
        /**
         * 受理单号(维修单号)
         */
        @ApiModelProperty(value = "受理单号(维修单号)")
        private Integer shouhouId;
        /**
         * 检测门店(门店id)
         */
        @ApiModelProperty(value = "检测门店(门店id)")
        private Integer areaId;
        /**
         * 检测门店(门店code)
         */
        @ApiModelProperty(value = "检测门店(门店code)")
        private String area;
        /**
         * 送修人姓名
         */
        @ApiModelProperty(value = "送修人姓名")
        private String afterUserName;
        /**
         * 送修人电话
         */
        @ApiModelProperty(value = "送修人电话")
        private String afterUserMobile;
        /**
         * 检测人员姓名
         */
        @ApiModelProperty(value = "检测人员姓名")
        private String checkUser;

        /**
         * 检测人员姓名
         */
        @ApiModelProperty(value = "检测人员姓名")
        private String shouhouUrl;

        /**
         * 检测结果
         * @see ShouhouServiceReport.FaultResultEnum
         */
        @ApiModelProperty(value = "检测结果")
        private Integer faultResult;

        /**
         * 检测结果
         * @see ShouhouServiceReport.FaultResultEnum
         */
        @ApiModelProperty(value = "检测结果文字")
        private String faultResultName;
        /**
         * 温馨提示
         */
        @ApiModelProperty(value = "小文件服务器的fid")
        private String fid;
    }

    @Data
    @Accessors(chain = true)
    public static class ServiceReportEnum implements Serializable {
        private static final long serialVersionUID = 8095017006846534361L;
        private String label;
        private Object value;
    }
}
