package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
@TableName("product")
public class ProductEntity extends Model<Productinfo> {
    @TableId
    private Integer id;

    private Integer cid;

    @TableField("cidFamily")
    private String cidFamily;

    private String name;

    private Boolean display;

    @TableField("viewsWeek")
    private Integer viewsWeek;

    private Boolean ismobile;

    @TableField("brandID")
    private Integer brandId;

    @TableField("supportService")
    private Integer supportService;

    @TableField(value = "otherLimit", fill = FieldFill.DEFAULT)
    private Integer otherLimit;

    @TableField(value = "numberPanDian", fill = FieldFill.DEFAULT)
    private Boolean numberPanDian;

    private Integer vendor;

    @TableField(value = "isuse", fill = FieldFill.DEFAULT)
    private Boolean isUse;

    @TableField(value = "adddate", fill = FieldFill.INSERT)
    private Date addDate;

    @TableField(value = "isVirtualGoods", fill = FieldFill.DEFAULT)
    private Boolean isVirtualGoods;

    private Integer mark;
}
