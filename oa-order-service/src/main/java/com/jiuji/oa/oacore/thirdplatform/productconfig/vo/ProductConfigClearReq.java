package com.jiuji.oa.oacore.thirdplatform.productconfig.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * 商品关系配置展现层
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@ApiModel(value = "清空商户商品配置请求清空商户商品配置请求")
public class ProductConfigClearReq  {
    /**
     * 清除类型
     */
    private Integer clearType;

    /**
     * ppid
     */
    private String skuid;

    /**
     * 商户编码
     */
    private String tenantCode;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    private String platCode;

}
