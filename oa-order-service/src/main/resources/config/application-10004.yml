consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://************:5083
  select:
    url: https://img.iteng.com/
  upload:
    url: http://************:9333
instance-zone: 10004
jiuji:
  sys:
    moa: https://moa.iteng.com
    pc: https://oa.iteng.com
    web: https://www.iteng.com
    xtenant: 10004
  xtenant: 4000
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10004:KhedYeBZcURacY2N@************:27017,************:27017,************:27017/ch999oa__10004
  url1: mongodb://ch999oa__10004:<PERSON><PERSON>YeBZcURacY2N@************:27017,************:27017,************:27017/ch999oa__10004
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10004
    password: NgPj%EQkjXb$*17T
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10004
  oa_nc:
    dbname: oa_nc__10004
    password: sfHc6MgmQ7!p^3Mm
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10004
office:
  sys:
    xtenant: 10004
rabbitmq:
  master:
    password: VjTWb
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10004
    vhost: oaAsync__10004
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: kjxNx
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10004
    vhost: oa__10004
  oaAsync:
    password: VjTWb
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10004
    vhost: oaAsync__10004
  printer:
    password: yQGMw
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10004
    vhost: printer__10004
redis:
  oa:
    host: ************
    password: google99
    port: 6394
    url: google99@************:6394
sms:
  send:
    email:
      url: http://sms.iteng.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.iteng.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10004
sqlserver:
  after_write:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'Z*T4Zt5xb2Y7SF'
    port: 1433
    username: ch999oanew__10004
  ch999oanew:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'Z*T4Zt5xb2Y7SF'
    port: 1433
    username: ch999oanew__10004
  ch999oanewHis:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'Z*T4Zt5xb2Y7SF'
    port: 1433
    username: ch999oanew__10004
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'aPh60HDweCV*ZB'
    port: 1433
    username: office__10004
  oanewWrite:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'Z*T4Zt5xb2Y7SF'
    port: 1433
    username: ch999oanew__10004
  office:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'aPh60HDweCV*ZB'
    port: 1433
    username: office__10004
  officeWrite:
    dbname: office__10004
    host: shoa.sqlserver.serv.iteng.com
    password: 'aPh60HDweCV*ZB'
    port: 1433
    username: office__10004
  smallpro_write:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  web999:
    dbname: web999__10004
    host: shoa.sqlserver.serv.iteng.com
    password: sCf_CPl53hwKpJ
    port: 1433
    username: web999__10004
  ch999oanewReport:
    dbname: ch999oanew__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "Z*T4Zt5xb2Y7SF"
    port: 1433
    username: ch999oanew__10004
  ershou:
    dbname: ershou__10004
    host: shoa.sqlserver.serv.iteng.com
    password: "wHGw19x92apDug"
    port: 1433
    username: ershou__10004
url:
  delImgUrl: http://************:5083
  oa-push-info: http://inwcf.iteng.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.iteng.com/
  source:
      path: i18n/url
  uploadImgUrl: http://************:9333
autoCalculateSalary:
  xtenant: 10004
