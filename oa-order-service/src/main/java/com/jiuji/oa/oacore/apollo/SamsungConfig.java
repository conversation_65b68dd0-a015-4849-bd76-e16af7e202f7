package com.jiuji.oa.oacore.apollo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/9 9:50
 */
@Data
@Slf4j
public class SamsungConfig {
    @Value("${samsung.instalment.url:https://qagcdm.samsung.com.cn/sap/bc/srt/rfc/sap/zws1105_receive_alipay_paymnt/200/zws1105_receive_alipay_paymnt/zws_zcdb1105_hcc}")
    private String samsungInstalmentUrl;

    @Value("${samsung.instalment.user:CPIC_SYS1858}")
    private String samsungInstalmentUser;

    @Value("${samsung.instalment.pwd:CPIC_sys1858}")
    private String samsungInstalmentPwd;

    @Value("${samsung.instalment.seed.key:GCRMCDBYYYYMMDD_0000001}")
    private String samsungInstalmentSeedKey;

    @Value("${samsung.instalment.filter.code:E,330301,330010,330004,330001,330002,330009,330003,330302}")
    private List<String> filterMsgCodeList;
}
