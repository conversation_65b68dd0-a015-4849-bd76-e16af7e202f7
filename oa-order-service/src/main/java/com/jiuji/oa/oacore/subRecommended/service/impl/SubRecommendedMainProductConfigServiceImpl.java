package com.jiuji.oa.oacore.subRecommended.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedMainProductConfig;
import com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedMainProductConfigMapper;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedMainProductConfigService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

@Service
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class SubRecommendedMainProductConfigServiceImpl extends ServiceImpl<SubRecommendedMainProductConfigMapper, SubRecommendedMainProductConfig> implements SubRecommendedMainProductConfigService {
}
