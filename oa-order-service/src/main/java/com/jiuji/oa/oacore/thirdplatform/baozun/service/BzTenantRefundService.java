package com.jiuji.oa.oacore.thirdplatform.baozun.service;

import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzRefundBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantSalesDetailBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesDetailSninfo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 宝尊退货退款处理
 * <AUTHOR>
 * @since 2021/10/8 13:49
 */
public interface BzTenantRefundService {
    /**
     * 扫描退款订单信息 生成售后单并自动退款
     * @return 处理的本地订单id
     */
    R<String> scannerRefund();

    /**
     * 扫描退款订单信息 生成售后单并自动退款
     * @return 处理的本地订单id
     */
    R<String> scannerRefundForXXL();

    /**
     * 小件批量转现
     * @param bzRefund
     * @param transactionNumber
     * @param smallSalesDetails
     */
    void asyncBatchIntoStock(BzRefundBO bzRefund, String transactionNumber, List<BzTenantSalesDetailBO> smallSalesDetails);

    /**
     * 小件调用http请求进行退款
     * @param bzRefund
     * @param transactionNumber
     * @param successCallback
     */
    void smallHttpTuikuan(BzRefundBO bzRefund, String transactionNumber, Runnable successCallback);

    @Transactional(rollbackFor = Exception.class)
    void saveThirdOrigin(Integer tuihuanId, String userName, List<ThirdOriginRefundVo> myTuiWayDetails);



    @Transactional(rollbackFor = Exception.class)
    void saveCardPayOriginWay(Integer tuihuanId, String userName, List<CardOriginRefundVo> myTuiWayDetails);

    /**
     * 大件取机接口
     * @param shouhouId
     * @param bzRefund
     * @param salesDetail
     * @param sn
     * @param successCallback
     */
    void mobileQuji(Integer shouhouId, BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn, Runnable successCallback);

    /**
     * 大件售后转出
     * @param bzRefund
     * @param salesDetail
     * @param sn
     */
    void asyncMobileXianhuoHttp(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn);

    List<BzRefundBO> listBzRefundOrderByAreaId(List<String> transactionNumbers);

    List<BzRefundBO> listBzRefundOrderByAreaIdForXXL(List<String> transactionNumbers);

    /**
     * 获取调用c#的秘钥
     * @return
     */
    String getShouHouThjSecret();

    /**
     * 模拟用户信息
     */
    String simulateUser(AreaInfo areaInfo, BzRefundBO bzRefund);
}
