package com.jiuji.oa.oacore.caigou.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/10/11 16:02
 */
@NoArgsConstructor
@Data
public class DiaoboAwaitCountVo {

    /**
     * 总数
     */
    @ApiModelProperty("总数")
    private Integer total;
    /**
     * 较早时间
     */
    @ApiModelProperty("较早时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime earlyTime;
}
