package com.jiuji.oa.oacore.oaorder.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jiuji.oa.oacore.oaorder.po.Tousu;
import com.jiuji.oa.oacore.oaorder.dao.TousuMapper;
import com.jiuji.oa.oacore.oaorder.service.TousuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投诉 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-14
 */
@Service
@DS("office")
public class TousuServiceImpl extends ServiceImpl<TousuMapper, Tousu> implements TousuService {

    @Override
    public Tousu getTousuById(Integer id) {
        return getById(id);
    }

    @Override
    public Tousu getSomeFieldsTousuById(Integer id) {
        return getOne(Wrappers.<Tousu>lambdaQuery().select(Tousu::getId, Tousu::getDealUser).eq(Tousu::getId, id));
    }
}
