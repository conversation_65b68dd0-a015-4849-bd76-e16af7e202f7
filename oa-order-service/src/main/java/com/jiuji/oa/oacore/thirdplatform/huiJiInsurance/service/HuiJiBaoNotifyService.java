package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.entity.HuiJiBaoNotify;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.UpdateOrderInfoReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res.HuijbRecoverDto;


public interface HuiJiBaoNotifyService extends IService<HuiJiBaoNotify> {

    /**
     * @Description 汇机保服务通知处理
     * <AUTHOR>
     * @Date   2023/3/23 11:42
     */
    void huijbNotifyHandle(HuijbRecoverDto huijbRecoverDto);


    /**
     * 汇机保修改订单信息
     * @param req
     * @return
     */
    Result<Boolean> updateOrder(UpdateOrderInfoReq req);
}
