package com.jiuji.oa.oacore.partner.feedback.vo.req;

import com.jiuji.oa.oacore.common.group.Save;
import com.jiuji.oa.oacore.tousu.po.Attachments;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class FeedbackReq implements Serializable {

    private static final long serialVersionUID = 8927975463818504440L;

    @ApiModelProperty(value = "反馈人id")
    @NotNull(groups = Save.class, message = "反馈人id不能为空")
    private Integer userId;

    @ApiModelProperty(value = "反馈人电话")
    @NotBlank(groups = Save.class,message = "反馈人联系方式不能为空")
    private String mobile;

    @ApiModelProperty(value = "反馈内容")
    @NotBlank(groups = Save.class,message = "内容不能为空")
    private String content;

    @ApiModelProperty(value = "反馈人姓名")
    private String memberName;

    @ApiModelProperty(value = "附件ids")
    private String attachIds;

    @ApiModelProperty(value = "附件信息")
    List<Attachments> files;

    @ApiModelProperty(value = "反馈性质：1投诉，2表扬,3建议")
    @NotNull(groups = Save.class,message = "反馈性质不能为空")
    private Integer tag;

    @ApiModelProperty(value = "租户ID")
    @NotNull(groups = Save.class, message = "租户ID不能为空")
    private Integer xtenant;

    @ApiModelProperty(value = "租户名称")
    @NotBlank(groups = Save.class,message = "租户名称不能为空")
    private String xtenantName;

    @ApiModelProperty(value = "数据来源（null = 九机，1=九讯云，2=九讯云Neo）,这里不能为null")
    @NotNull(groups = Save.class,message = "数据来源不能为空")
    private Integer source;

    /**
     * 隐藏字段，用于控制内部推送测试
     */
    @ApiModelProperty(hidden = true)
    private Boolean notifyTest = false;
}
