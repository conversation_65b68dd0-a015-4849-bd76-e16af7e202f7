<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantRefundMapper">

    <sql id="cndSaleDetail">
        btsd.transaction_type in ('R','E','D') and isnull(btsd.is_del,0)=0 and (btsd.kc_deal_result is null or btsd.kc_deal_time is null or (kc_deal_result=0 and DATEDIFF(mi,kc_deal_time,GETDATE())&lt;10))
    </sql>
    <sql id="cndSaleDetailForXXL">
        kc_deal_result=0 and DATEDIFF(mi,kc_deal_time,GETDATE())&lt;10
    </sql>

    <insert id="insertShouhou" useGeneratedKeys="true" keyProperty="sn.shouhouId" keyColumn="id">
        INSERT INTO shouhou ( name, peizhi, problem, username, mobile, stats, baoxiu, inuser, imei, xianshi, tradedate, modidate
        , feiyong, costprice, userid, kinds, isticheng, issoft, product_color, ppriceid, isquick, wcount, weixiuzuid, weixiu_startdtime
        , orderid, isquji, isfan, sub_id, basket_id, ishuishou, sxuserid, lockpwd, wxkind, areaid, isBakData, isjbanwxqq, truename
        , ServiceCostprice, question_type ) VALUES (#{salesDetail.productInfo.productName}, '无', '宝尊订单退款处理,【BJ】单号'+#{bzRefund.salesOrder.transactionNumber}
        , #{bzRefund.member.userName}, #{bzRefund.member.mobile}, 0, 1, '系统', #{sn.sn}, 1,#{bzRefund.sub.tradeDate}, GETDATE(), 0, 0, #{bzRefund.member.id}
        , 'bd', 0, 0, #{salesDetail.productInfo.productColor},#{salesDetail.ppid}, 0, 0
        <!--维修组id 4 综合组-->
        , 4, GETDATE(), #{orderId}, 0, 0, #{bzRefund.sub.subId},#{salesDetail.basket.basketId}, 0, #{bzRefund.member.userSex}, '无', 2,#{bzRefund.areaInfo.id}, 0
        <!--维修处理方式 2 退换-->
        , 0,#{bzRefund.member.realName}, 0.0, '2' )
    </insert>
    <insert id="insertReturnCb">
        INSERT INTO shouhou_returnCb ( oldshouhou_id, shouhou_id, tuihuan_id, tuihuanKind, ppid, dtime, shzrPrice,mkc_id)
        VALUES (#{sn.shouhouId}, #{sn.shouhouId}, 0, 5, #{salesDetail.ppid}, GETDATE(), #{salesDetail.productInfo.memberprice},#{mkc.id});
    </insert>
    <insert id="insertShouhouTimePoint">
        INSERT INTO dbo.ShouhouTimePoint (shouhouid, [type], dtime, timelength, state, isdelete)
        <!-- type 1 接件-->
        VALUES(#{shouhouId}, 1, GETDATE(), 0, 1, 0);
    </insert>
    <insert id="insertShouhouTuihuan" useGeneratedKeys="true" keyProperty="sn.shouhouTuihuanId" keyColumn="id">
        INSERT INTO dbo.shouhou_tuihuan
        (shouhou_id, tuihuan_kind, basket_id, tuikuanM, tuikuanM1, sub_id, tui_way, bankname, bankfuming, banknumber, comment
        , dtime, inuser, check1, check2, check1dtime, check2dtime, check1user, check2user, area, isdel, sub_idM, zhejiaM, check3
        , check3dtime, check3user, basket_ids, tuikuanlock, buypriceM, inprice, pzid, salenm, puhuim, piaoInfo, ctype, areaid
        , coinM, IncludeChecklist, IsSendWeixinInviteCode, smallproid, iszengpin, gzinfo, payOpenId, paymobilecode, ispayMoney
        , baitiaoM, kuBaiTiaoM, isValidt, peizhi, peizhiPrice, piaoPrice, piaoType, faultType, checkType, tradeType, tradeDate
        , fpOpenid, fpPayState, fpPayTime, delUser, delTime, netExceptionFlag, otherType, accountException, tui_kinds)
        <!-- tuihuan_kind 3 退款-->
        VALUES(#{sn.shouhouId}, 3, #{salesDetail.basket.basketId}, #{salesDetail.basket.price2}, #{salesDetail.basket.price2}, #{salesDetail.basket.subId}
               <choose>
                    <when test="(sn.thirdOriginRefundVos == null || sn.thirdOriginRefundVos.isEmpty())">,#{bzRefund.app.refundType}</when>
                    <otherwise>,null</otherwise>
               </choose>
                ,NULL, NULL, NULL
                , '宝尊订单退款处理', GETDATE(), '系统', 1,1, GETDATE(), GETDATE(), '系统', '系统', NULL, 0, NULL, 0.0000, NULL, NULL, NULL, NULL, NULL
                , #{salesDetail.basket.price}, #{productMkc.inbeihuoprice}, NULL, NULL, NULL, NULL, 0, #{bzRefund.areaInfo.id}, 0.00, 0, NULL, NULL, NULL, NULL
                , NULL, NULL, NULL, NULL, 0.00, NULL, #{salesDetail.productInfo.config}, 0.00, 0.00, 0, '无故障', '特殊退换', 1, #{bzRefund.sub.tradeDate}
               , NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
                <choose>
                    <when test="(sn.thirdOriginRefundVos == null || sn.thirdOriginRefundVos.isEmpty())">,0</when>
                    <otherwise>,1</otherwise>
                </choose>
               );
    </insert>
    <insert id="insertSmallPro" useGeneratedKeys="true" keyColumn="id" keyProperty="bzRefund.smallProId">
        INSERT INTO Smallpro ( Name, Area, userid, sub_id, Buydate, IsBaoxiu, Groupid, Inuser, Indate, Kind, Stats, Username, Mobile, Problem, Comment
        , areaid, feiyong, dataRelease,codeMsg)
        <!-- kind 3 退 dataRelease 3 数据无需解绑-->
        VALUES (#{name},#{bzRefund.areaInfo.area},#{bzRefund.member.id},#{bzRefund.sub.subId},#{bzRefund.sub.tradeDate}, 1, 1, '系统', GETDATE(), 3,
                0, #{bzRefund.member.realName}, #{bzRefund.member.mobile}, '宝尊订单退款,【BJ】单号'+#{bzRefund.salesOrder.transactionNumber}, '宝尊订单退款'
                , #{bzRefund.areaInfo.id}, 0, 3,'授权验证 【系统】 '+CONVERT(varchar(100), GETDATE(), 120));
    </insert>
    <insert id="batchInsertSmallProBill">
        INSERT INTO SmallproBill ( smallproID, basket_id, ppriceid, count, inprice ) VALUES
            <foreach collection="smallSalesDetails" item="salesDetail" separator=",">
                (#{smallProId}, #{salesDetail.basket.basketId}, #{salesDetail.ppid}, #{salesDetail.quantity},#{salesDetail.basket.inprice} )
            </foreach>
    </insert>
    <insert id="insertSmallProTuihuan" useGeneratedKeys="true" keyColumn="id" keyProperty="bzRefund.smallProTuihuanId">
        INSERT INTO dbo.shouhou_tuihuan
        (shouhou_id, tuihuan_kind, basket_id, tuikuanM, tuikuanM1, sub_id, tui_way, bankname, bankfuming, banknumber, comment
        , dtime, inuser, check1, check2, check1dtime, check2dtime, check1user, check2user, area, isdel, sub_idM, zhejiaM, check3
        , check3dtime, check3user, basket_ids, tuikuanlock, buypriceM, inprice, pzid, salenm, puhuim, piaoInfo, ctype, areaid
        , coinM, IncludeChecklist, IsSendWeixinInviteCode, smallproid, iszengpin, gzinfo, payOpenId, paymobilecode, ispayMoney
        , baitiaoM, kuBaiTiaoM, isValidt, peizhi, peizhiPrice, piaoPrice, piaoType, faultType, checkType, tradeType, tradeDate
        , fpOpenid, fpPayState, fpPayTime, delUser, delTime, netExceptionFlag, otherType, accountException, tui_kinds)
        <!-- tuihuan_kind 3 退款-->
        VALUES(#{bzRefund.smallProId}, 7, NULL, #{tuikuanM}, #{tuikuanM}, #{bzRefund.sub.subId}
        <choose>
            <when test="(thirdOriginRefundVos == null || thirdOriginRefundVos.isEmpty())">,#{bzRefund.app.refundType}</when>
            <otherwise>,null</otherwise>
        </choose>
        , NULL, NULL, NULL
        , '宝尊订单退款', GETDATE(), '系统', 1,1, GETDATE(), GETDATE(), '系统', '系统', NULL, 0, NULL, 0.0000, NULL, NULL, NULL, NULL, NULL
        , #{tuikuanM}, #{inPrice}, NULL, NULL, NULL, NULL, 0, #{bzRefund.areaInfo.id}, 0.00, 0, NULL,#{bzRefund.smallProId}, NULL, NULL
        , NULL, NULL, NULL, NULL, 0.00, NULL, null, 0.00, 0.00, 0, '无故障', '特殊退换', 1, #{bzRefund.sub.tradeDate}
        , NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
        <choose>
            <when test="(thirdOriginRefundVos == null || thirdOriginRefundVos.isEmpty())">,0</when>
            <otherwise>,1</otherwise>
        </choose>
        );
    </insert>
    <insert id="batchInsertSmallProTuihuanBill">
        INSERT INTO ReturnsDetail ( shthid, BasketID, BasketCount ) VALUES
        <foreach collection="smallSalesDetails" item="salesDetail" separator=",">
            (#{smallProTuihuanId}, #{salesDetail.basket.basketId},#{salesDetail.quantity})
        </foreach>
    </insert>
    <insert id="insertSecret">
        INSERT INTO secretCodeConfig(secret, version, code)VALUES(#{secret}, '1.0', #{code});
    </insert>
    <insert id="batchInsertThirdOrigin">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,third_refund_type,refund_price,tui_way,tui_group,pay_kinds,record_id,create_time,create_user,update_time,is_del)
        values
        <foreach collection="myTuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanId},#{tuiWayDetail.refundType},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},
            3,#{tuiWayDetail.otherRecordId},getdate(),#{userName},getdate(),isnull(#{tuiWayDetail.isDel},0))
        </foreach>
    </insert>
    <insert id="batchInsertCardPayOriginWay">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,refund_price,pay_kinds,record_id,tui_way,tui_group,kemu_tui,create_time,create_user,update_time,is_del,third_refund_type)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanId},#{tuiWayDetail.refundPrice},2,#{tuiWayDetail.posId},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},null,
            getdate(),#{userName},getdate(),isnull(#{tuiWayDetail.isDel},0),
            <!--isDel true 其他方式退款的记录 2-->
            <choose>
                <when test="tuiWayDetail.isDel">2</when>
                <otherwise>null</otherwise>
            </choose>
            )
        </foreach>

    </insert>
    <update id="appendOrderMsg">
        update baozun_tenant_sales_order set message=RIGHT(REPLACE(isnull(message,''),#{message},'')+' '+#{message},500)
        where transaction_number = #{transactionNumber}
    </update>
    <update id="updateSnShouhouId">
        UPDATE dbo.baozun_tenant_sales_detail_sninfo
        SET shouhou_id = #{shouhouId} WHERE id=#{id}

    </update>
    <update id="updateOrderDetailDealResult">
        UPDATE dbo.baozun_tenant_sales_detail
        SET
            <if test="dealResult">
                refund_time= GETDATE(),
            </if>
            kc_deal_result=#{dealResult}, kc_deal_time=GETDATE()
        WHERE id=#{id} and (kc_deal_result is null or #{dealResult} &lt;&gt;0)

    </update>
    <update id="updateShouhouQujiData">
        update dbo.shouhou set stats = 1,reweixiuren = 0,weixiuren = '系统',weixiurentime = GETDATE() where id = #{shouhouId}
    </update>
    <update id="batchUpdateThirdOriginShouyingOther">
        update dbo.shouyin_other
        <set>
            <trim prefix="refund_price=case" suffix=" end,">
                <foreach collection="myTuiWayDetails" item="tuiWayDetail">
                    when  id = #{tuiWayDetail.otherRecordId} then
                    (case when isnull(refund_price,0)+#{tuiWayDetail.refundPrice} &lt; 0 then 0.0 else isnull(refund_price,0)+#{tuiWayDetail.refundPrice} end)
                </foreach>
            </trim>
        </set>
        <where>
            id in
            <foreach collection="myTuiWayDetails" open="(" separator="," close=")" item="tuiWayDetail">
                #{tuiWayDetail.otherRecordId}
            </foreach>
        </where>
    </update>
    <select id="queryRefundOrderDetail"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantSalesDetailBO">
        SELECT id, combo_code, line_number, fk_transaction_number, transaction_type, upc, ppid, unit_price, actual_price, quantity, discount
        , line_total, create_time, create_user, update_time, is_del, refund_time, kc_deal_result, kc_deal_time, btsd.order_label,btsd.basket_id basketId
        FROM dbo.baozun_tenant_sales_detail btsd with(nolock)
        where fk_transaction_number in
            <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
                #{orderId}
            </foreach>
        <if test="isNotTest">
            and <include refid="cndSaleDetail"></include>
        </if>
    </select>

    <select id="queryRefundOrderDetailForXXL"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantSalesDetailBO">
        SELECT id, combo_code, line_number, fk_transaction_number, transaction_type, upc, ppid, unit_price, actual_price, quantity, discount
        , line_total, create_time, create_user, update_time, is_del, refund_time, kc_deal_result, kc_deal_time
        FROM dbo.baozun_tenant_sales_detail btsd with(nolock)
        where fk_transaction_number in
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
        <if test="isNotTest">
            and <include refid="cndSaleDetailForXXL"></include>
        </if>
    </select>
    <select id="queryRefundOrder" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesOrder">
        SELECT top ${topNum} transaction_number, sub_id, fk_tenant_id, order_date, transaction_date, Transaction_time, store_code, area_id, slip_code, platform_source
             , user_id, customer_id, email, name, telephone, mobile, transfer_fee, remark, create_time, create_user, update_time, is_del
        FROM dbo.baozun_tenant_sales_order btso with(nolock)
                where isnull(btso.is_del,0)=0 and btso.sub_id is not null
                         and exists(select 1 from dbo.sub s with(nolock) where s.sub_id = btso.sub_id and s.sub_check = 3)
                         and exists(select 1 from dbo.baozun_tenant_sales_detail btsd with(nolock)
                            where btsd.fk_transaction_number = btso.transaction_number
                              <choose>
                                  <when test="transactionNumbers != null and !transactionNumbers.isEmpty()">
                                      and btso.transaction_number in
                                      <foreach collection="transactionNumbers" open="(" close=")" separator="," item="transactionNumber">
                                          #{transactionNumber}
                                      </foreach>
                                  </when>
                                  <otherwise>and <include refid="cndSaleDetail"></include></otherwise>
                              </choose>
                        )
        order by  btso.area_id asc,btso.transaction_number desc
    </select>
    <select id="queryRefundSninfo"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesDetailSninfo">
        SELECT id, fk_transaction_number, slip_code, line_number, upc, sn, create_time, create_user, update_time,shouhou_id, is_del,inv_status
        FROM dbo.baozun_tenant_sales_detail_sninfo btsds with(nolock)
        where isnull(is_del,0)=0 and fk_transaction_number in
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
        <if test="isNotTest">
            and exists(select 1 from dbo.baozun_tenant_sales_detail btsd with(nolock)
            where btsd.fk_transaction_number = btsds.fk_transaction_number and btsd.upc = btsds.upc
            and <include refid="cndSaleDetail"></include>)
            and btsds.shouhou_id is null
        </if>
    </select>
    <select id="querySalesTenders"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesTender">
        SELECT id, fk_transaction_number, payment_type, transaction_amount, create_time, create_user, update_time, is_del
        FROM dbo.baozun_tenant_sales_tender btst with(nolock)
        where isnull(is_del,0)=0 and fk_transaction_number in
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
    </select>
    <select id="queryBaskets" resultType="com.jiuji.oa.oacore.oaorder.po.Basket">
        SELECT basket_id, basket_count, basket_date, product_peizhi, seller, ismobile, price, sub_id, price1, ppriceid, inprice
             , giftid, [type], isdel, ischu, price2, iskc, isOnShop,return_price, youhuiPrice, jifenPrice, giftPrice
        FROM dbo.basket b with(nolock)
        where isnull(b.isdel,0)=0 and
              <foreach collection="bzRefunds" open="(" close=")" separator="or" item="bzRefund">
                    b.sub_id = #{bzRefund.salesOrder.subId} and b.ppriceid in
                    <foreach collection="bzRefund.salesDetails" item="salesDetail" open="(" close=")" separator=",">
                        #{salesDetail.ppid}
                    </foreach>
              </foreach>

    </select>
    <select id="listSub" resultType="com.jiuji.oa.oacore.oaorder.po.Sub">
        SELECT sub_id, sub_date, sub_check, sub_to, sub_tel, sub_pay, comment, Inuser, sub_mobile, printxcount, area, zitidianID
             , userid, online_pay, Marketingid, ispiao, subtype, delivery, yingfuM, yifuM, feeM, youhui1M, shouxuM, jidianM, tradeDate
             , tradeDate1, dingjing, subPID, trader, fankuan, islock, areaid, coinM, expectTime, returnDate, kcAreaid, pzid, voucherId,
               subApartDate
        FROM dbo.sub s with(nolock)
        where s.sub_check = 3 and s.sub_id in
        <foreach collection="subIds" open="(" close=")" separator="," item="subId">
            #{subId}
        </foreach>
    </select>
    <select id="listProductInfoByPpid" resultType="com.jiuji.oa.oacore.oaorder.po.Productinfo">
        SELECT productid, product_id, ppriceid, product_name, product_color, costprice, vipPrice, cid, ismobile1, bpic, memberprice
             , pricefd, que, display, isdel, viewsWeek, ppriceid1, config, brandID, cidFamily, viewsweekr, rank1, noPromotion, pLabel
             , OEMPrice, barCode, vip2price, isbarCode, Scarcity, isSn, supportService, otherLimit, saleStartTime, sale_channel
             , vendor, isuse, barCodeCount, mark
        FROM dbo.productinfo p
        where p.ppriceid in
        <foreach collection="ppids" item="ppid" separator="," open="(" close=")">
            #{ppid}
        </foreach>

    </select>
    <select id="listProductMkc" resultType="com.jiuji.oa.oacore.oaorder.po.ProductMkc">
        SELECT id, ppriceid, imei, inuser, kc_check, area, basket_id, inprice, origarea,inbeihuoprice
        FROM dbo.product_mkc pm with(nolock)
        where
        <foreach collection="salesDetails" open="(" close=")" separator="or" item="salesDetail">
            pm.basket_id = #{salesDetail.basket.basketId} and pm.imei in
            <foreach collection="salesDetail.salesDetailSninfos" open="(" close=")" separator="," item="info">
                #{info.sn}
            </foreach>
        </foreach>

    </select>
    <select id="getShouhouId" resultType="java.lang.Integer">
        select id from shouhou s with(nolock)
        where s.xianshi = 1 and s.imei = #{sn} and (s.sub_id = #{subId}
            or exists(select 1 from sub with(nolock) where sub.sub_id = s.sub_id and sub.subPID = #{subId}))
    </select>
    <select id="listApp" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantApp">
        SELECT id, fk_tenant_id, child_merchant_name, user_id, platform_kemu, customer_kemu, goods_arrival_kemu, create_time
             , create_user, update_time, is_del, refund_type
        FROM dbo.baozun_tenant_app app with(nolock)
        where isnull(app.is_del,0) = 0 and
            <foreach collection="salesOrders" item="salesOrder" open="(" close=")" separator="or">
                app.fk_tenant_id = #{salesOrder.fkTenantId} and app.child_merchant_name = #{salesOrder.platformSource}
            </foreach>

    </select>
    <select id="getSmallPro" resultType="com.jiuji.oa.oacore.oaorder.po.Smallpro">
        select top 1 * from Smallpro with(nolock) where Stats&lt;&gt;2 and sub_id = #{bzRefund.sub.subId} order by id desc
    </select>
    <select id="getSecretByCode" resultType="java.lang.String">
        select secret from secretCodeConfig t with(nolock) where t.code=#{code}
    </select>
    <select id="getXianHuoByFromId" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.AfterXianhuoBo">
        select s.id shouhouId,pm.id mkcId,pm.inbeihuoprice inBeihuoPrice from shouhou s with(nolock)
        left join product_mkc pm with(nolock) on pm.id = s.mkc_id
        where s.xianshi = 1 and s.fromshouhouid = #{shouhouId}
    </select>
    <select id="getMkdIdByMkcId" resultType="java.lang.Integer">
        select top 1 md.id from mkc_dellogs md with(nolock)
        <where>
            <if test="id != null">
                and md.id = #{id}
            </if>
            and md.mkc_id = #{mkcId}
            <choose>
                <when test="isCheck1"> and md.check1 is not null</when>
                <otherwise> and md.check1 is null</otherwise>
            </choose>
            <choose>
                <when test="isCheck2"> and md.check2 is not null</when>
                <otherwise> and md.check2 is null</otherwise>
            </choose>
        </where>
        order by md.id desc
    </select>


</mapper>
