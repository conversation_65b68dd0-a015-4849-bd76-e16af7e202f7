package com.jiuji.oa.afterservice.sub.enums;

import com.jiuji.oa.afterservice.sub.constants.ColorConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <审核状态枚举>
 *
 * <AUTHOR>
 * @date 2021-6-30
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum CheckStatusEnum implements CodeMessageEnumInterface {
    /**
     * 审核状态枚举
     */
    TO_BE_CHECKED(1, "待审核", ColorConstant.GREEN_COLOR, ColorConstant.GREEN_COLOR, "待审核"),
    PASS(2, "通过", ColorConstant.GREEN_COLOR, "#0BBE69", "已通过"),
    REJECT(3, "否决", "#FA6400", "#F15643", "未通过");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    private String buttonColor;

    private String textColor;

    private String topLabel;


    public static CheckStatusEnum valueOfByCode(Integer code) {
        for (CheckStatusEnum enumConstant : CheckStatusEnum.class.getEnumConstants()) {
            if (Objects.equals(enumConstant.getCode(), code)) {
                return enumConstant;
            }
        }
        return null;
    }
}
