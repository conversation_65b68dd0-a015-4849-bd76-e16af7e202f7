package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.entity.TripartiteInteractive;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.mapper.TripartiteInteractiveMapper;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service.TripartiteInteractiveService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

@Service
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class TripartiteInteractiveServiceImpl extends ServiceImpl<TripartiteInteractiveMapper,TripartiteInteractive> implements TripartiteInteractiveService {


    @Override
    public Boolean saveOrUpdateEntity(TripartiteInteractive tripartiteInteractive) {
       return this.saveOrUpdate(tripartiteInteractive);
    }
}
