package com.jiuji.oa.oacore.oaorder.service.impl.orderbaseinfo;

import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.oaorder.dao.BbsxpUsersMapper;
import com.jiuji.oa.oacore.oaorder.dao.EvaluateMapper;
import com.jiuji.oa.oacore.oaorder.po.Evaluate;
import com.jiuji.oa.oacore.oaorder.res.OrderBaseInfoRes;
import com.jiuji.oa.oacore.oaorder.res.Stats;
import com.jiuji.oa.oacore.oaorder.res.UserBO;
import com.jiuji.oa.oacore.oaorder.service.OrderBaseInfo;
import com.jiuji.oa.oacore.oaorder.service.OrderBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 售后单
 */
@Component("S")
public class ShouhouOrder implements OrderBaseInfo {
    @Autowired
    private EvaluateMapper evaluateMapper;
    @Autowired
    private BbsxpUsersMapper bbsxpUsersMapper;
    @Autowired
    private OrderBaseInfoService orderBaseInfoService;

    @Override
    public OrderBaseInfoRes getOrderBaseInfo(Integer id) {
        Integer shouhou = EvaluateTypeEnum.Shouhou.getCode();
        Evaluate evaluate = evaluateMapper.getEvaluateBySubIdAndType(id, shouhou);
        // 获取售后单会员信息
        UserBO userBO = bbsxpUsersMapper.getShouhouUser(id);
        OrderBaseInfoRes orderBaseInfoRes = orderBaseInfoService.buildOrderBaseInfoRes(evaluate, userBO, shouhou, false);

        boolean isCanEvaluate = true; // 默认可评价
        if (orderBaseInfoRes.getStats().getStats() != 2) {
            // 内部员工不可评价
            isCanEvaluate = bbsxpUsersMapper.shouhouIsCanEvaluate(id) == 0;
        }

        //超过15天 或者 内部员工不能评价的
        if (orderBaseInfoRes.getStats().getStats() != 2 && !isCanEvaluate) {
            orderBaseInfoRes.setStats(new Stats(2, null));
        }

        return orderBaseInfoRes;
    }
}
