package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jiuji.tc.utils.jackson.LocalDateTimeDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * description: <小件进程日志res>
 * translation: <Smallpro process log res>
 *
 * <AUTHOR>
 * @date 2019/12/5
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproLogRes implements Serializable {
    private static final long serialVersionUID = -5339761061975747885L;
    /**
     * 操作信息
     */
    @ApiModelProperty(value = "操作信息")
    private String information;
    /**
     * 用户是否可见标志位，[1可见，0不可见]
     */
    @ApiModelProperty(value = "用户是否可见标志位，[1可见，0不可见]")
    private Integer showFlag;

    /**
     * 备注内容
     */
    private String comment;

    /**
     * 接件人
     */
    @Field("inuser")
    private String inUser;

    /**
     * 用户是否手动添加的日志，[1手动添加】
     */
    @ApiModelProperty(value = "用户是否手动添加的日志，[1手动添加】")
    private Integer commentType;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime inDate;
}
