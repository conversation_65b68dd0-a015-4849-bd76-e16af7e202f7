package com.jiuji.oa.oacore.thirdplatform.tuangou.service;

import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;

/**
 * 抖音团购核销
 * <AUTHOR>
 */
public interface DouyinTuangouService extends TuangouCertificateService{
    /**
     * 验券准备
     * @param req
     * @return
     */
    @Override
    String certificatePrepare (CertificatePrepareReq req);

    /**
     * 验券
     * @param req
     * @return
     */
    @Override
    String certificateVerify (CertificateVerifyReq req);

    /**
     * 撤销核销
     * @param req
     * @return
     */
    @Override
    String certificateCancel (CertificateCancelReq req);

    /**
     * 券状态查询
     * @param req
     * @return
     */
    @Override
    String certificateGet (CertificateGetReq req);

    /**
     * 券状态批量查询
     * @param req
     * @return
     */
    @Override
    String certificateQuery (CertificateQueryReq req);
}
