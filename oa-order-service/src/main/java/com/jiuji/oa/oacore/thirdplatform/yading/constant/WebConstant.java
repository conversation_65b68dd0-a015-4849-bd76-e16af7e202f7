package com.jiuji.oa.oacore.thirdplatform.yading.constant;


import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public interface WebConstant {

    //测试万能验证码
    String TEST_PASS_CODE = "8888";

    //web登录token的key前缀
    String WEB_LOGIN_TOKEN = "web_login:";

    //oa登录token的key前缀
    String OA_LOGIN_TOKEN = "oa_login:";

    //九机oa扫码登录
    String JIUJI_OA_TOKEN ="urn:tokeninfo:";

    //超级后台登录token的key前缀
    String SUPER_ADMIN_LOGIN_TOKEN = "super_admin_login:";

    /**
     * 记录第三方登录的userId，后接uuid
     */
    String OPEN_LOGIN_TOKEN = "javaweb:open_login:_";

    /**
     * 微信授权
     */
    String KEY_PREFIX_WEIXIN_JSAPI_TICKET = "weixin_jsapi_ticket_";

    /**
     * 微信授权
     */
    String KEY_PREFIX_WEIXIN_INFO = "weixin_info_";

    /**
     * 微信小程序退出登录，微信信息前缀
     */
    String KEY_PREFIX_WEIXIN_INFO_OUT_LOGIN = "weixin_info_out_login_";

    //找回密码的token前缀，检验信息之后返给前端，在进行重置密码时需传入
    String RETRIEVE_PASS_WORD_TOKEN = "RETRIEVE_PASSWORD_TOKEN:";

    String AUTHORIZATION = "Authorization";

    /**
     * APP唯一标识
     */
    String APP_UNIQUE_IDENTIFICATION = "UUID";

    String DEVICE = "Device";

    String APP_VERSION = "Platform";

    String SYS_VERSION = "System";

    String TENANT_HEADER_NAME = "xtenant";


    String HTTPS_PREFIX = "https://";

    String HTTP_PREFIX = "http://";

    String TENANT_INFO = "tenant_info";

    String IOS = "IOS";

    String ANDROID = "ANDROID";

    /**
     * 优惠券列表url
     */
    String COUPON_LIST_URL = "/#/order/coupon-list/";

    /**
     * APP进行中订单页面跳转链接,
     */
    String APP_ON_GOING_ORDER_URL = "/native/userOnGoingOrder?isOnGoing=1";

    //客户分类
    String CUSTOMERTYPE = "customer_type";

    //客户类别（来源）
    String CUSTOMERSOURCE = "customer_source";

    //微信accessToken的key
    String WEIXIN_ACCESSTOKEN = "WEIXIN_ACCESSTOKEN_";

    //时间格式化
    DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    //web用户密码错误的登录次数，加上uuid作为key
    String WEB_LOGIN_ERRORTIMES_REDIS_KEY = "WEB_LOGIN_ERRORTIMES_";

    //OA用户密码错误的登录次数，加上uuid作为key
    String OA_LOGIN_ERRORTIMES_REDIS_KEY = "OA_LOGIN_ERRORTIMES_";

    /**
     * OA用户上一次是否通过滑动验证
     */
    String OA_LOGIN_SLIDING_VERIFICATION = "OA_LOGIN_SLIDING_VERIFICATION_";


    String MEMBER = "MEMBER_";

    //网站底部信息的key
    String BOTTOM_INFO_KEY = "BOTTOM_INFO_KEY_";

    /**
     * 运费规则前缀
     */
    String FREIGHT_REDIS_KEY = "freight_prefix_key_";

    /**
     * 用户折让金额前缀
     */
    String USER_DISCOUNT_AMOUNT_KEY = "user_discount_amount_";


    /**
     * 后台登录ip变化监控key
     */
    String ADM_LOGIN_IP_MONITOR_KEY = "adm:login:ip:monitor";

    String TRANSFER = "transfer:id:";
    String PC_LOGIN = "pcLogin";
    String TODO_NOTE = "todoNote";
    /**
     * 销售概览
     */
    String SALES_NEW_DATA = "salesNewData";
    String ATTENDANCE = "attendance";

    /**
     * 预约商品的价格，预约商品不显示分期
     */
    int YUYUE_PRICE = 1234567;

    /**
     * 暂无报价
     */
    int NO_PRICE = 12345678;

    /**
     * 图片(短信)校验码 redis的key前缀
     */
    interface VERIFY_REDIS_KEY {

        // 手机验证码
        String VERIFY_PHONE = "VERIFY_PHONE";

        // 手机动态密码短信
        String DYNAMIC_PASS_WORD = "VERIFY_DYNAMIC_PWD";

        //  退订退款短信验证
        String REFUND_VERIFY = "refund_verify";

        // 名片短信验证码
        String VERIFY_SP = "VERIFY_SP";

        // 用户信息（USER_REDISKEY+userId作为key）
        String USER_REDISKEY_PRE = "USER_REDISKEY_";

        // 绑定手机号（BIND_PHONE_PRE_+mobile作为key）
        String BIND_PHONE_PRE = "BIND_PHONE_PRE_";

        // 用户找回密码时发送的验证码（USER_RESETPWD+电话或邮箱作为key）
        String USER_RESET_PASS_WORD_PRE = "USER_RESETPWD_";

        //用户更新电话号码或者邮箱是发送的验证码，（USER_RESETINF0_+电话或邮箱作为key）
        String USER_RESETINFO_PRE = "USER_RESETINF0_";

        //web用户第一次登录时进行邮箱校验，WEB_LOGIN_EMAIL_CHECK_+邮箱作为key
        String WEB_LOGIN_EMAIL_CHECK = "WEB_LOGIN_EMAIL_CHECK_";

        //手机验证码24小时内
        String PHONE_24HOURS_VERIFY_PRE = "PHONE_24HOURS_VERIFY_PRE_";

        // 用户首次修改密码时进行手机号验证
        String USER_UPDATE_PASS_WORD_CHECK = "USER_UPDATE_PASS_WORD_CHECK_";

        //员工忘记密码时重置密码的验证码
        String FORGET_PASSWORD_VERIFICATION_CODE = "FORGET_PASSWORD_VERIFICATION_CODE";

        //员工忘记密码时验证码验证过后给App发送一个密文
        String FORGET_PASSWORD_VERIFICATION_CODE_CIPHERTEXT = "FORGET_PASSWORD_VERIFICATION_CODE_CIPHERTEXT";

        //员工注销发送验证码
        String STAFF_ACCOUNT_CANCELLATION = "STAFF_ACCOUNT_CANCELLATION";

        //员员工注销验证验证码过后给App发送一个密文
        String STAFF_ACCOUNT_CANCELLATION_CIPHERTEXT = "STAFF_ACCOUNT_CANCELLATION_CIPHERTEXT";

    }

    /**
     * 短信验证码/图片验证码类型，由于发送短信前都必须校验图片验证码，所以两种类型一致
     */
    interface VERIFY_CODE_TYPE {

        int WEB_LOGIN = 1;//web登录
        int OA_LOGIN = 2;//oa登录
        int WEB_RESET_PWD = 3;//web找回密码
        int OA_REGISTER = 4;//OA注册
        int ADDRESS = 5;//地址相关
        int WEB_REGISTER = 6;//web注册
        int BINDING_WEIXIN = 7;//绑定微信
        int DISCOUNT_BALANCE = 8;//折让或余额
        int DISCOUNT_BALANCE_ADMIN = 9;//折让或余额(后台收银)
        /**
         * 注册审核通过
         */
        int REGISTER_AUDIT_PASS_NOTICE = 10;
        /**
         * 注册审核拒绝
         */
        int REGISTER_AUDIT_REFUSE_NOTICE = 11;
    }

    /**
     * 短信验证码的key前缀，加上电话号码做为key
     */
    interface SMS_REDIS_KEY {

        String WEB_LOGIN_SMS = "WEB_LOGIN_SMS_";//web登录
        String OA_LOGIN_SMS = "OA_LOGIN_SMS_";//oa登录
        String WEB_RESET_PASS_WORD_SMS = "WEB_RESET_PWD_SMS_";// web找回密码，
        String OA_REGISTER_SMS = "OA_REGISTER_SMS_";// OA注册
        String ADDRESS_SMS = "ADDRESS_SMS_";// 地址相关
        String WEB_REGISTER_SMS = "WEB_REGISTER_SMS_";// web注册，
        String BINDING_WEIXIN_SMS = "BINDING_WEIXIN_SMS_";//绑定微信
        String DISCOUNT_BALANCE_SMS = "DISCOUNT_BALANCE_SMS_";//折让或余额
        String DISCOUNT_BALANCE_SMS_ADMIN = "DISCOUNT_BALANCE_SMS_ADMIN_";//折让或余额

    }

    /**
     * 图片验证码的key前缀，加上uuid作为key
     */
    interface IMG_REDIS_KEY {

        String WEB_LOGIN_VERIFYIMG = "WEB_LOGIN_VERIFYIMG_";//web登录
        String OA_LOGIN_VERIFYIMG = "OA_LOGIN_VERIFYIMG_";//oa登录
        String WEB_RESET_PASS_WORD_VERIFYIMG = "WEB_RESET_PWD_VERIFYIMG_";// web找回密码，
        String OA_REGISTER_VERIFYIMG = "OA_REGISTER_VERIFYIMG_";// OA注册
        String ADDRESS_VERIFYIMG = "ADDRESS_VERIFYIMG_";// 地址相关
        String WEB_REGISTER_VERIFYIMG = "WEB_REGISTER_VERIFYIMG_";// web注册，
        String BINDING_WEIXIN_VERIFYIMG = "BINDING_WEIXIN_VERIFYIMG_";//绑定微信
    }


    /**
     * 邮箱验证码类型
     */
    interface EMAIL_CODE_TYPE {

        /**
         * 修改手机
         */
        int UPDATE_MOBILE = 1;
    }


    /**
     * 邮箱验证码的redis key前缀，加上邮箱作为key
     */
    interface EMAIL_REDIS_KEY {

        /**
         * 修改手机
         */
        String UPDATE_MOBILE = "UPDATE_MOBILE_";
    }


    /**
     * 发短信拦截标识
     */
    interface SEND_INFO_JUDGE {

        String RETURN_CODE = "RETURN_CODE";
        String RETURN_MSG = "RETURN_MSG";
        String SUCCESSFLAG = "0";
        String ONEMIN = "1";
        String MIN90 = "2";
        String ONEDAY = "3";
        String OVERFLAG = "4";

        String SUCCESS_MSG = "成功";
        String ONEMIN_MSG = "一分钟内只能发一次哦";
        String MIN90_MSG = "一个半小时内最多发10次哦";
        String ONEDAY_MSG = "一天内最多发20次哦";
        String OVERFLAG_MSG = "发送短信过频繁，已被拦截哦";

    }

    /**
     * (短信)验证码 redis的key过期时间
     */
    interface VERIFY_REDIS_KEY_EXPIRE {

        Integer DYNAMIC_PWD_VERIFY_EXPIRE = 5;//接口userInfoService.sendMsg中 手机发送的动态码的过期时间 5分钟

    }

    /**
     * 公用请求头
     */
    interface REQUEST_HEADER {

        String CITY = "City";// 针对主站业务的多城市，所有request必须携带，值为用户选择或者定位的城市的id（第三级）

        String PLATFORM = "Platform";// 平台标识，所有的request必须携带，值为平台/版本，如：

        // PC/1.0.0、Android/3.3.0、iOS/3.0.0、M/1.0.0
        // 等，接收时注意大小写敏感。

        String AUTHORIZATION = "Authorization";// 用户加密字符串，所有的request必须携带，值为登录接口返回的token
        String ADMIN_TOKEN = "Token";// admin后台登录token
        String LOGIN_TOKEN = "token";// 登录接口返回的token

        String USER_AGENT = "User-Agent";// APP或各客户端的webview必须重写User-Agent，以区分网页是在浏览器还是客户端中打开

        String UUID = "UUID";

        String DEVICE = "Device";
        String XTENANT = "xtenant";
        //微信浏览器
        String WECHAT_BROWSER = "MicroMessenger";

        String SYS_VERSION = "version";
    }

    /**
     * 折让使用类型
     */
    interface DISCOUNT_USE_TYPE {

        int USE = 1; // 使用
        int BACK = 2; // 恢复
    }

    /**
     * 折让规则
     */
    interface DISCOUNT_RULE {

        int NO_LIMIT = 0; //不限
        int MUST_INCLUDE = 1; //必须包含
        int NO_INCLUDE = 2; //不可包含
        int MIXTURE_RATIO = 3; //配比
        int GREATER_THAN = 4; //必须大于
        int BRAND_INCLUDE = 5; //包含品牌
        int BRAND_EXCLUDE = 6; //不包含品牌
    }

    /**
     * 折让规则限制
     */
    interface DISCOUNT_RULE_LIMIT {

        Integer MODEL = 1; //机型
        Integer PRODUCT = 2; //商品
    }

    /**
     * 活动赠品限制类型
     */
    interface ACTIVITY_GIFTS_LIMIT {

        Integer COUNT = 1; //数量
        Integer AMOUNT = 2; //金额
    }

    /**
     * 用户信息模糊类型
     */
    interface USER_SEARCH_TYPE {

        Integer USER = 1; //用户
        Integer COMPANY = 2; //公司
    }

    /**
     * 折让状态
     */
    interface DISCOUNT_STATUS {

        Integer VALID = 1; //有效
        Integer USED = 2; //已使用
        Integer OVERDUE = 3; //已过期
        Integer UNCONFIRM = 4; //未确认
        Integer CONFIRM = 5; //已确认
    }

    /**
     * 用户操作日志类型
     */
    interface USER_LOG_TYPE {

        int LOGIN = 1;//登录
        int ADD_ORDER = 2;//添加订单
        int CHANGE_ORDER = 3;//修改订单
        int DELETE_ORDER = 4;//删除订单
        int TOP_UP = 5;//充值
        int CHANGE_ADDRESS = 6;//修改地址
        int CHANGE_PASSWORD = 7;//修改密码
        int CHANGE_MOBILE = 8;//修改手机
        int CHANGE_EMAIL = 9;//修改邮箱
    }

    /**
     * 微信场景类型
     */
    interface WEIXIN_SCENE_TYPE {

        Integer BINDING_WEIXIN = 1;//绑定微信
    }

    /**
     * 正则表达式
     */
    interface REGULAR {

        String DOUBLE_BRACE_PATTERN = "\\{\\{(.*?)\\}\\}";
        //        String PASSWORD_PATTERN = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{6,}.*$";
        String P_PATTERN = "^(?=.*\\d)(?=.*[A-Za-z]).{6,16}$";
        String SPECIAL_CHARACTER = "^.*[_`~!@#$%^&*()+=|{}':;',\\-\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]+.*$";
        String BARCODE_PATTERN = "/^69\\d{11}$/";
        String PHONE = "^(1[3-9])\\d{9}$"; //手机号码正则(弱规则，只保证格式)
    }

    interface RETURN_MONEY_RET {

        String SUCCESS = "退款成功";
        String FAIL = "退款失败";
    }

    /**
     * 维护类型
     */
    interface WEB_MAINTAIN_TYPE {

        /**
         * 注册
         */
        Integer REGISTER = 1;
        /**
         * 全局折让
         */
        Integer DISCOUNT = 2;
        /**
         * 公司
         */
        Integer COMPANY = 3;

        /**
         * 公司附件模板
         */
        Integer ANNEX_TEMPLATE = 4;

        /**
         * 地址附件模板
         */
        Integer ADDRESS_TEMPLATE = 5;

        /**
         * 邮箱附件模板
         */
        Integer EMAIL_TEMPLATE = 6;

        /**
         * 法人附件模板
         */
        Integer LEGAL_TEMPLATE = 7;
        /**
         * 开票信息附件模板
         */
        Integer INVOICE_TEMPLATE = 8;
    }

    interface PICTURE_PATH {

        String PATH_NEWSTATIC = "newstatic/";
        String PIC_70x70 = "pic/product/70x70/";
        String PIC_160x160 = "pic/product/160x160/";
        String PIC_216x216 = "pic/product/216x216/";
        String PIC_440x440 = "pic/product/440x440/";
        String PIC_800x800 = "pic/product/800x800/";
    }

    interface PICTURE_PREFIX {

        String HEAD_DEF = "2182/02634dba4b12ff2c.png";// 登录后的默认头像
        String NOT_LOGIN_AVATAR = "2179/02634c3600306802.png"; // 未登录默认头像
    }

    /**
     * 自动化测试手机号
     */
    interface AUTO_TEST_PHONE {

        String TEST_PHONE = "1409527";
        String SMS_VERIFY = "0721";
    }

    /**
     * 第三方登录方式
     */
    interface OPEN_LOGIN_TYPE {

        int QQ_LOGIN = 1;//QQ登录
        int WEIXIN_LOGIN = 2;//微信登录
    }

    /**
     * PC个人中心图片
     */
    interface PC_MEMBER_CENTER_IMAGE {
        String BIRTHDAY = "1380/01132d318b47d81f.png";
        String MOBILE = "1383/01132d40e5444dfb.png";
        String WECHAT = "1381/01132d38a4e6f601.png";
        String IMPROVE = "1383/0114f4f7826f5366.png";
        String VIPCLUB = "1380/01132d1d946bf519.jpg";
    }

    /**
     * 系统配置类型
     */
    interface SYS_CONFIG_TYPE {

        /**
         * 微信配置
         */
        Integer WEIXIN = 1;
        /**
         * 邮箱配置
         */
        Integer EMAIL = 2;
    }

    /**
     * 手机序列号识别
     */
    interface NumberIdentify {

        /**
         * 手机序列号识别Token缓存key
         */
        String IDENTIFY_TOKEN = "";

        /**
         * 图片大小，最大不能超过4M
         */
        Integer MAX_IMAGE_SIZE = 4 * 1024 * 1024;

        /**
         * base64图片开头标识
         */
        String IMAGE_CONTENT_HEADER = "data:image/jpeg;base64,";

        /**
         * Token异常错误码
         */
        String TOKEN_VALID_ERROR_CODE = "110";

    }

    /**
     * 平台标识
     */
    interface CLIENT_PLAT {

        String M = "M";// 手机移动M端

        String PC = "PC";// PC端

        String iOS = "iOS";// iOS端

        String Android = "Android";// Android端

        String WECHAT_SHOP = "mp";// 微信小程序商城

        String IM = "im";// IM项目端
        /**
         * 门店的台式机订单 选机订单
         */
        String STREET_SHOP = "shop";

        /**
         * App里的m版
         */
        String APP = "app";
    }

    /**
     * 主题相关的redis的key
     */
    interface THEME_REDIS_KEY {
        String THEME_CLOUD = "theme_cloud";
        String THEME_CLOUD_DC = "theme_cloud_dc";
        String THEME_FOR_JS = "theme-for-js";
        String THEME_FOR_JSON = "theme-for-json";
    }


    /**
     * 取货方式
     */
    interface DELIVERY_TYPE{
        /**
         * 到店自取
         */
        int AREA_PICK=1;

        /**
         * 快递运输
         */
        int PACKAGE_DILIVERY=2;
    }

    /**
     * 通用正则表达式
     */
    interface REG_EXP {
        String COMMA_MINUS_NUM = "^$|^(-)?\\d+(,(-)?\\d+)*$";//为空或者逗号分隔的数字，数字可以是负数

        String COMMA_NUM = "^$|\\d+(,\\d+)*$";//为空或者逗号分隔的数字，只能是正数

        String BRACKET_SURROUND = "(\\（|\\().*(\\）|\\))";//（中英文）括号包围的    "\(.*?\)|（.*?）|（.*?\)|\(.*?）|\[.*?\]"

        /**
         * （中英文）括号包围的或前后跟了空格的
         */
        String BRACKET_SURROUND_AND_SPACE = "([\\s]*[(（]).*?([)）][\\s]*)";

        String VERSION = "\\d+\\.\\d\\.\\d";

        String LANDLINE_TELEPHONE = "^([0-9]{3,4}-)?[0-9]{7,8}$"; //固定电话

        String PHONE = "^(1[3-9])\\d{9}$"; //手机号码正则(弱规则，只保证格式)

        String PHONE_NUMBER = "^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\\d{8}$"; //手机号码正则，比上一个精细，但不一定是最新。(2018年5月最新)

        String CHINESE = "[\\u4e00-\\u9fa5]+";

        // 身份证号正则
        String ID_CARD = "(^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$)|(^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx])$)";

        // 是否包含身份证
        String HAS_ID_CARD = "^.*(([1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3})|([1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx]))).*$";

        /**
         *  小括号匹配正则
         */
        String BRACKETS_REGEX = "([(（]).*?([)）])";
    }


    /**
     * 会员俱乐部图标
     */
    interface MEMBER_CENTRE_IMAGE {
        /**
         * 普通会员
         */
        String generalMember = "newstatic/2181/02128a152d22a608.png";
        /**
         * 青铜会员
         */
        String bronzeMember = "newstatic/2182/02128a1b877107fd.png";
        /**
         * 白银会员
         */
        String silverMember = "newstatic/2181/02128a226e80fcf3.png";
        /**
         * 黄金会员
         */
        String goldMember = "newstatic/2179/02128a2604e8eece.png";
        /**
         * 钻石会员
         */
        String diamond = "newstatic/2178/02128a2c8f58350e.png";
        /**
         * 双钻会员
         */
        String towDiamond = "newstatic/2184/02128a3253319126.png";
    }


    interface DEPART_REPORT_CONFIG {

        /**
         * 时间段 key
         */
        String TIME_FRAME_PRE_KEY = "TIME_FRAME_PRE_KEY_";

        /**
         * 同步4位尾号 KEY
         */
        String TAIL_NUMBER_KEY = "TAIL_NUMBER_";

        /**
         * 上报类型 订单完成 增加金额
         */
        Integer REPORT_TYPE_COMPLETE = 0;

        /**
         * 上报类型 订单退款 减少金额
         */
        Integer REPORT_TYPE_REFUND = 1;

        /**
         * 上报状态  未处理
         */
        Integer REPORT_STATE_INIT = 0;

        /**
         * 上报状态 上报成功
         */
        Integer REPORT_STATE_SUCCESS = 1;

        /**
         * 上报状态 上报失败
         */
        Integer REPORT_STATE_FAILED = 2;

        /**
         * 调用次数上限
         */
        Integer CALL_LIMIT_COUNT = 5;



    }

    interface PAGE_PARAM {
        String PAGES = "pages";

        String TOTAL = "total";

        String RECORDS = "records";

        String CURRENT = "current";

        String SIZE = "size";

        String DATA = "data";

        String CODE = "code";

        String USER_MSG = "userMsg";

        String MSG = "msg";
    }

}


