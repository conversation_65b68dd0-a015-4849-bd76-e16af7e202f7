package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiuji.oa.afterservice.bigpro.enums.DaiyongjiPayStatesEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description ${comments}
 *
 * <AUTHOR> quan
 * @date 2020-05-11 09:02:32
 */
@Data
@TableName("daiyongji_yajin")
public class DaiyongjiYajin implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * $column.comments
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;

	/**
	 * $column.comments
	 */
	private Integer wxid;

	/**
	 * $column.comments
	 */
	private Integer dyjid;

	/**
	 * $column.comments
	 */
	private BigDecimal yajin;

	/**
	 * $column.comments
	 */
	private LocalDateTime applydate;

	/**
	 * $column.comments
	 * @see DaiyongjiPayStatesEnum
	 */
	private Integer paystate;

	/**
	 * $column.comments
	 */
	private String payorderid;

	/**
	 * 备用机是否取消(归还或未归还都是true)
	 * $column.comments
	 */
	private Boolean cancel;

	/**
	 * $column.comments
	 */
	private String inuser;

	/**
	 * $column.comments
	 */
	private String canceluser;

	/**
	 * $column.comments
	 */
	private LocalDateTime canceldate;

	/**
	 * $column.comments
	 */
	private BigDecimal total;

	/**
	 * $column.comments
	 */
	private String paytype ="";

	/**
	 * $column.comments
	 */
	private Integer pzid;

	/**
	 * $column.comments
	 */
	private Integer pzid2;

	/**
	 * $column.comments
	 */
	private String signimg;

	/**
	 * $column.comments
	 */
	private LocalDateTime signtime;

}
