package com.jiuji.oa.oacore.subRecommended.enums;

import cn.hutool.core.util.ObjectUtil;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;

@Getter
@AllArgsConstructor
public enum MainConfigTimeTypeEnum implements CodeMessageEnumInterface {


    EFFECTIVE_TIME(1, "生效时间"),
    TRADING_HOURS(2, "交易时间");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 对应编码信息
     */
    private String message;



}
