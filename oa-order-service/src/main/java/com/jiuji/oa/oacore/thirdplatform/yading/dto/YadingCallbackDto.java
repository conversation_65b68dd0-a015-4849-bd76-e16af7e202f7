package com.jiuji.oa.oacore.thirdplatform.yading.dto;

import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingCallbackMethodEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * @Description 亚丁回调参数封装对象
 *
 * <AUTHOR>
 * @Date   2022/5/12 19:33
 */
@Data
public class YadingCallbackDto {

    /**
     * 回调数据参考：
     * 注册提交成功通知：
     * 提交时间：submitTime
     *
     * 注册成功通知：
     * 保险生效开始时间：efficientStartTime
     * 保险生效截止时间：efficientEndTime
     * 审核时间：checkTime
     * 审核状态：exa_status
     *
     * 注册失败通知：
     * 审核时间：checkTime
     * 失败备注说明：remark
     * 审核状态：exa_status
     *
     * 理赔办理通知：
     * 保险编号：insuranceCode
     * 理赔办理时间：checkTime
     * 理赔备注说明：remark
     * 审核状态：exa_status
     *
     * 换机完成通知：
     * 换机时间：checkTime
     * 换机备注说明：remark
     * 审核状态：exa_status
     *
     * 更换imei通知：
     * 新imei: newImei
     * 旧imei: oldImei
     * 更换时间: checkTime
     * 更换备注说明：remark
     * 审核状态：exa_status
     *
     * 照片补交通知：
     * 补交时间：checkTime
     * 审核状态：exa_status
     **/

    /**
     * 当前业务节点
     * 公共参数
     */
    private YadingCallbackMethodEnum methodEnum;

    /**
     * 时间戳
     * 公共参数
     */
    private LocalDateTime timestamp;

    /**
     * 公司编号
     * 公共参数
     */
    private String companyCode;

    /**
     * 业务单号
     * 公共参数
     */
    private Long subId;

    /**
     * 审核状态
     * 公共参数
     */
    private Boolean checkStatus;


    /**
     * 审核时间
     * 公共参数
     */
    private LocalDateTime checkTime;

    /**
     * 备注说明
     * 公共参数
     */
    private String remark;

    /**
     * 保险编号
     */
    private String insuranceCode;

    /**
     * 新imei
     **/
    private String newImei;

    /**
     * 旧imei
     **/
    private String oldImei;

    /**
     * 保险有效期起始和截止时间
     */
    private LocalDate efficientStartDate;

    private LocalDate efficientEndDate;


    /**
     * 保险成本
     */
    private BigDecimal costPrice;

    /**
     * 注册提交成功时间
     * 注册提交节点
     */
    private LocalDateTime submitTime;


    public static final String METHOD_ENUM = "method";
    public static final String TIMESTAMP = "timestamp";
    public static final String COMPANY_CODE = "companyCode";
    public static final String SUB_ID = "subId";
    public static final String CONTENT = "content";
    public static final String CHECK_STATUS = "exa_status";
    public static final String CHECK_TIME = "checkTime";
    public static final String REMARK = "remark";
    public static final String INSURANCE_CODE = "insuranceCode";
    public static final String NEW_IMEI = "newImei";
    public static final String OLD_IMEI = "oldImei";
    public static final String COST_PRICE = "cost";
    public static final String EFFICIENT_START_TIME = "efficientStartTime";
    public static final String EFFICIENT_END_TIME = "efficientEndTime";
    public static final String SUBMIT_TIME = "submitTime";

}
