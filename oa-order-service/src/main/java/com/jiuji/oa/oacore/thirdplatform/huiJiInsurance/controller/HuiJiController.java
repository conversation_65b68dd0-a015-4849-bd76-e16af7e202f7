package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddOrderLog;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.entity.HuiJiBaoNotify;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.DeleteHuiJiBaoReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.RegisterQrcodeReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.UpdateOrderInfoReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res.HuijbResult;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res.RegisterQrcodeRes;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service.HuiJiBaoNotifyService;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service.HuiJiService;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingResultEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


@Slf4j
@RestController
@RequestMapping("/api/HuiJiController/")
public class HuiJiController {

    @Resource
    private HuiJiService huiJiService;
    @Resource
    private HuiJiBaoNotifyService huiJiBaoNotifyService;

    /**
     * 汇机保获取token
     * @return
     */
    @GetMapping("/selectHuiJiAccessToken/v1")
    public Result<String> selectHuiJiAccessToken() {
        return Result.success(huiJiService.getHuiJiAccessToken());
    }


    /**
     * 订单信息修改
     * @param req
     * @return
     */
    @PostMapping("/updateOrder/v1")
    public Result<Boolean> updateOrder(UpdateOrderInfoReq req){
        return huiJiBaoNotifyService.updateOrder(req);
    }




    /**
     * 获取汇机保注册二维码
     * @param req
     * @return
     */
    @PostMapping("/register/qrcode/v1")
    public Result<RegisterQrcodeRes> selectRegisterQrcode(@RequestBody @Valid RegisterQrcodeReq req) {
        log.warn("OA调用order项目获取汇机保注册接口传入参数:{}", JSONUtil.toJsonStr(req));
        RegisterQrcodeRes registerQrcode = huiJiService.getRegisterQrcode(req);
        log.warn("OA调用order项目获取汇机保注册接口传返回结果:{}", JSONUtil.toJsonStr(registerQrcode));
        return Result.success(registerQrcode);
    }

    /**
     * 接收汇机保回调信息
     * @param body
     * @return
     */
    @AddOrderLog(type = AddLogKind.HUIJI_INSURANCE_CALLBACK ,returnType = HuijbResult.class)
    @PostMapping("/receive/huijb/v1")
    public HuijbResult receiveHuijb(@RequestBody String body) {
        log.info("接收汇机保回调信息:{}",body);
        HuijbResult result = new HuijbResult();
        if(StringUtils.isBlank(body)){
            result.setState(YadingResultEnum.REQUEST_BODY_EMPTY.getMsg());
            return result;
        }
        // 保存到数据库
        HuiJiBaoNotify huiJiBaoNotify = new HuiJiBaoNotify();
        huiJiBaoNotify.setOriginalMsg(body.replaceAll("/r/n|/t",""));
        String msg = huiJiService.huijbNotifyHandle(huiJiBaoNotify);
        if(StringUtils.isEmpty(msg) || !"SUCCESS".equals(msg)){
            RRExceptionHandler.logError("接收汇机保回调信息异常",body , null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        result.setState(msg);
        return result;
    }


    /**
     * 删除汇机保信息
     * @param deleteHuiJiBaoReq
     * @return
     */
    @GetMapping("/deleteHuiJiBao/v1")
    public Result<String> deleteHuiJiBao(@RequestBody @Valid DeleteHuiJiBaoReq deleteHuiJiBaoReq) {
        log.warn("汇机保删除传入参数：{}",JSONUtil.toJsonStr(deleteHuiJiBaoReq));
        Result<String> stringResult = huiJiService.deleteHuiJiBao(deleteHuiJiBaoReq);
        log.warn("汇机保删除返回结果：{}",JSONUtil.toJsonStr(stringResult));
        return stringResult ;
    }
}
