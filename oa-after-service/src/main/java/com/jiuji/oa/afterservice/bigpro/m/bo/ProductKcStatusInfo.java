package com.jiuji.oa.afterservice.bigpro.m.bo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class ProductKcStatusInfo{
    @ApiModelProperty(value = "门店名称")
    private String area;

    @ApiModelProperty(value = "门店中文名称")
    private String areaName;

    @ApiModelProperty(value = "门店 英文+中文")
    private String areaText;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入库时间")
    private LocalDateTime dTime;

    @ApiModelProperty(value = "库龄 单位（天）")
    private Integer dDays;

    @ApiModelProperty(value = "机型")
    private String productName;

    @ApiModelProperty(value = "规格")
    private String productColor;

    @ApiModelProperty(value = "库存类别")
    private Integer kcType;

    @ApiModelProperty(value = "库存状态中文描述")
    private String kcTypeText;

    @ApiModelProperty(value = "库存状态")
    private Integer kcStatus;

    @ApiModelProperty(value = "库存状态中文描述")
    private String kcStatusText;

    @ApiModelProperty(value = "备注")
    private String remarks;
}
