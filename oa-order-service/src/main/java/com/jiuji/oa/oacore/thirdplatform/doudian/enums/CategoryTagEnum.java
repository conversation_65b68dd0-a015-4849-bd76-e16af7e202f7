package com.jiuji.oa.oacore.thirdplatform.doudian.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CategoryTagEnum implements IEnum<Integer>, CodeMessageEnumInterface {
    CLEARANCE(1, "清仓"),
    ANDROID(2, "安卓"),
    HUAWEI(3, "华为"),
    STOCK(4, "备货"),
    NONE(5, "无");

    private final Integer code;
    private final String message;

    @Override
    public Integer getValue() {
        return code;
    }
}
