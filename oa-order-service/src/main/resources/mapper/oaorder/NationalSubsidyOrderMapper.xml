<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.mapper.NationalSubsidyOrderMapper">

    <select id="selectNationalSubsidyOrderList" resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT
            s.sub_id AS orderNo,
            ISNULL(sr.check_state, 0) AS checkState,
            a.id AS shopId,
            a.area AS shopCode,
            a.area_name AS shopName,
            b.basket_date AS addTime,
            s.tradeDate1 AS tradeTime,
            s.sub_check AS orderStatus,
            s.sub_to AS contactPerson,
            s.sub_mobile AS contactPhone,
            b.seller AS salesperson,
            s.trader AS trader,
            p.product_name AS productName,
            p.product_color AS specification,
            b.ppriceid AS skuId,
            bgpi.basket_id as xiaomiBasketId,
            c.name AS category,
            r.name AS brand,
            b.price1 * b.basket_count AS salesPrice,
            ISNULL(s.yifuM, 0) AS actualAmount,
            b.basket_count AS quantity,
            ISNULL(k.imei, bgpi.imei) AS serialNumber,
            ISNULL(k.imei2, bgpi.imei2) AS auxiliarySerialNumber,
            ISNULL(k.imei3, bgpi.sn) AS productSN,
            s.comment AS orderRemark,
            tpm.pro_mark AS productStatisticsTag,
            isnull(bgpi.bar_code,p.barCode) as barCode,
            isnull(n.efficiency_level,be.efficiency_level) as efficiencyLevel,
            be.subsidy_ratio AS subsidyRatio,
            be.max_subsidy_price AS maxSubsidyPrice,
            be.efficiency_level_id AS efficiencyLevelId
        FROM
            subFlagRecord sr WITH(NOLOCK)
            LEFT JOIN sub s WITH(NOLOCK) ON sr.sub_id = s.sub_id
            LEFT JOIN basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
            LEFT JOIN dbo.basket_extend be with(nolock) ON be.basket_id = b.basket_id
            LEFT JOIN areainfo a WITH(NOLOCK) ON a.id = s.areaid
            LEFT JOIN product_mkc k WITH(NOLOCK) ON b.basket_id = k.basket_id
            LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON b.ppriceid = p.ppriceid
            LEFT JOIN t_product_mark tpm WITH(NOLOCK) ON tpm.id = p.markId
            LEFT JOIN dbo.category c WITH(NOLOCK) ON p.cid = c.ID
            LEFT JOIN dbo.brand r WITH(NOLOCK) ON r.id = p.brandID
            LEFT JOIN dbo.basket_guobu_product_info bgpi WITH(NOLOCK) ON b.basket_id = bgpi.basket_id and bgpi.is_del = 0
            LEFT JOIN dbo.tax_code_old_new n with(nolock) on bgpi.bar_code = n.efficiency_code and n.activity_type=3 and n.ppriceid=0
        <where>
            sr.flagType = 7 AND ISNULL(b.isdel,0) = 0
            <!-- 租户授权隔离-->
            <if test="req.authorizeId != null and req.authPart != null">
                <if test="req.authPart == true">
                    and a.authorizeid = #{req.authorizeId}
                </if>
            </if>
            <if test="req.xTenant != null ">
                and a.xtenant = #{req.xTenant}
            </if>

            <!-- 时间查询 -->
            <if test="req.timeType != null and req.startTime != null and req.endTime != null">
                <choose>
                    <when test="req.timeType == 1">
                        AND s.tradeDate1 BETWEEN #{req.startTime} AND #{req.endTime}
                    </when>
                    <when test="req.timeType == 2">
                        AND b.basket_date BETWEEN #{req.startTime} AND #{req.endTime}
                    </when>
                    <when test="req.timeType == 3">
                        AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                        <include refid="invoiceExists"/>
                        AND t.jingbandtime BETWEEN #{req.startTime} AND #{req.endTime})
                    </when>
                    <when test="req.timeType == 4">
                        AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                        <include refid="invoiceExists"/>
                        AND t.dtime BETWEEN #{req.startTime} AND #{req.endTime})
                    </when>
                </choose>
            </if>

            <!-- 门店查询 -->
            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                AND a.id IN
                <foreach collection="req.areaIdList" item="areaIds" open="(" separator="," close=")">
                    #{areaIds}
                </foreach>
            </if>

            <if test="req.subsidyPaymentThreePartiesTypes != null and req.subsidyPaymentThreePartiesTypes.size() > 0">
                AND EXISTS (
                SELECT 1 FROM
                shouying sy WITH(NOLOCK)
                LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
                LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sy.areaid
                LEFT JOIN (SELECT * FROM dbo.sysConfig c WITH(NOLOCK) WHERE c.code IN (36, 37)) sys ON sys.value = so.type_
                    AND (ISNULL(sys.authId, 0) = 0 OR sys.authId = a.authorizeid OR (ISNULL(sys.areaids, '') &lt;> '' AND CHARINDEX(',' + CAST(a.id AS NVARCHAR(10)) + ',', ',' + sys.areaids + ',') > 0))
                WHERE sy.sub_id = s.sub_id
                <include refid="cashierThreePartiesExists"/>
                AND sys.id IN
                <foreach collection="req.subsidyPaymentThreePartiesTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
                )
            </if>

            <if test="req.settlementStatus != null">
                <choose>
                    <when test="req.settlementStatus == 1">
                        AND EXISTS (
                        SELECT 1 FROM
                        shouying sy WITH(NOLOCK)
                        LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
                        LEFT JOIN (SELECT * FROM dbo.sysConfig c WITH(NOLOCK) WHERE c.code IN (36, 37)) sys ON sys.value = so.type_
                        AND (ISNULL(sys.authId, 0) = 0 OR sys.authId = a.authorizeid OR (ISNULL(sys.areaids, '') &lt;> '' AND CHARINDEX(',' + CAST(a.id AS NVARCHAR(10)) + ',', ',' + sys.areaids + ',') > 0))
                        WHERE sy.sub_id = s.sub_id
                        <include refid="cashierThreePartiesExists"/>
                        AND ISNULL(sy.sub_pay05 - ISNULL(so.jsPrice, 0) - ISNULL(so.other_shouxum, 0), 0 ) > 0
                        )
                    </when>
                    <when test="req.settlementStatus == 2">
                        AND EXISTS (
                        SELECT 1 FROM
                        shouying sy WITH(NOLOCK)
                        LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
                        LEFT JOIN (SELECT * FROM dbo.sysConfig c WITH(NOLOCK) WHERE c.code IN (36, 37)) sys ON sys.value = so.type_
                        AND (ISNULL(sys.authId, 0) = 0 OR sys.authId = a.authorizeid OR (ISNULL(sys.areaids, '') &lt;> '' AND CHARINDEX(',' + CAST(a.id AS NVARCHAR(10)) + ',', ',' + sys.areaids + ',') > 0))
                        WHERE sy.sub_id = s.sub_id
                        <include refid="cashierThreePartiesExists"/>
                        AND ISNULL(sy.sub_pay05 - ISNULL(so.jsPrice, 0) - ISNULL(so.other_shouxum, 0), 0 ) = 0
                        )
                    </when>
                </choose>
            </if>
            
            <if test="req.searchKeyword != null and req.searchKeyword != '' and req.searchType1 != null ">
                <if test="req.searchType1 == 1">
                    AND p.product_name LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
                <if test="req.searchType1 == 2">
                    AND k.ppriceid = #{req.searchKeyword}
                </if>
                <if test="req.searchType1 == 3">
                    AND p.product_id = #{req.searchKeyword}
                </if>
                <if test="req.searchType1 == 4">
                    AND s.sub_id = #{req.searchKeyword}
                </if>
                <if test="req.searchType1 == 5">
                    AND k.imei LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
                <if test="req.searchType1 == 6">
                    AND k.imei3 LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
                <if test="req.searchType1 == 7">
                    AND s.comment LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
                <if test="req.searchType1 == 8">
                    AND k.imei2 LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
                <if test="req.searchType1 == 9">
                    AND isnull(bgpi.bar_code,p.barCode) =  #{req.searchKeyword}
                </if>

                <if test="req.searchType1 == 10">
                    AND EXISTS (
                    SELECT 1 FROM
                    shouying sy WITH(NOLOCK)
                    LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
                    LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sy.areaid
                    LEFT JOIN (SELECT * FROM dbo.sysConfig c WITH(NOLOCK) WHERE c.code IN (36, 37)) sys ON sys.value =
                    so.type_
                    AND (ISNULL(sys.authId, 0) = 0 OR sys.authId = a.authorizeid OR (ISNULL(sys.areaids, '') &lt;> ''
                    AND CHARINDEX(',' + CAST(a.id AS NVARCHAR(10)) + ',', ',' + sys.areaids + ',') > 0))
                    WHERE sy.sub_id = s.sub_id
                    AND so.num LIKE  CONCAT('%', #{req.searchKeyword}, '%')
                    <include refid="cashierThreePartiesExists"/>
                    )
                </if>

                <if test="req.searchType1 == 11">
                    AND bgpi.basket_id LIKE CONCAT('%', #{req.searchKeyword}, '%')
                </if>
            </if>


            <if test="req.searchKeyword2 != null and req.searchKeyword2 != '' and req.searchType2 != null ">
                <if test="req.searchType2 == 1">
                    AND b.seller LIKE CONCAT('%', #{req.searchKeyword2}, '%')
                </if>
                <if test="req.searchType2 == 2">
                    AND s.trader LIKE CONCAT('%', #{req.searchKeyword2}, '%')
                </if>
                <if test="req.searchType2 == 3">
                    AND s.sub_mobile LIKE CONCAT('%', #{req.searchKeyword2}, '%')
                </if>
                <if test="req.searchType2 == 4">
                    AND s.sub_to LIKE CONCAT('%', #{req.searchKeyword2}, '%')
                </if>
            </if>
            
            <if test="req.searchKeyword3 != null and req.searchKeyword3 != '' and req.searchType3 != null ">
                <if test="req.searchType3 == 1">
                    AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND t.id = #{req.searchKeyword3})
                </if>
                <if test="req.searchType3 == 2">
                    AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND t.name LIKE CONCAT('%', #{req.searchKeyword3}, '%'))
                </if>
                <if test="req.searchType3 == 3">
                    AND EXISTS (
                    SELECT 1 FROM tax_piao t WITH(NOLOCK)
                    LEFT JOIN tax_piao_response tpr WITH(NOLOCK) ON tpr.piaoid = t.id AND tpr.isQuery = 1
                    WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND tpr.fpdm LIKE CONCAT('%', #{req.searchKeyword3}, '%')
                    )
                </if>
                <if test="req.searchType3 == 4">
                    AND EXISTS (
                    SELECT 1 FROM tax_piao t WITH(NOLOCK)
                    LEFT JOIN tax_piao_response tpr WITH(NOLOCK) ON tpr.piaoid = t.id AND tpr.isQuery = 1
                    WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND ISNULL(tpr.fphm, t.piao_number) LIKE CONCAT('%', #{req.searchKeyword3}, '%')
                    )
                </if>
                <if test="req.searchType3 == 5">
                    AND EXISTS (
                    SELECT 1 FROM tax_piao t WITH(NOLOCK)
                    LEFT JOIN tax_swzheng ts WITH(NOLOCK) ON ts.id = t.swid
                    WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND ts.company_name LIKE CONCAT('%', #{req.searchKeyword3}, '%')
                    )
                </if>
                <if test="req.searchType3 == 6">
                    AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                    <include refid="invoiceExists"/>
                    AND t.piaoRemark LIKE CONCAT('%', #{req.searchKeyword3}, '%'))
                </if>
            </if>
            
            <if test="req.orderStatus != null and req.orderStatus.size() > 0">
                AND s.sub_check IN
                <foreach collection="req.orderStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <if test="req.barCode != null and req.barCode != ''">
                AND isnull(bgpi.bar_code,p.barCode) = #{req.barCode}
            </if>

            <!-- 能效查询 -->
            <if test="req.efficiencyId != null and req.efficiencyId != 0">
                AND isnull(be.efficiency_level_id,0) = #{req.efficiencyId}
            </if>

            <if test="req.efficiency != null and req.efficiency != '' and (req.efficiencyId == null or req.efficiencyId == 0)">
                AND isnull(n.efficiency_level,be.efficiency_level) = #{req.efficiency}
            </if>

            <!--查询未提交发票的数据-->
            <if test="req.queryNoInvoice != null and req.queryNoInvoice">
                AND NOT EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                <include refid="invoiceExists"/>
                )
            </if>


            <if test="req.invoiceStatus != null and req.invoiceStatus.size() > 0">
                AND EXISTS (SELECT 1 FROM tax_piao t WITH(NOLOCK) WHERE t.sub_id = s.sub_id
                <include refid="invoiceExists"/>
                AND t.flag IN
                <foreach collection="req.invoiceStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
                )
            </if>

            <if test="req.productStatisticsTag != null and req.productStatisticsTag.size() > 0">
                AND tpm.id IN
                <foreach collection="req.productStatisticsTag" item="tag" open="(" separator="," close=")">
                    #{tag}
                </foreach>
            </if>

            <if test="req.cidList != null and req.cidList.size() > 0">
                AND c.id IN
                <foreach collection="req.cidList" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>

            <if test="req.brandIdList != null and req.brandIdList.size() > 0">
                AND r.id IN
                <foreach collection="req.brandIdList" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
        </where>
    </select>

    <sql id="cashierThreePartiesExists">
            AND EXISTS(SELECT 1 FROM dbo.payment_guobu_config pgc with(nolock) WHERE pgc.payment_type=1 AND pgc.payment_state = 1 AND pgc.payment_link_id = sys.id)
            AND sy.shouying_type IN (N'订金', N'交易')
            AND ISNULL(so.refund_price,0)=0
    </sql>

    <sql id="invoiceExists">
       AND t.type_ = 0
        <!-- 查询的时候不包含红票的数据 -->
       AND NOT EXISTS (
        SELECT 1 FROM tax_piao_response tpr WITH(NOLOCK)
        WHERE tpr.piaoid = t.id and tpr.requestKind != 0
        )
    </sql>

    <select id="selectNationalSubsidyInvoiceList"
            resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT
            t.sub_id AS orderNo,
            t.flag AS invoiceStatus,
            t.dtime AS invoiceSubmitTime,
            t.jingbandtime AS invoiceOperateTime,
            t.customType AS headerType,
            t.name AS invoiceHeader,
            ts.company_name AS sellerCompanyName,
            t.piaoRemark AS invoiceRemark,
            tpr.fpdm AS invoiceCode,
            ISNULL(tpr.fphm, t.piao_number) AS invoiceNumber,
            ep.fileid AS invoiceUrls
        FROM
            tax_piao t WITH(NOLOCK)
            LEFT JOIN tax_piao_response tpr WITH(NOLOCK) ON tpr.piaoid = t.id AND tpr.isQuery = 1
            LEFT JOIN electronPiao ep WITH(NOLOCK) ON ep.piaoid = t.id AND ep.InvoiceID = tpr.fphm
            LEFT JOIN tax_swzheng ts WITH(NOLOCK) ON ts.id = t.swid
        WHERE t.sub_id in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        <include refid="invoiceExists"/>
        ORDER BY t.id DESC
    </select>

    <select id="selectNationalSubsidyCashierList"
            resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT
            sy.sub_id AS orderNo,
            sys.name AS threePartiesPaymentType,
            '' AS posPaymentType,
            so.num AS contractNo,
            sy.sub_pay05 + sy.sub_pay07 AS cashierAmount,
            sy.sub_pay05 - ISNULL(so.jsPrice, 0) - ISNULL(so.other_shouxum, 0) AS subsidyUnsettledAmount
        FROM
            shouying sy WITH(NOLOCK)
            LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
            LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sy.areaid
            LEFT JOIN (SELECT * FROM dbo.sysConfig c WITH(NOLOCK) WHERE c.code IN (36, 37)) sys ON sys.value = so.type_
            AND (ISNULL(sys.authId, 0) = 0 OR sys.authId = a.authorizeid OR (ISNULL(sys.areaids, '') &lt;> '' AND CHARINDEX(',' + CAST(a.id AS NVARCHAR(10)) + ',', ',' + sys.areaids + ',') > 0))
        WHERE sy.sub_id in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        <include refid="cashierThreePartiesExists"/>
        ORDER BY sy.id DESC
    </select>

    <select id="isNationalSubsidy" resultType="java.lang.Integer">
        select count(1) FROM subFlagRecord sr WITH(NOLOCK)
        where sub_id = #{subId} and sr.flagType = 7
    </select>

    <select id="getTagStatistics" resultType="com.jiuji.oa.oacore.oaorder.po.ProductMark">
        SELECT
            id,
            pro_mark AS proMark,
            level,
            parent_id AS parentId,
            path
        FROM
            t_product_mark WITH(NOLOCK)
        ORDER BY
            sort;
    </select>
    <select id="nationalSubShouyinOther"
            resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT
            MAX(so.num) AS subsidySeqNum,
            s.sub_id AS orderNo
        FROM
            shouyin_other  so
                INNER JOIN
            shouying s ON so.shouyinid = s.id
                INNER JOIN (
                SELECT DISTINCT
                    sc.value
                FROM
                    payment_guobu_config c WITH(NOLOCK)
    INNER JOIN
        sysConfig sc WITH(NOLOCK) ON c.payment_link_id = sc.id
                WHERE
                    c.is_del = 0
                  AND c.payment_type = 1
            ) config ON so.type_ = config.value
        WHERE
            s.shouying_type IN ('订金', '交易')
          AND s.sub_id IN
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        GROUP BY
            s.sub_id;
    </select>

    <select id="getEfficiencyByTGovernmentCategory" resultType="com.jiuji.oa.oacore.oaorder.vo.res.EfficiencyRes">
        select id as [value], name as label
        from t_government_category with(nolock)
        where type = 1 and del_flag = 0 and xtenant = #{xtenant}
        ORDER BY rank,id
    </select>

    <select id="getEfficiencyByTaxCodeOldNew" resultType="com.jiuji.oa.oacore.oaorder.vo.res.EfficiencyRes">
        SELECT DISTINCT efficiency_level as label  FROM tax_code_old_new with(nolock)
    </select>

    <select id="getEfficiencyByBasketExtend" resultType="com.jiuji.oa.oacore.oaorder.vo.res.EfficiencyRes">
        SELECT DISTINCT efficiency_level as label FROM basket_extend with(nolock)
    </select>


    <select id="getIdCard" resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT sa.sub_id as orderNo, sa.invoice_id_card as idCard
        FROM SubAddress sa with(nolock)
         where sa.sub_id in
        <foreach collection="subIdList" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="getSubAddress" resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes">
        SELECT sa.sub_id as orderNo, sa.invoice_id_card as idCard, sa.cityid as cityId, sa.Address as address
        FROM SubAddress sa with(nolock)
        where sa.sub_id in
        <foreach collection="subIdList" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>

    <select id="getEfficiencyCode" resultType="com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyEfficiencyCode">
        SELECT sy.sub_id, so.efficiencyCode
        FROM shouying sy WITH(NOLOCK)
        LEFT JOIN shouyin_other so ON so.shouyinid = sy.id
        where sy.sub_id in
        <foreach collection="subIdList" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </select>
</mapper>
