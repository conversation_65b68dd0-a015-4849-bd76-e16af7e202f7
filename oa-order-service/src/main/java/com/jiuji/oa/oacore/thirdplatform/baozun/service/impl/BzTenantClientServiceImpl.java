package com.jiuji.oa.oacore.thirdplatform.baozun.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.LineHandler;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ch999.common.util.utils.Exceptions;
import com.google.common.collect.Sets;
import com.jcraft.jsch.SftpException;
import com.jiuji.oa.baozun.common.exception.BaoZunApiException;
import com.jiuji.oa.baozun.common.util.AesEncrypt;
import com.jiuji.oa.baozun.common.util.JsonUtil;
import com.jiuji.oa.baozun.common.util.MD5Encrypt;
import com.jiuji.oa.baozun.common.util.SignUtil;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.oacore.apollo.ApolloEntity;
import com.jiuji.oa.oacore.baozun.cloud.BaoZunCloud;
import com.jiuji.oa.oacore.baozun.vo.req.InvoiceQRcodeUrlVo;
import com.jiuji.oa.oacore.baozun.vo.res.QRcodeUrlResponse;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.config.rabbitmq.BaozunMqReceiver;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.EncodeUtils;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.oa.oacore.csharp.cloud.CsharpOaWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.GenerateMkcPurcharseReq;
import com.jiuji.oa.oacore.csharp.vo.req.MkcdhUpV2Req;
import com.jiuji.oa.oacore.csharp.vo.req.UserReq;
import com.jiuji.oa.oacore.goldseed.po.Ok3wQudao;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.goldseed.service.Ok3wQudaoService;
import com.jiuji.oa.oacore.oaorder.client.vo.Result;
import com.jiuji.oa.oacore.oaorder.dao.QudaoMapper;
import com.jiuji.oa.oacore.oaorder.document.MkcLogsNewDocument;
import com.jiuji.oa.oacore.oaorder.enums.KcCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.oaorder.vo.req.MkcCaiGouSubCommentAddReq;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.a30.A30RequestBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.a30.A30SalesDetailBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.a30.A30SalesDetailSnInfoBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.a30.A30ValueAddedServiceBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.eapi.M09ReqBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.BzResultCode;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.ChildMerchantNameEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.TransactionTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantClientMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSubMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.BaozunE01OrderTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.BaozunE01StatusEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.InbeihuoEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.util.BaoZunHttpClient;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.AppInfoDTO;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.StockingCheckSaveDTO;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.req.BzCloudStoreMqReq;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.req.MemberReq;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/9/6 19:25
 */
@Service
@Slf4j
public class BzTenantClientServiceImpl implements BzTenantClientService {

    private static final Set<Integer> PJTYPE_ONE_LIST = new HashSet<>(Arrays.asList(217, 464, 218, 295, 385));
    private static final Set<Integer> PJTYPE_TWO_LIST = new HashSet<>(Arrays.asList(219, 465, 220, 662, 386));
    private static final Integer PJTYPE_ONE = 1;
    private static final Integer PJTYPE_TWO = 2;
    private static final Integer BAOZUN_FLOW_E02_LOG_TYPE = 9;
    private static final String USER_NAME = "宝尊";
    private final static Integer MAX_TRY_COUNT = 3;
    @Autowired
    private MessagePushCloud messagePushCloud;
    @Autowired
    private BzTenantClientMapper bzTenantClientMapper;
    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private BzTenantSalesOrderService salesOrderService;
    @Resource
    private BzTenantSalesDetailService salesDetailService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private BzTenantAreaService bzTenantAreaService;

    @Resource
    private BzTenantService bzTenantService;

    @Resource
    private ProductMkcService productMkcService;

    @Resource
    private QudaoMapper qudaoMapper;

    @Resource
    private Ok3wQudaoService ok3wQudaoService;

    @Resource
    private CaigouSubService caigouSubService;

    @Resource
    private CaigouBasketService caigouBasketService;

    @Resource
    private MkcCaiGouSubService mkcCaigouSubService;

    @Resource
    private MkcCaiGouBasketService mkcCaiGouBasketService;

    @Resource
    private BzTenantVariantsService bzTenantVariantsService;

    @Resource
    private CaigouSubCommentService caigouSubCommentService;

    @Resource
    private MkcCaigouCommentService mkcCaigouCommentService;

    @Resource
    private MkcLogsNewService mkcLogsNewService;

    @Resource
    private IBaozunTenantPurchaseDetailService bzTenantPurchaseDetailService;
    @Resource
    private IBaozunTenantPurchaseOrderService bzTenantPurchaseOrderService;

    @Resource
    private IBaoZunFlowLogService bzFlowLogService;
    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "oaAsyncRabbitTempe")
    private RabbitTemplate oaAsyncRabbitTempe;
    @Autowired
    private BzTenantSalesOrderService bzTenantSalesOrderService;

    private static final String BEIHUO_CHECK = "/oaApi.svc/rest/BaoZunDeleteProduct";

    private static final String E02_URL = "https://api-wms2erp.baozun.com/wmserp";

    private static final String STOCK_ = "stock_";

    private static final String BEIHUO_CHECK_URL = "/kcApi/stockingCheckSave";
    @Autowired
    private MemberClient memberClient;

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<DecryptBodyBO> getTenantApp(String sourceApp){
        //根据appKey 获取 secret
        DecryptBodyBO tenantApp = bzTenantClientMapper.getTenantApp(sourceApp);
        if(tenantApp == null || StrUtil.isBlank(tenantApp.getAppSecret())){
            return R.error(StrFormatter.format("获取不到app:{}的密钥!",sourceApp));
        }
        return R.success(tenantApp);
    }

    /**
     * 验证签名并获取解码内容
     * @param body 加密内容
     * @param sign 签名
     * @param version 版本号
     * @param sourceApp appKey
     * @param interfaceType 接口类型
     * @param requestTime 请求时间
     * @param methodName 方法名称
     * @return
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public BzResult<DecryptBodyBO> getDecryptBody(String body, String sign, String version, String sourceApp, Integer interfaceType, String requestTime, String methodName) {

        //根据appKey 获取 secret
        R<DecryptBodyBO> tenantAppR = getTenantApp(sourceApp);
        if(!tenantAppR.isSuccess()){
            return BzResult.error(BzResultCode.INVALID_REQUEST_PARAMETER_1007,tenantAppR.getUserMsg());
        }
        DecryptBodyBO tenantApp = tenantAppR.getData();
        boolean checkSign = SignUtil.checkSign(sign, version, sourceApp, interfaceType,methodName, requestTime, tenantApp.getAppSecret(), body);
        if(checkSign){
            try {
                String decryptBody = AesEncrypt.aesDecrypt(body, MD5Encrypt.encryptMD5(tenantApp.getAppSecret()),"UTF-8");
                tenantApp.setDecryptBody(decryptBody);
                return BzResult.success(tenantApp);
            } catch (Exception e) {
                StaticLog.error(e,"{}方法内容解密异常",methodName);
                return BzResult.error(BzResultCode.DOWNSSTREAM_EXCEPTION_9006, StrFormatter.format("{}方法解密异常",methodName));
            }
        }else{
            return BzResult.error(BzResultCode.INVALID_SIGNATURE_1002,StrFormatter.format("{}方法签名验证未通过!",methodName));
        }
    }

    /**
     * 同步宝尊商品信息
     * @param m07RequestBody 宝尊传递商品信息
     * @param decryptBody
     * @return
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public BzResult<Object> syncProductInfo(M07RequestBodyBO m07RequestBody, DecryptBodyBO decryptBody) {
        //校验并预处理同步数据
        BzResult<Object> checkResult = checkAndPreDealProductInfo(m07RequestBody,decryptBody);
        if (Boolean.FALSE.equals(checkResult.getSuccess())){
            return checkResult;
        }
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
            saveMaster(m07RequestBody, decryptBody);
            saveVariants(m07RequestBody, decryptBody);
        }).commit();
        return BzResult.success(null);
    }

    /**
     * 库存冻结和释放
     * @param s47RequestBody 请求体
     * @param decryptBody 解码后的对象
     * @return
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{s47RequestBody.inventoryFrezzingAndRelease[0].orderCode}")
    public BzOrderResult inventoryFrezzingAndRelease(S47RequestBodyBO s47RequestBody, DecryptBodyBO decryptBody) {
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingAndReleases = s47RequestBody.getInventoryFrezzingAndRelease();
        //预处理库存冻结和释放的数据
        prevHandleS47Data(frezzingAndReleases);
        //按订单号进行分组
        Map<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>> orderGoodMap = frezzingAndReleases.stream()
                .collect(Collectors.groupingBy(S47RequestBodyBO.InventoryFrezzingAndReleaseBO::getOrderCode));
        Set<String> allOrderIds = orderGoodMap.keySet();
        //挂接本地配置信息 areaId user_id ppid subId 入库前先挂接本地信息
        concatLocalInfo(frezzingAndReleases,allOrderIds,decryptBody.getTenantId());
        //判断订单表中是否存在数据,不存在进行插入,没有锁定之前允许更新
        insertOrUpdateOrders(orderGoodMap,decryptBody.getTenantId());
        insertOrUpdateOrderDetails(frezzingAndReleases,allOrderIds);

        //获取inwcf地址
        Optional<String> wcfOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData).filter(StrUtil::isNotBlank);
        if(!wcfOpt.isPresent()){
            return BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001,"wcf地址为空,无法占用释放库存!");
        }
        //记录订单需要更新的信息
        List<S47RequestBodyBO.OrderSubIdBO> orderSubIdList = new LinkedList<>();
        for (Map.Entry<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>> entry : orderGoodMap.entrySet()) {
            invokeFrezzingAndRelease(orderSubIdList, wcfOpt.get(), entry);
        }
        //保存备注和错误信息
        List<S47RequestBodyBO.OrderSubIdBO> updateOrderMsgMarks = orderSubIdList.stream()
                .filter(os -> StrUtil.isNotBlank(os.getRemark()) || StrUtil.isNotBlank(os.getMessage()))
                .collect(Collectors.toList());
        if(!updateOrderMsgMarks.isEmpty()){
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateOrderMsg(updateOrderMsgMarks)).commit();
        }

        return handleBzOrderResult(orderSubIdList);
    }


    private static Integer getPjType(String cidFamilyStr) {
        if (StringUtils.isEmpty(cidFamilyStr)) {
            return 0;
        }
        String[] cids = cidFamilyStr.split(",");
        for (String cid : cids) {
            if (StringUtils.isEmpty(cid)) {
                continue;
            }
            Integer cidFamily = Integer.valueOf(cid);
            if (PJTYPE_ONE_LIST.contains(cidFamily)) {
                return PJTYPE_ONE;
            } else if (PJTYPE_TWO_LIST.contains(cidFamily)) {
                return PJTYPE_TWO;
            } else {
                return 0;
            }
        }
        return 0;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public BzOrderResult generateCaigouSub(E01RequestBodyBO e01RequestBodyBO, DecryptBodyBO decryptBody) {
        String firstInboundNo = e01RequestBodyBO.getInboundOrder().stream().findFirst().map(E01RequestBodyBO.InboundOrder::getInboundNo).orElse(null);
        RLock lock = redissonClient.getLock(StrUtil.format("BzTenantClientService:generateCaigouSub:{}", firstInboundNo));
        if (!lock.tryLock()) {
            BzOrderResult error = BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001, "操作太频繁, 请稍后再试");
            error.setResult(error.getResponse());
            return error;
        }
        try {
            return invokeGenerateCaigouSub(e01RequestBodyBO, decryptBody);
        } finally {
            lock.unlock();
        }
    }

    private BzOrderResult invokeGenerateCaigouSub(E01RequestBodyBO e01RequestBodyBO, DecryptBodyBO decryptBody) {
        if (Objects.isNull(decryptBody) || Objects.isNull(decryptBody.getTenantId())) {
            BzOrderResult error = BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001, "商户id为空");
            error.setResult(error.getResponse());
            return error;
        }
        Integer tenantId = decryptBody.getTenantId();
        Optional<BzCloudStoreMqReq> mqReqOpt = Optional.ofNullable(e01RequestBodyBO).map(E01RequestBodyBO::getMqReq);
        List<E01RequestBodyBO.InboundOrder> inboundOrderList = e01RequestBodyBO.getInboundOrder();
        List<Runnable> mqFunList = new LinkedList<>();
        for (E01RequestBodyBO.InboundOrder inboundOrder : inboundOrderList) {
            AtomicReference<BzOrderResult> errorRef = new AtomicReference<>();
            MultipleTransaction transaction = MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                if (Objects.isNull(inboundOrder)) {
                    BzOrderResult error = BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001, "采购信息为空");
                    error.setResult(error.getResponse());
                    errorRef.set(error);
                    return;
                }

                String inboundNo = inboundOrder.getInboundNo();
                String platformNo = inboundOrder.getPlatformNo();
                BaozunTenantPurchaseOrder baozunTenantPurchaseOrder = bzTenantPurchaseOrderService.lambdaQuery()
                        .eq(BaozunTenantPurchaseOrder::getInboundNo, inboundNo)
                        .list().stream().findFirst().orElse(null);
                if (Objects.nonNull(baozunTenantPurchaseOrder)) {
                    log.warn("采购单已经存在，入库单号:{}", inboundNo);
                    return;
                }


                String whCode = inboundOrder.getWhCode();
                boolean dcFlag = whCode.contains(StringPool.DASH);
                Integer areaId = this.computeAreaId(whCode);
                if (Objects.isNull(areaId)) {
                    BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "采购地区匹配错误：" + whCode);
                    error.setResult(error.getResponse());
                    errorRef.set(error);
                    return;
                }
                BzTenant bzTenant = bzTenantService.lambdaQuery().eq(BzTenant::getId, tenantId)
                        .eq(BzTenant::getIsEnable, 1).list().stream().findFirst().orElse(null);
                if (Objects.isNull(bzTenant)) {
                    BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "平台匹配错误：" + tenantId);
                    error.setResult(error.getResponse());
                    errorRef.set(error);
                    return;
                }

                if (StringUtils.isEmpty(bzTenant.getNppChannel())) {
                    BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "本地配置npp渠道为空：" + bzTenant.getTenantName());
                    error.setResult(error.getResponse());
                    errorRef.set(error);
                    return;
                }
                Map<String, BzTenant.ChannelInfo> channelInfoMap = BzTenantClientService.getChannelInfoMap(bzTenant);
                BzTenant.ChannelInfo channelInfo = inboundOrder.getChannelInfo();
                String bzHqId = BzTenantClientService.getBzHqId(inboundOrder.getFromLocation());
                if(channelInfo == null){
                    channelInfo = channelInfoMap.get(bzHqId);
                    String cloudChannelKey = StrUtil.format("cloud_{}", bzHqId);
                    boolean isCloudChannel = e01RequestBodyBO.wasFromCloudStore()
                            && mqReqOpt
                            .filter(mqReq -> BzCloudStoreMqReq.OrderTypeEnum.SALES_ORDER.getCode().equals(mqReq.getOrderType()))
                            .isPresent()
                            && channelInfoMap.get(cloudChannelKey) != null;
                    if(isCloudChannel){
                        //修改为云仓特定的渠道
                        channelInfo = channelInfoMap.get(cloudChannelKey);
                    }
                }

                if (Objects.isNull(channelInfo)) {
                    BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "本地没有配置匹配的渠道：" + bzHqId);
                    error.setResult(error.getResponse());
                    errorRef.set(error);
                    return;
                }

                String title;
                if(StrUtil.isNotBlank(e01RequestBodyBO.getTitleFormat())){
                    title = StrUtil.format(e01RequestBodyBO.getTitleFormat(), BeanMap.create(inboundOrder));
                }else{
                    title = platformNo;
                }


                MkcCaiGouSub mkcCaiGouSub = new MkcCaiGouSub();
                List<MkcCaiGouBasket> mkcCaiGouBasketList = new ArrayList<>();
                mkcCaiGouSub.setBasketList(mkcCaiGouBasketList);

                CaigouSub caigouSub = new CaigouSub();
                List<CaigouBasket> caigouBasketList = new ArrayList<>();
                caigouSub.setBasketList(caigouBasketList);
                HashMap<Integer, String> caigouBasketMap = new HashMap<>();
                HashMap<Integer, String> mkcCaigouBasketMap = new HashMap<>();

                List<E01RequestBodyBO.InboundOrderLine> inboundOrderLine = inboundOrder
                        .getInboundOrderLines().getInboundOrderLine();

                for (E01RequestBodyBO.InboundOrderLine orderLine : inboundOrderLine) {
                    String lineNo = orderLine.getLineNo();

                    Integer ppriceid;
                    String upc = orderLine.getUpc();
                    int qty = orderLine.getQty();
                    Integer status = orderLine.getStatus();
                    BzTenantVariants bzTenantVariants = bzTenantVariantsService.lambdaQuery()
                            .eq(BzTenantVariants::getFkTenantId, bzTenant.getId())
                            .eq(BzTenantVariants::getBrandSkuCode, upc)
                            .eq(BzTenantVariants::getIsEnabled, 1)
                            .list().stream().findFirst().orElse(null);

                    if (Objects.isNull(bzTenantVariants)) {
                        BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "商品匹配错误：" + upc);
                        error.setResult(error.getResponse());
                        errorRef.set(error);
                        return;
                    }
                    BigDecimal nppPrice;
                    if(orderLine.getUnitPrice() != null){
                        nppPrice = orderLine.getUnitPrice();
                    }else{
                        nppPrice = e01RequestBodyBO.wasFromCloudStore() ? bzTenantVariants.getEcppPrice() : bzTenantVariants.getNppPrice();
                    }

                    if (Objects.isNull(nppPrice)) {
                        BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR,
                                StrUtil.format("{}为空：{}", e01RequestBodyBO.wasFromCloudStore() ? "ecppPrice": "NppPrice", upc));
                        error.setResult(error.getResponse());
                        errorRef.set(error);
                        return;
                    }
                    if (Objects.isNull(bzTenantVariants.getPpriceid())) {
                        BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "ppriceid为空：" + upc);
                        error.setResult(error.getResponse());
                        errorRef.set(error);
                        return;
                    }
                    ppriceid = bzTenantVariants.getPpriceid();

                    Productinfo productinfo = productinfoService.getProductinfoByPpid(Collections.singletonList(ppriceid))
                            .stream().findFirst().orElse(null);
                    if (Objects.isNull(productinfo)) {
                        BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "productinfo为空：" + upc);
                        error.setResult(error.getResponse());
                        errorRef.set(error);
                        return;
                    }
                    Boolean ismobile1 = productinfo.getIsmobile1();
                    if (ismobile1) {
                        if(orderLine.getSnLines() == null || CollUtil.isEmpty(orderLine.getSnLines().getSn())){
                            BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR,
                                    StrUtil.format("upc:{}本地ppid[{}]为大件, 但是串号信息为空", upc, ppriceid));
                            error.setResult(error.getResponse());
                            errorRef.set(error);
                            return;
                        }
                        Integer id = qudaoMapper.getIsCooperationMobile(Convert.toStr(channelInfo.getBigChannelId()));
                        if (Objects.isNull(id)) {
                            BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "大件npp渠道不在合作中：" + channelInfo.getBigChannelId());
                            error.setResult(error.getResponse());
                            errorRef.set(error);
                            return;
                        }
                        boolean mouldFlag = false;
                        if (BaozunE01StatusEnum.THREE.getCode().equals(status)) {
                            mouldFlag = true;
                        }

                        Ok3wQudao ok3wQudao = ok3wQudaoService.lambdaQuery().eq(Ok3wQudao::getId, Convert.toInt(channelInfo.getBigChannelId())).one();
                        if (Objects.isNull(ok3wQudao)) {
                            BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "找不到大件npp渠道：" + channelInfo.getBigChannelId());
                            error.setResult(error.getResponse());
                            errorRef.set(error);
                            return;
                        }

                        for (int i = 0; i < qty; i++) {
                            ProductMkc productMkc = new ProductMkc();
                            productMkc.setPpriceid(ppriceid)
                                    .setInbeihuo(InbeihuoEnum.GOT.getCode())
                                    .setDtime(LocalDateTime.now())
                                    .setInbeihuodate(LocalDateTime.now())
                                    .setInbeihuoprice(nppPrice)
                                    .setInuser(USER_NAME)
                                    .setKcCheck(KcCheckEnum.KC_CHECK_SUBMITTED.getCode())
                                    .setMouldFlag(mouldFlag)
                                    .setIsTax(false)
                                    .setInprice(nppPrice)
                                    .setInsourceid(ok3wQudao.getInsourceid())
                                    .setInsourceid2(Convert.toInt(channelInfo.getBigChannelId()))
                                    .setAreaid(areaId)
                                    .setOrigareaid(areaId);

                            MkcCaiGouBasket mkcCaiGouBasket = new MkcCaiGouBasket();
                            mkcCaiGouBasket.setProductMkc(productMkc);
                            mkcCaiGouBasket.setIsDel(Boolean.FALSE);
                            mkcCaiGouBasket.setStatus(status);
                            mkcCaiGouBasketList.add(mkcCaiGouBasket);
                            mkcCaigouBasketMap.put(mkcCaiGouBasket.hashCode(), lineNo);
                            //云仓采购通知自动入库
                            if (e01RequestBodyBO.wasFromCloudStore()) {
                                BzCloudStoreMqReq mqReq = BzCloudStoreMqReq.builder().transactionNumber(inboundNo)
                                        .tenantId(tenantId)
                                        .orderType(mqReqOpt.map(BzCloudStoreMqReq::getOrderType).orElse(BzCloudStoreMqReq.OrderTypeEnum.SALES_ORDER.getCode()))
                                        .largeInReq(BzCloudStoreMqReq.LargeInKuCunReq.builder()
                                                .mkcInReq(MkcdhUpV2Req.builder().imei(orderLine.getSnLines().getSn().get(i)).build())
                                                .areaId(areaId)
                                                .build())
                                        .operationType(BzCloudStoreMqReq.OperationTypeEnum.LARGE_INBOUND.getCode())
                                        .build();
                                mqFunList.add(() -> {
                                    mqReq.getLargeInReq().getMkcInReq().setMkcId(productMkc.getId());
                                    oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_CLOUD_STORE, JSON.toJSONString(mqReq));
                                });
                            }
                        }
                    } else {
                        Integer id = qudaoMapper.getIsCooperationAccessory(Convert.toStr(channelInfo.getSmallChannelId()));
                        if (Objects.isNull(id)) {
                            BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, "小件npp渠道不在合作中：" + channelInfo.getSmallChannelId());
                            error.setResult(error.getResponse());
                            errorRef.set(error);
                            return;
                        }
                        CaigouBasket caigouBasket = new CaigouBasket();
                        caigouBasket.setPpriceid(ppriceid);
                        caigouBasket.setLcount(qty);
                        caigouBasket.setInprice(nppPrice);
                        BaozunE01StatusEnum statusEnum = BaozunE01StatusEnum.getByCode(status);
                        if (Objects.nonNull(statusEnum)) {
                            caigouBasket.setComment(statusEnum.getMessage());
                        }
                        caigouBasketList.add(caigouBasket);
                        caigouBasketMap.put(caigouBasket.hashCode(), lineNo);
                    }
                }

                BaozunTenantPurchaseOrder bzOrder = new BaozunTenantPurchaseOrder();
                BeanUtils.copyProperties(inboundOrder, bzOrder);
                bzOrder.setWhcode(inboundOrder.getWhCode());
                bzOrder.setFkTenantId(tenantId);
                bzTenantPurchaseOrderService.save(bzOrder);

                E01RequestBodyBO.InboundOrderLines inboundOrderLines = inboundOrder.getInboundOrderLines();
                List<E01RequestBodyBO.InboundOrderLine> inboundOrderLineList = inboundOrderLines.getInboundOrderLine();

                Map<String, BaozunTenantPurchaseDetail> lineNoToDetailMap = new HashMap<>();
                for (E01RequestBodyBO.InboundOrderLine temp : inboundOrderLineList) {
                    BaozunTenantPurchaseDetail detail = new BaozunTenantPurchaseDetail();
                    BeanUtils.copyProperties(temp, detail);
                    E01RequestBodyBO.SnLines snLines = temp.getSnLines();
                    if (Objects.nonNull(snLines)) {
                        List<String> sn = snLines.getSn();
                        detail.setSn(StringUtils.join(sn, StringPool.COMMA));
                    }
                    detail.setBaozunPurchaseId(bzOrder.getId());
                    bzTenantPurchaseDetailService.save(detail);
                    lineNoToDetailMap.put(detail.getLineNo(), detail);
                }
                String comment = "宝尊中台采购，系统自动生成采购单";

                //大件
                if (CollectionUtils.isNotEmpty(mkcCaiGouBasketList) && !e01RequestBodyBO.wasFromCloudStore()) {
                    mkcCaiGouSub.setAreaId(areaId);
                    mkcCaiGouSub.setInuser(USER_NAME);
                    mkcCaiGouSub.setIndate(LocalDateTime.now());
                    mkcCaiGouSub.setTitle(title);
                    mkcCaiGouSub.setInsourceId(Convert.toInt(channelInfo.getBigChannelId()));
                    mkcCaigouSubService.save(mkcCaiGouSub);
                    MkcCaiGouSubCommentAddReq req = new MkcCaiGouSubCommentAddReq();
                    req.setSubId(mkcCaiGouSub.getId());
                    req.setInuser(USER_NAME);
                    req.setComment(comment);
                    mkcCaigouCommentService.saveMkcComment(req);

                    Integer subId = mkcCaiGouSub.getId();

                    StockingCheckSaveDTO dto = new StockingCheckSaveDTO();
                    List<StockingCheckSaveDTO.StockingCheckSaveItem> items = new ArrayList<>();
                    dto.setItems(items);

                    Map<String, List<Integer>> lineNotoProductMkcIdListMap = new HashMap<>();

                    for (MkcCaiGouBasket mkcCaiGouBasket : mkcCaiGouBasketList) {

                        String lineNo = mkcCaigouBasketMap.get(mkcCaiGouBasket.hashCode());

                        List<Integer> productMkcIdList = lineNotoProductMkcIdListMap.get(lineNo);
                        if (CollectionUtils.isEmpty(productMkcIdList)) {
                            productMkcIdList = new ArrayList<>();
                            lineNotoProductMkcIdListMap.put(lineNo, productMkcIdList);
                        }

                        ProductMkc productMkc = mkcCaiGouBasket.getProductMkc();
                        productMkcService.save(productMkc);
                        Integer productMkcId = productMkc.getId();
                        productMkcIdList.add(productMkcId);
                        mkcCaiGouBasket.setMkcId(productMkcId);
                        mkcCaiGouBasket.setSubId(subId);
                        mkcCaiGouBasketService.save(mkcCaiGouBasket);
                        //备货核对
                        StockingCheckSaveDTO.StockingCheckSaveItem item = new StockingCheckSaveDTO.StockingCheckSaveItem();
                        item.setAreaId(areaId);
                        item.setPpid(productMkc.getPpriceid());
                        item.setSupplierId(productMkc.getInsourceid2());
                        item.setPurchaseOrderNumber(subId);
                        items.add(item);

                        List<String> mkcCommentList = new ArrayList<>();
                        Integer status = mkcCaiGouBasket.getStatus();
                        BaozunE01StatusEnum statusEnum = BaozunE01StatusEnum.getByCode(status);
                        String message = "";
                        if (Objects.nonNull(statusEnum)) {
                            message = statusEnum.getMessage();
                        }

                        // 保存库存日志
                        MkcLogsNewDocument mkcLogsNewDocument = new MkcLogsNewDocument();
                        mkcLogsNewDocument.setMkcId(Long.valueOf(productMkcId));
                        MkcLogsNewDocument.Conts conts = new MkcLogsNewDocument.Conts();
                        conts.setDTime(LocalDateTime.now()).setComment("宝尊状态:" + message).setType(true).setInUser(USER_NAME);
                        MkcLogsNewDocument.Conts conts2 = new MkcLogsNewDocument.Conts();
                        conts2.setDTime(LocalDateTime.now()).setComment("渠道、成本已录，转备货核对 ").setType(true).setInUser(USER_NAME);
                        MkcLogsNewDocument.Conts conts3 = new MkcLogsNewDocument.Conts();
                        conts3.setDTime(LocalDateTime.now()).setComment("生成采购单，单号：<a target='_blank' href='/staticpc/#/logistics/productMkc/mkcCaiGouDetail?sub_id="
                                + subId + "'> " + subId + " </a>").setType(true).setInUser(USER_NAME);
                        List<MkcLogsNewDocument.Conts> consList = new ArrayList<>();
                        consList.add(conts);
                        consList.add(conts2);
                        consList.add(conts3);
                        mkcLogsNewDocument.setCons(consList);
                        mkcLogsNewService.saveMkcLogsNew(mkcLogsNewDocument);

                        LambdaUpdateWrapper<BaozunTenantPurchaseDetail> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(BaozunTenantPurchaseDetail::getLineNo, lineNo);
                        updateWrapper.eq(BaozunTenantPurchaseDetail::getBaozunPurchaseId, bzOrder.getId());
                        updateWrapper.set(BaozunTenantPurchaseDetail::getSubId, subId);
                        updateWrapper.set(BaozunTenantPurchaseDetail::getBussinessId, StringUtils.join(productMkcIdList, StringPool.COMMA));
                        updateWrapper.set(BaozunTenantPurchaseDetail::getPpriceid, productMkc.getPpriceid());
                        updateWrapper.set(BaozunTenantPurchaseDetail::getOrderType, BaozunE01OrderTypeEnum.TWO.getCode());
                        bzTenantPurchaseDetailService.update(updateWrapper);
                    }
                    //备货核对
                    mqFunList.add(() -> this.beihuoCheck(dto));
                }
                //云仓大件
                if (CollectionUtils.isNotEmpty(mkcCaiGouBasketList) && e01RequestBodyBO.wasFromCloudStore()) {
                    UserReq.UserReqBuilder<GenerateMkcPurcharseReq> userReqBuilder = UserReq.builder();
                    Ch999User ch999User = SpringUtil.getBean(Ch999UserService.class).lambdaQuery()
                            .eq(Ch999User::getCh999Name, BzTenantCloudStoreService.CLOUD_STORE_NAME).one();
                    if (ch999User == null) {
                        BzOrderResult error = BzOrderResult.error(BzTenantCloudStoreService.RETRY_CODE_STR, StrUtil.format("[{}]OA用户不存在", BzTenantCloudStoreService.CLOUD_STORE_NAME));
                        errorRef.set(error);
                        return;
                    }
                    // 获取渠道信息
                    Optional<Ok3wQudao> ok3wQudaoOpt = SpringUtil.getBean(Ok3wQudaoService.class).lambdaQuery()
                            .eq(Ok3wQudao::getId, channelInfo.getBigChannelId()).list()
                            .stream().findFirst();
                    //通过门店id 获取门店编码
                    AreaInfo areaInfo = SpringUtil.getBean(AreaInfoService.class).getAreaInfoById(areaId);
                    userReqBuilder.user(ch999User.getCh999Id()).pwd(ch999User.getPwd()).area(areaInfo.getArea())
                            .model(GenerateMkcPurcharseReq.builder().areaId(areaId).title(title)
                                    .insourceId(ok3wQudaoOpt.map(Ok3wQudao::getInsourceid).map(Convert::toStr).orElse(null))
                                    .insourceId2(Convert.toStr(channelInfo.getBigChannelId()))
                                    .needStockingCheck(1)
                                    .isProductMould(true)
                                    .lineItems(mkcCaiGouBasketList
                                            .stream().map(MkcCaiGouBasket::getProductMkc)
                                            .map(mkc -> GenerateMkcPurcharseReq.LineItem.builder().ppid(mkc.getPpriceid())
                                                    .count(1).inbeihuoPrice(mkc.getInbeihuoprice()).isMould(Convert.toInt(mkc.getMouldFlag()))
                                                    .build()).collect(Collectors.toList()))
                                    .build());
                    R<List<Dict>> genR = SpringUtil.getBean(CsharpOaWcfCloud.class).generateMkcPurcharse(userReqBuilder.build());
                    log.warn("大件采购单生成结果: {}", JSON.toJSONString(genR));
                    if (!genR.isSuccess()) {
                        BzOrderResult error = BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001, StrUtil.format("大件采购单生成失败, 原因: {}", genR.getUserMsg()));
                        errorRef.set(error);
                        return;
                    }
                    //给对应的mkc赋值mkcId
                    List<Integer> subIds = genR.getData().stream().map(dict -> dict.getInt("subId")).collect(Collectors.toList());
                    List<MkcCaiGouBasket> newMkcBaskets = SpringUtil.getBean(MkcCaiGouBasketService.class).lambdaQuery().in(MkcCaiGouBasket::getSubId, subIds)
                            .orderByAsc(MkcCaiGouBasket::getId).list();
                    //按行号来进行分组处理
                    Map<String, List<MkcCaiGouBasket>> groupByLineNo = new HashMap<>();
                    for (int i = 0; i < newMkcBaskets.size(); i++) {
                        MkcCaiGouBasket newMkcBasket = newMkcBaskets.get(i);
                        Integer mkcId = newMkcBasket.getMkcId();
                        MkcCaiGouBasket mkcCaiGouBasket = mkcCaiGouBasketList.get(i);
                        String lineNo = mkcCaigouBasketMap.get(mkcCaiGouBasket.hashCode());
                        mkcCaiGouBasket.setMkcId(mkcId);
                        ProductMkc productMkc = mkcCaiGouBasket.getProductMkc();
                        productMkc.setId(mkcId);
                        newMkcBasket.setProductMkc(productMkc);
                        if (groupByLineNo.get(lineNo) == null) {
                            groupByLineNo.put(lineNo, new LinkedList<>());
                        }
                        groupByLineNo.get(lineNo).add(newMkcBasket);
                    }
                    //更新采购信息
                    groupByLineNo.forEach((lineNo, newBaskets) -> {
                        MkcCaiGouBasket newMkcBasket = newBaskets.get(0);
                        ProductMkc productMkc = newMkcBasket.getProductMkc();
                        // 获取对应id
                        List<Integer> detailIds = bzTenantPurchaseDetailService.lambdaQuery().eq(BaozunTenantPurchaseDetail::getLineNo, lineNo)
                                .eq(BaozunTenantPurchaseDetail::getBaozunPurchaseId, bzOrder.getId())
                                .select(BaozunTenantPurchaseDetail::getId).list().stream()
                                .map(BaozunTenantPurchaseDetail::getId)
                                .collect(Collectors.toList());
                        bzTenantPurchaseDetailService.lambdaUpdate().in(BaozunTenantPurchaseDetail::getId, detailIds)
                                .set(BaozunTenantPurchaseDetail::getSubId, newMkcBasket.getSubId())
                                .set(BaozunTenantPurchaseDetail::getPpriceid, productMkc.getPpriceid())
                                .set(BaozunTenantPurchaseDetail::getOrderType, BaozunE01OrderTypeEnum.TWO.getCode())
                                .set(BaozunTenantPurchaseDetail::getBussinessId, newBaskets.stream().map(MkcCaiGouBasket::getMkcId)
                                        .map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA)))
                                .update();
                    });


                }

                //小件
                if (CollectionUtils.isNotEmpty(caigouBasketList)) {
                    caigouSub.setAreaid(areaId);
                    caigouSub.setInuser(USER_NAME);
                    caigouSub.setDtime(LocalDateTime.now());
                    caigouSub.setTitle(title);
                    caigouSub.setInsourceid(Convert.toInt(channelInfo.getSmallChannelId()));
                    int stats = 2;
                    if (dcFlag) {
                        stats = 1;
                    }
                    caigouSub.setStats(stats);
                    caigouSub.setKinds("pj");
                    //            caigouSub.setPjType(getPjType(productInfoVo.getCidFamily()));

                    caigouSubService.save(caigouSub);
                    Integer subId = caigouSub.getId();
                    caigouSubCommentService.addCaiGouSubComment(subId, comment, USER_NAME, 0);
                    for (CaigouBasket caigouBasket : caigouBasketList) {
                        String lineNo = caigouBasketMap.get(caigouBasket.hashCode());

                        caigouBasket.setSubId(Convert.toLong(subId));
                        caigouBasketService.save(caigouBasket);

                        // 获取对应id
                        List<Integer> detailIds = bzTenantPurchaseDetailService.lambdaQuery().eq(BaozunTenantPurchaseDetail::getLineNo, lineNo)
                                .eq(BaozunTenantPurchaseDetail::getBaozunPurchaseId, bzOrder.getId())
                                .select(BaozunTenantPurchaseDetail::getId).list().stream()
                                .map(BaozunTenantPurchaseDetail::getId)
                                .collect(Collectors.toList());
                        LambdaUpdateWrapper<BaozunTenantPurchaseDetail> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.in(BaozunTenantPurchaseDetail::getId, detailIds);
                        updateWrapper.set(BaozunTenantPurchaseDetail::getSubId, subId);
                        updateWrapper.set(BaozunTenantPurchaseDetail::getBussinessId, Convert.toStr(caigouBasket.getId()));
                        updateWrapper.set(BaozunTenantPurchaseDetail::getPpriceid, caigouBasket.getPpriceid());
                        updateWrapper.set(BaozunTenantPurchaseDetail::getOrderType, BaozunE01OrderTypeEnum.ONE.getCode());
                        bzTenantPurchaseDetailService.update(updateWrapper);
                    }
                    //云仓采购通知自动入库
                    if (e01RequestBodyBO.wasFromCloudStore()) {
                        BzCloudStoreMqReq mqReq = BzCloudStoreMqReq.builder().transactionNumber(inboundNo).tenantId(tenantId).smallInReq(
                                        BzCloudStoreMqReq.SmallInKuCunReq.builder().caigouSubId(subId).build())
                                .operationType(BzCloudStoreMqReq.OperationTypeEnum.SMALL_INBOUND.getCode())
                                .orderType(mqReqOpt.map(BzCloudStoreMqReq::getOrderType).orElse(BzCloudStoreMqReq.OrderTypeEnum.SALES_ORDER.getCode()))
                                .build();
                        mqFunList.add(() -> oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_CLOUD_STORE, JSON.toJSONString(mqReq)));
                    }
                }
            });

            if(errorRef.get() != null){
                transaction.rollback();
                return errorRef.get();
            }else{
                transaction.commit();
            }
        }
        BzOrderResult success = BzOrderResult.success(new ArrayList<>());
        success.setResult(success.getResponse());
        mqFunList.forEach(Runnable::run);
        return success;
    }

    @Override
    public Integer computeAreaId(String whCode) {
        if(StrUtil.isBlank(whCode)){
            return null;
        }

        Integer areaId = null;
        if (whCode.contains(StringPool.DASH)) {
            String[] split = whCode.split(StringPool.DASH);
            if (NumberUtil.isNumber(split[1])) {
                areaId = Convert.toInt(split[1]);
            }
        }

        if(Objects.isNull(areaId)) {
            BzTenantArea bzTenantArea =  bzTenantAreaService.lambdaQuery().eq(BzTenantArea::getTenantStoreCode, whCode)
                    .eq(BzTenantArea::getIsDel, 0)
                    .eq(BzTenantArea::getIsEnable, 1)
                    .list().stream().findFirst().orElse(null);
            if (Objects.nonNull(bzTenantArea)){
                areaId = bzTenantArea.getAreaId();
            }
        }
        return areaId;
    }

    private String getTenantStoreCode(Integer areaId) {
        BzTenantArea bzTenantArea = bzTenantAreaService.lambdaQuery().eq(BzTenantArea::getAreaId, areaId)
                .eq(BzTenantArea::getIsDel, 0)
                .eq(BzTenantArea::getIsEnable, 1)
                .list().stream().findFirst().orElse(null);
        if (Objects.isNull(bzTenantArea)) {
            return Strings.EMPTY;
        }
        return bzTenantArea.getTenantStoreCode();
    }

    private void postE02(AppInfoDTO infoDTO, E02RequestBodyBO e02) {
        String result = "";
        String request = JsonUtil.object2JsonNonNull(e02);
        Integer resultFlag = null;
        try {
            result = BaoZunHttpClient.inboundConfirmPost(request, infoDTO);
            BzOrderResult bzOrderResult = JSONUtil.toBean(result, BzOrderResult.class);
            resultFlag = bzOrderResult.getResult();
        } catch (Exception e) {
            throw new BaoZunApiException("请求失败", e.getMessage());
        } finally {
            BaoZunFlowLog baoZunFlowLog = new BaoZunFlowLog();
            baoZunFlowLog.setBatchId(0L);
            baoZunFlowLog.setLogType(BAOZUN_FLOW_E02_LOG_TYPE);
            baoZunFlowLog.setRetryCount(0);
            baoZunFlowLog.setRemark("采购入库回调");
            baoZunFlowLog.setApiResponseParams(request);
            baoZunFlowLog.setReturnParams(Convert.toStr(resultFlag));
            baoZunFlowLog.setCreateTime(LocalDateTime.now());
            bzFlowLogService.saveLog(baoZunFlowLog);
            log.info("sendMsgUrl:{},result:{}", request, result);
        }
    }


    /**
     * 备货核对
     **/
    private boolean beihuoCheck(StockingCheckSaveDTO dto) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String paramter = STOCK_ + LocalDateTime.now().format(formatter);
        String token = EncodeUtils.MD5(paramter, "UTF-8");
        String json = JSONUtil.toJsonStr(dto);
        //获取inwcf地址
        Optional<String> wcfOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_WCF_HOST)).map(R::getData).filter(StrUtil::isNotBlank);
        if (!wcfOpt.isPresent()) {
            return false;
        }
        String beihuoCheckUrl = wcfOpt.get() + BEIHUO_CHECK_URL;
        log.info("调用[logistics]服务,请求方法：post,当前调用地址：{},请求参数:{}", beihuoCheckUrl, json);
        String body = HttpUtil.createPost(beihuoCheckUrl)
                .header("token", token)
                .body(json)
                .execute().body();
        log.debug("返回参数:{}", body);
        return true;
    }

    /**
     * shunfengApiServices.MD5ToBase64String
     * 加密
     *
     * @param str 需加密的字符串
     * @return string 加密后的字符
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-03
     */
    public static String shunfengMd5ToBase64String(String str) {
        try {
            return md5DigestToBase64(str);
        } catch (NoSuchAlgorithmException e) {
            log.error("WuLiuServiceImpl.shunfengMd5ToBase64String 报错={}", Exceptions.getStackTraceAsString(e), e);
            return "";
        }
    }

    /**
     * md5DigestToBase64
     *
     * @param str String
     * @return String
     * <AUTHOR> [<EMAIL>]
     * @date 2021-11-16
     */
    public static String md5DigestToBase64(String str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(md.digest(bytes));
    }


    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<String> e02(E02CallbackBodyBO bo) {
        if(CommonUtil.closeBaoZunEsApi()){
            return R.success("响应宝尊要求, 接口已停用");
        }
        E02RequestBodyBO e02 = new E02RequestBodyBO();
        e02.setSourceMarkCode("route_apple_wms");

        List<BaozunTenantPurchaseDetail> detailList = bzTenantPurchaseDetailService.lambdaQuery()
                .eq(BaozunTenantPurchaseDetail::getSubId, bo.getSubId())
                .eq(BaozunTenantPurchaseDetail::getOrderType, bo.getOrderType())
                .eq(BaozunTenantPurchaseDetail::getIsComplete, 0)
                .list();

        if (CollectionUtils.isEmpty(detailList)) {
            throw new CustomizeException(StrUtil.format("找不到该采购单: 类型:[{}], 采购单号:[{}]", bo.getOrderType(), bo.getSubId()));
        }

        detailList.forEach(x -> {
            x.setIsComplete(1);
            bzTenantPurchaseDetailService.updateById(x);
        });
        BaozunTenantPurchaseDetail detail = detailList.get(0);

        Integer baozunPurchaseId = detail.getBaozunPurchaseId();

        List<BaozunTenantPurchaseDetail> notCompleteList = bzTenantPurchaseDetailService.lambdaQuery()
                .eq(BaozunTenantPurchaseDetail::getBaozunPurchaseId, baozunPurchaseId)
                .eq(BaozunTenantPurchaseDetail::getIsComplete, 0)
                .list();

        if (CollectionUtils.isNotEmpty(notCompleteList)){
            throw new CustomizeException("有未完成的采购单");
        }


        BaozunTenantPurchaseOrder bzOrder = bzTenantPurchaseOrderService.lambdaQuery()
                .eq(BaozunTenantPurchaseOrder::getId, baozunPurchaseId).one();
        //云仓采购单不需要传e02
        if(BzTenantCloudStoreService.PO_TYPE_OA_15.equals(bzOrder.getPoType())){
            log.warn("云仓订单,不需要同步e02, 宝尊采购表主键: {}", bzOrder.getId());
            return R.success("云仓订单,不需要同步e02");
        }

        BzTenant bzTenant = bzTenantService.lambdaQuery().eq(BzTenant::getId, bzOrder.getFkTenantId())
                .eq(BzTenant::getIsEnable, 1).list().stream().findFirst().orElse(null);
        if (Objects.isNull(bzTenant)) {
            messagePushCloud.pushOaMessagePost("13685,8942,13774", "e02入库反馈"
                    + "平台匹配错误：" + bzOrder.getFkTenantId()
                    + "," + bo.getOrderType() + "," + bo.getSubId(), "");
            throw new CustomizeException("平台匹配错误：" + bzOrder.getFkTenantId());
        }

        String inboundNo = bzOrder.getInboundNo();
        E02RequestBodyBO.InboundConfirm inboundConfirm = new E02RequestBodyBO.InboundConfirm();
        e02.setInboundConfirm(inboundConfirm);
        e02.setMsgId(inboundNo);
        String dataSource = bzOrder.getDataSource();
        inboundConfirm.setUuid(bzOrder.getUuid());
        inboundConfirm.setDataSource(dataSource);
        inboundConfirm.setInboundNo(inboundNo);
        inboundConfirm.setPlatformNo(inboundNo);
        inboundConfirm.setPreOrderType("6");
        inboundConfirm.setPoType(bzOrder.getPoType());
        String hqId = bzTenant.getHqId();
        inboundConfirm.setOwner(hqId);
        inboundConfirm.setFromLocation(bzOrder.getFromLocation());
        inboundConfirm.setStoreCode(bzOrder.getWhcode());
        inboundConfirm.setWhCode(bzOrder.getWhcode());
        inboundConfirm.setToLocation(bzOrder.getWhcode());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        inboundConfirm.setInboundTime(formatter.format(now));
        E02RequestBodyBO.InboundConfirmLines inboundConfirmLines = new E02RequestBodyBO.InboundConfirmLines();
        List<E02RequestBodyBO.InboundConfirmLine> inboundConfirmLine = this.getInboundConfirmLineList(bzOrder);
        inboundConfirmLines.setInboundConfirmLine(inboundConfirmLine);
        inboundConfirm.setInboundConfirmLines(inboundConfirmLines);
        Integer qtyReceivedSum = 0;
        for (E02RequestBodyBO.InboundConfirmLine x : inboundConfirmLine) {
            for (E02RequestBodyBO.InboundInvLineConfirm y : x.getInboundInvLineConfirms().getInboundInvLineConfirm()) {
                Integer qtyReceived = y.getQtyReceived();
                qtyReceivedSum += qtyReceived;
            }
        }
        inboundConfirm.setActualQuantity(qtyReceivedSum);
        AppInfoDTO infoDTO = new AppInfoDTO();
        infoDTO.setTenantName(bzTenant.getTenantName());
        infoDTO.setAppKey(bzTenant.getAppKey());
        infoDTO.setAppSecret(bzTenant.getAppSecret());
        infoDTO.setRequestUrl(E02_URL);
        this.postE02(infoDTO,e02);
        return R.success("调用成功");
    }

    private List<E02RequestBodyBO.InboundConfirmLine> getInboundConfirmLineList(BaozunTenantPurchaseOrder bzOrder) {

        List<BaozunTenantPurchaseDetail> detailsList = bzTenantPurchaseDetailService.lambdaQuery()
                .eq(BaozunTenantPurchaseDetail::getBaozunPurchaseId, bzOrder.getId())
                .list();
        List<E02RequestBodyBO.InboundConfirmLine> inboundConfirmLine = new ArrayList<>();

        for (BaozunTenantPurchaseDetail detail : detailsList) {
            Integer orderType = detail.getOrderType();
            Integer actualQty = 0;
            String basketId = detail.getBussinessId();
            List<E02RequestBodyBO.InboundSnLineConfirm> inboundSnLineConfirm = new ArrayList<>();
            if (BaozunE01OrderTypeEnum.ONE.getCode().equals(Convert.toStr(orderType))) {
                CaigouBasket caigouBasket = caigouBasketService.getById(basketId);
                if (Objects.nonNull(caigouBasket) && Objects.nonNull(caigouBasket.getLcount())) {
                    actualQty = caigouBasket.getInputCount();
                }
            } else {
                String[] mkcIdList = basketId.split(StringPool.COMMA);
                for (String mkcId : mkcIdList) {
                    ProductMkc productMkc = productMkcService.getById(mkcId);
                    if (Objects.nonNull(productMkc) && Objects.nonNull(productMkc.getImei())) {
                        E02RequestBodyBO.InboundSnLineConfirm temppp = new E02RequestBodyBO.InboundSnLineConfirm();
                        temppp.setSn(productMkc.getImei());
                        inboundSnLineConfirm.add(temppp);
                    }
                }
                actualQty = inboundSnLineConfirm.size();
            }
            detail.setActualQty(actualQty);
            bzTenantPurchaseDetailService.updateById(detail);
            String upc = detail.getUpc();
            E02RequestBodyBO.InboundConfirmLine temp = new E02RequestBodyBO.InboundConfirmLine();
            temp.setUpc(upc);
            String lineNo = detail.getLineNo();
            temp.setLineNo(lineNo);

            E02RequestBodyBO.InboundInvLineConfirms inboundInvLineConfirms = new E02RequestBodyBO.InboundInvLineConfirms();

            List<E02RequestBodyBO.InboundInvLineConfirm> inboundInvLineConfirm = new ArrayList<>();

            E02RequestBodyBO.InboundInvLineConfirm tempp = new E02RequestBodyBO.InboundInvLineConfirm();
            tempp.setInvStatus(String.valueOf(detail.getStatus()));
            Integer qtyReceived = detail.getActualQty();
            tempp.setQtyReceived(qtyReceived);

            E02RequestBodyBO.InboundSnLineConfirms inboundSnLineConfirms = new E02RequestBodyBO.InboundSnLineConfirms();

            inboundSnLineConfirms.setInboundSnLineConfirm(inboundSnLineConfirm);

            tempp.setInboundSnLineConfirms(inboundSnLineConfirms);
            inboundInvLineConfirm.add(tempp);
            inboundInvLineConfirms.setInboundInvLineConfirm(inboundInvLineConfirm);
            temp.setInboundInvLineConfirms(inboundInvLineConfirms);
            inboundConfirmLine.add(temp);
        }

        return inboundConfirmLine;

    }


    /**
     * 预处理库存预占和释放的库存数据
     * @param frezzingAndReleases
     */
    private void prevHandleS47Data(List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingAndReleases) {
        //由于宝尊预占的时候不一定已经生成ordercode,释放的时候ordercode与数据里面的不一致,这里处理为一致
        List<String> releasePocIdList = frezzingAndReleases.stream().filter(far -> far.getQty() < 0)
                .map(S47RequestBodyBO.InventoryFrezzingAndReleaseBO::getPlatformOrderCode).distinct()
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(releasePocIdList)){
            //获取订单信息,并更新orderCode与数据库一致
            bzTenantClientMapper.existsOrderIds(releasePocIdList).stream().forEach(osi -> frezzingAndReleases.stream()
                    .filter(far -> Objects.equals(far.getPlatformOrderCode(),osi.getOrderCode()))
                    .forEach(far -> far.setOrderCode(osi.getOrderCode())));
        }
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<Object> parseA01Data() {
        //获取未解析的文件内容
        List<SftpInfoBO.FileInfo> fileInfos = bzTenantClientMapper.listNotParseFile();
        fileInfos.sort(Comparator.comparing(fi->fileNameRank(fi.getName())));
        MultiValueMap<String,String> orderMsgMap = new LinkedMultiValueMap<>(NumberConstant.SIXTEEN);
        List<SalesDetailBO> allSalesDetails = new LinkedList<>();
        for (SftpInfoBO.FileInfo fileInfo : fileInfos) {
            //文件解析后的结果对象
            try {
                List<SalesDetailBO> salesDetails = new LinkedList<>();
                List<SalesDetailBO.SalesDetailSnInfoBO> snList = new LinkedList<>();
                List<SalesDetailBO.SalesTender> stList = new LinkedList<>();
                contentToObjs(orderMsgMap, fileInfo, salesDetails, snList, stList);
                //清理无效的数据
                clearData(salesDetails, snList, stList, null);
                insertOrUpdateSaleDetails(salesDetails);
                insertOrUpdateSns(snList);
                insertOrUpdateSt(stList);
                //成功修改为已解析,单文件修改,成功一个修改一个
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->bzTenantClientMapper.updateFileParse(fileInfo)).commit();
                allSalesDetails.addAll(salesDetails);
            } catch (Exception e) {
                //文件处理异常通知
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->bzTenantClientMapper.updateFileParse(fileInfo)).commit();
                RRExceptionHandler.logError(StrUtil.format("宝尊订单文件解析{}",fileInfo.getName()),fileInfo,e,SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }
        //同步信息到订单中
        SpringUtil.getBean(BzTenantSubService.class).asyncInfoToSub(Collections.emptyList());

        BaozunMqReceiver baozunMqReceiver = SpringUtil.getBean(BaozunMqReceiver.class);
        allSalesDetails.stream().map(SalesDetailBO::getTransactionNumber).distinct()
                //解析云仓订单信息,并进行预占
                .peek(baozunMqReceiver::baozunParseA01AfterCloudStoreNoEx)
                //广播解析后的订单号
                .forEach(transactionNumber -> oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_PARSE_A01_TOPIC,"", transactionNumber));
        //解析云仓顶大并自动进行建单
        return R.success(fileInfos.stream().map(SftpInfoBO.FileInfo::getName).collect(Collectors.joining(SignConstant.BLANK)));
    }

    /**
     * 清理工单
     *
     * @param salesDetails
     * @param snList
     * @param stList
     * @param addServiceList
     */
    private void clearData(List<SalesDetailBO> salesDetails, List<SalesDetailBO.SalesDetailSnInfoBO> snList,
                           List<SalesDetailBO.SalesTender> stList, List<BzTenantSalesAddService> addServiceList) {
        snList.removeIf(sn -> {
            if(StrUtil.isBlank(sn.getSn())){
                return true;
            }
            return false;
        });
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<Object> downloadA01SalesFile() {
        //获取ftp 用户信息 9
        SftpInfoBO sftpInfo = bzTenantClientMapper.getSftpInfo();
        if(sftpInfo == null){
            return R.error("未配置ftp连接地址");
        }
        Optional<String[]> urlsOpt = Optional.ofNullable(sftpInfo).map(SftpInfoBO::getAppApiUrl).map(url->url.split(":"));

        //获取url地址
        try (Sftp sftp = new Sftp(JschUtil.openSession(urlsOpt.filter(us->us.length>0).map(us->us[0]).orElse(null)
                //获取端口号
                , urlsOpt.filter(us -> us.length > 1).map(us -> us[1]).map(Integer::valueOf).orElse(22)
                , sftpInfo.getAppKey(), sftpInfo.getAppSecret()))){
            //切换到文件目录
            sftp.cd(urlsOpt.filter(us->us.length>NumberConstant.TWO).map(us->us[NumberConstant.TWO]).orElse("upload"));
            String bakPath = sftp.getClient().realpath(urlsOpt.filter(us->us.length>NumberConstant.THREE).map(us->us[NumberConstant.THREE]).orElse("backup"));
            //备份目录需要预先创建,这段代码无效
            if(!sftp.exist(bakPath)){
                sftp.mkdir(bakPath);
            }
            List<String> fileNames = sftp.lsFiles(sftp.pwd());
            fileNames.sort(Comparator.comparing(BzTenantClientServiceImpl::fileNameRank));
            for (String fileName : fileNames) {
                String content = IoUtil.read(sftp.getClient().get(fileName), StandardCharsets.UTF_8);
                //写入到库 文件同名不进行重复写入
                if(!bzTenantClientMapper.existsFile(fileName)) {
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.insertFile(fileName, content)).commit();
                }else{
                    log.warn("文件[{}]已经存在", fileName);
                }
                //解析完成移动文件
                sftp.getClient().rename(fileName, String.format("%s/%s", bakPath,fileName));
            }
            return R.success(fileNames.stream().collect(Collectors.joining(SignConstant.BLANK)));
        } catch (SftpException e) {
            String errorMsg = StrUtil.format("sftp文件操作异常,错误编号:{}!", CommonUtils.getRandom4Code());
            StaticLog.error(e, errorMsg);
            return R.error(errorMsg);
        }
    }

    @Override
    public int batchUpdateOrderMsg(List<S47RequestBodyBO.OrderSubIdBO> orderSubIdList) {
        return bzTenantClientMapper.batchUpdateOrderMsg(orderSubIdList);
    }

    /**
     * 处理订单返回结果
     * @param orderSubIdList
     * @return
     */
    private static BzOrderResult handleBzOrderResult(List<S47RequestBodyBO.OrderSubIdBO> orderSubIdList) {

        List<OrderResponse> orderResponses = orderSubIdList.stream().map(BzTenantClientServiceImpl::toOrderResponse).collect(Collectors.toList());
        String errorMsg = orderSubIdList.stream().map(S47RequestBodyBO.OrderSubIdBO::getMessage).filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(SignConstant.BLANK));
        if(orderResponses.stream().anyMatch(or->NumberConstant.ONE.equals(or.getStatus()))){
            BzOrderResult success = BzOrderResult.success(orderResponses);
            success.setErrorMsg(errorMsg);
            return success;
        }
        return BzOrderResult.error(BzResultCode.DOWNSSTREAM_EXCEPTION_9006,errorMsg);
    }

    private static OrderResponse toOrderResponse(S47RequestBodyBO.OrderSubIdBO os) {
        S47RequestBodyBO.InventoryFrezzingAndReleaseBO far = os.getInventoryFrezzingAndRelease();
        OrderResponse orderResponse = new OrderResponse();
        orderResponse.setOrderCode(os.getOrderCode());
        if (far != null) {
            orderResponse.setOrderType(far.getOrderType());
            orderResponse.setDataSource(far.getPlatformSource());
            //任意一个影响结果的,返回失败 或标志为空但是错误消息不为空
            boolean isError = (CollUtil.isEmpty(os.getMessageMark()) && StrUtil.isNotBlank(os.getMessage()))
                    || os.getMessageMark().stream().anyMatch(b ->Boolean.TRUE.equals(b));
            if (isError) {
                orderResponse.setMemo(os.getMessage());
                orderResponse.setErrorCode(BzResultCode.DOWNSSTREAM_EXCEPTION_9006.getCode());
                orderResponse.setStatus(0);
            } else {
                orderResponse.setStatus(NumberConstant.ONE);
                //成功也返回消息提示
                orderResponse.setMemo(os.getMessage());
            }
            orderResponse.setPlatformOrderCode(far.getPlatformOrderCode());
            orderResponse.setStoreCode(far.getStoreCode());
            orderResponse.setWarehouseCode(far.getWarehouseCode());
        }
        return orderResponse;
    }

    /**
     * 执行占用和释放库存
     * @param orderSubIdList
     * @param wcfUrl
     * @param entry
     */
    private void invokeFrezzingAndRelease(List<S47RequestBodyBO.OrderSubIdBO> orderSubIdList, String wcfUrl
            , Map.Entry<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>> entry) {
        S47RequestBodyBO.OrderSubIdBO orderMsg = new S47RequestBodyBO.OrderSubIdBO().setOrderCode(entry.getKey())
                .setInventoryFrezzingAndRelease(entry.getValue().stream().findFirst().orElse(null)).setMessageMark(new LinkedList<>());
        orderSubIdList.add(orderMsg);
        //冻结
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingList = new LinkedList<>();
        //释放
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> releaseList = new LinkedList<>();
        Set<String> msgJoiner = new HashSet<>();
        checkAndSplitData(entry, frezzingList, releaseList, msgJoiner,orderMsg);
        if(!msgJoiner.isEmpty() && orderMsg.getMessageMark().stream().anyMatch(mm -> Boolean.TRUE.equals(mm))){
            //记录日志,影响最终结果不再调用库存接口
            orderMsg.addMessage(msgJoiner.stream().collect(Collectors.joining(SignConstant.BLANK)));
            return;
        }
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(RedisKeyConstant.BAOZUN_S47_CURRENT_CONCURRENCY);
        ApolloEntity apolloEntity = SpringUtil.getBean(ApolloEntity.class);
        Integer currentConcurrency = apolloEntity.getBaozunS47CurrentConcurrency();
        RedisTemplate<String,Double> intRedisTemplate = SpringUtil.getBean(RedisTemplate.class);
        boolean isSync = Optional.ofNullable(currentConcurrency).filter(cc -> cc>0)
                .filter(cc -> {
                    if(!rateLimiter.isExists()){
                        rateLimiter.trySetRate(RateType.OVERALL, cc, 1, RateIntervalUnit.SECONDS);
                    }
                    return true;
                })
                .map(cc -> rateLimiter.tryAcquire()).orElse(true)
                || ObjectUtil.defaultIfNull(intRedisTemplate.opsForValue().get(RedisKeyConstant.BAOZUN_S47_LAST_COST_TIME),0D) < apolloEntity.getBaozunS47SyncOaSubmitLimitTime();
        if(!frezzingList.isEmpty()){
            //占用库存
            BzSubmitOrderInput submitOrder = createSubmitOrder(frezzingList);
            String url = wcfUrl + BzTenantClientService.BAO_ZUN_SUBMIT_ORDER;
            String workName = "占用库存";
            if(isSync){
                SpringUtil.getBean(BzTenantClientService.class).postOrder(url,workName, orderMsg, submitOrder, r-> orderMsg.setRemark(submitOrder.getRemark()));
            }else{
                // 推送到mq
                oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_S47_OASUBMIT, JSON.toJSONString(Dict.create()
                        .set("url", url).set("workName", workName).set("orderMsg", orderMsg).set("submitOrder", submitOrder)));
                orderMsg.getMessageMark().add(Boolean.FALSE);
                orderMsg.addMessage("触发oa异步建单");
            }
            //调用接口
        }else if(!releaseList.isEmpty()){
            //释放库存
            BzSubmitOrderInput submitOrder = createSubmitOrder(releaseList);
            String url = wcfUrl + BzTenantClientService.BAO_ZUN_DELETE_PRODUCT;
            String workName = "释放库存";
            if(isSync){
                SpringUtil.getBean(BzTenantClientService.class).postOrder(url, workName, orderMsg,submitOrder,null);
            }else{
                // 推送到mq
                oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_S47_OASUBMIT, JSON.toJSONString(Dict.create()
                        .set("url", url).set("workName", workName).set("orderMsg", orderMsg).set("submitOrder", submitOrder)));
                orderMsg.getMessageMark().add(Boolean.FALSE);
                orderMsg.addMessage("触发oa异步释放库存");
            }
        }
        // 同步更新最后同步时间
        if(isSync){
            SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.API_STOP_WATCH)).ifPresent(stopWatch ->
                    intRedisTemplate.opsForValue().set(RedisKeyConstant.BAOZUN_S47_LAST_COST_TIME, ((StopWatch)stopWatch).getTotalTimeSeconds()));
        }
    }

    @Override
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{submitOrder.orderNum}")
    public void postOrder(String url, String workName, S47RequestBodyBO.OrderSubIdBO orderMsg, BzSubmitOrderInput submitOrder, Consumer<R> fallback) {
        Optional<BzTenantSalesOrder> orderOpt = salesOrderService.lambdaQuery()
                .eq(BzTenantSalesOrder::getTransactionNumber, submitOrder.getOrderNum())
                .isNull(BzTenantSalesOrder::getSubId)
                .select(BzTenantSalesOrder::getSubId, BzTenantSalesOrder::getTransactionNumber)
                .list().stream().findFirst();

        if(!orderOpt.isPresent()){
            throw new CustomizeException(StrUtil.format("宝尊单号: {}, oa订单已生成, 请勿重复生成", submitOrder.getOrderNum()));
        }

        SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.API_STOP_WATCH))
                .ifPresent(stopWatch -> ((StopWatch)stopWatch).start(StrUtil.format("调用oa{}", workName)));
        Dict input = Dict.create().set("input", submitOrder);
        try {
            Optional.ofNullable(restTemplate.postForEntity(url, input, R.class))
                    .ifPresent(sResult -> {
                        if (HttpStatus.OK == sResult.getStatusCode()) {
                            if (Result.SUCCESS == sResult.getBody().getCode()) {
                                Optional.ofNullable(fallback).ifPresent(f -> f.accept(sResult.getBody()));
                            } else {
                                StaticLog.error("调用{}接口返回[{}]异常,url:{},参数:{}", workName, sResult.getBody().getMsg(), url, JSON.toJSONString(input));
                                orderMsg.addMessage(String.format("调用%s接口返回[%s]异常", workName, sResult.getBody().getMsg()));
                            }
                        } else {
                            //接口发生系统异常
                            StaticLog.error("调用{}接口返回[{}]异常,url:{},参数:{}", workName, sResult.getStatusCode().getReasonPhrase(), url, JSON.toJSONString(input));
                            orderMsg.addMessage(String.format("调用%s接口系统[%s]异常", workName, sResult.getStatusCode().getReasonPhrase()));
                        }
                        //获取宝尊的订单号
                        String orderNum = submitOrder.getOrderNum();
                        updateDetailBasketId(orderNum);
                    });
        } catch (RestClientException e) {
            StaticLog.error(e, "调用{}接口异常,url:{},参数:{}", workName, url, JSON.toJSONString(input));
            orderMsg.addMessage(String.format("调用%s接口%s异常", workName, e.getMessage()));
        }finally {
            SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.API_STOP_WATCH)).ifPresent(stopWatch -> ((StopWatch)stopWatch).stop());
        }

    }

    /**
     * e02入库反馈失败补偿
     *
     * @param fkTenantId
     */
    @Override
    public void e02Xxl(Integer fkTenantId) {
        /**
         * 查询失败的调用记录
         */
        List<BaoZunFlowLog> errorE02BaoZunFlowLog = bzFlowLogService.getE02ErrorBaoZunFlowLogs();
        if (CollectionUtils.isEmpty(errorE02BaoZunFlowLog)) {
            return;
        }
        /**
         * 查询宝尊平台信息
         */
        BzTenant bzTenant = bzTenantService.lambdaQuery().eq(BzTenant::getId, fkTenantId)
                .eq(BzTenant::getIsEnable, 1).list().stream().findFirst().orElse(null);
        if (Objects.isNull(bzTenant)) {
            throw new CustomizeException("平台匹配错误：" + fkTenantId);
        }

        for (BaoZunFlowLog baoZunFlowLog : errorE02BaoZunFlowLog) {
            AppInfoDTO infoDTO = new AppInfoDTO();
            infoDTO.setTenantName(bzTenant.getTenantName());
            infoDTO.setAppKey(bzTenant.getAppKey());
            infoDTO.setAppSecret(bzTenant.getAppSecret());
            infoDTO.setRequestUrl(E02_URL);
            String apiResponseParams = baoZunFlowLog.getApiResponseParams();
            E02RequestBodyBO e02 = JSONUtil.toBean(apiResponseParams, E02RequestBodyBO.class);
            boolean executeFlag = this.addTryCount(baoZunFlowLog);
            if (executeFlag){
                this.postE02(infoDTO, e02);
            }
        }
    }

    @Override
    public R<String> getInvoiceQRcodeUrl(String subId) {
        //根据单号获取宝尊订单信息
        InvoiceQRcodeInfoBo invoiceQRcodeInfo = bzTenantClientMapper.getInvoiceQRcodeInfo(subId);
        //invoiceQRcodeInfo 为空返回异常提示
        if (Objects.isNull(invoiceQRcodeInfo)) {
            return R.error("获取宝尊订单信息失败");
        }
        QRcodeUrlResponse qRcodeUrlResponse = SpringUtil.getBean(BaoZunCloud.class).orderInvoiceQrcodeUrlF08(new InvoiceQRcodeUrlVo().setOrderCode(invoiceQRcodeInfo.getTransactionNumber())
                        .setAppleId(invoiceQRcodeInfo.getStoreCode()).setPlatformSource(invoiceQRcodeInfo.getPlatformSource()),
                invoiceQRcodeInfo.getAppKey(), invoiceQRcodeInfo.getAppSecret());
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "宝尊返回结果: {}", JSON.toJSONString(qRcodeUrlResponse));
        if(!qRcodeUrlResponse.success()){
            return R.error(qRcodeUrlResponse.getErrorMsg());
        }
        return R.success("查询成功", qRcodeUrlResponse.getUrl());
    }

    public boolean addTryCount(BaoZunFlowLog baoZunFlowLog) {
        int retryCount = Objects.isNull(baoZunFlowLog.getRetryCount()) ? 0 : baoZunFlowLog.getRetryCount();
        int newTryCount = retryCount + 1;
        if (newTryCount < MAX_TRY_COUNT) {
            bzFlowLogService.addTryCount(newTryCount,baoZunFlowLog.getId());
            return true;
        }
        //补偿超额时，停止补偿
        if (newTryCount == MAX_TRY_COUNT) {
            bzFlowLogService.deleteBaozunFlowLog(baoZunFlowLog.getId());
            //发送oa消息通知相关人员
            messagePushCloud.pushOaMessagePost("13685,8942,13774", "宝尊数据e02入库反馈次数超额", "");
        }
        return false;
    }


    /**
     * 更新宝尊明细与订单的关联关系
     * @param orderNum
     */
    @Override
    public void updateDetailBasketId(String orderNum) {
        Optional<BzTenantSalesOrder> orderOpt = salesOrderService.lambdaQuery()
                .eq(BzTenantSalesOrder::getTransactionNumber, orderNum)
                .isNotNull(BzTenantSalesOrder::getSubId)
                .select(BzTenantSalesOrder::getSubId, BzTenantSalesOrder::getTransactionNumber)
                .list().stream().findFirst();

        orderOpt.ifPresent(order -> {
            //获取宝尊的明细信息
            List<BzTenantSalesDetail> details = salesDetailService.lambdaQuery()
                    .eq(BzTenantSalesDetail::getFkTransactionNumber, order.getTransactionNumber())
                    .eq(BzTenantSalesDetail::getIsDel, Boolean.FALSE).isNull(BzTenantSalesDetail::getBasketId)
                    .orderByDesc(BzTenantSalesDetail::getId)
                    .list();
            //按ppid和数量获取最新的 basketId更新到明细中
            BiFunction<Integer, Integer, String> matchKeyFun = (ppid,count) -> StrUtil.format("{}_{}", ppid, count);
            Map<String, List<Basket>> basketMap = SpringUtil.getBean(BasketService.class).lambdaQuery().eq(Basket::getSubId, order.getSubId())
                    .list().stream().sorted(Comparator.comparing(Basket::getBasketId, Comparator.reverseOrder()))
                    .collect(Collectors.groupingBy(basket -> matchKeyFun.apply(Convert.toInt(basket.getPpriceid()),
                            basket.getBasketCount())));
            for (BzTenantSalesDetail detail : details) {
                List<Basket> baskets = basketMap.get(matchKeyFun.apply(detail.getPpid(), detail.getQuantity()));
                if(CollUtil.isEmpty(baskets)){
                    continue;
                }
                //每次取第一个, 也就是最新的一个
                Basket basket = baskets.remove(0);
                salesDetailService.lambdaUpdate().eq(BzTenantSalesDetail::getId, detail.getId())
                        .isNull(BzTenantSalesDetail::getBasketId)
                        .set(BzTenantSalesDetail::getBasketId, basket.getBasketId())
                        .update();
            }

            // 先下单记录退款日志
            if(details.stream().anyMatch(salesDetail -> TransactionTypeEnum.listReturnTypeCode()
                    .contains(salesDetail.getTransactionType()))){
                CompletableFuture.runAsync(() -> SpringUtil.getBean(SubLogsCloud.class).addSubLog(LambdaBuild.create(new SubLogsNewReq())
                        .set(SubLogsNewReq::setSubId, order.getSubId()).set(SubLogsNewReq::setInUser, "系统")
                        .set(SubLogsNewReq::setComment, StrUtil.format("用户发起退款, 宝尊退款单号[{}]", orderNum))
                        .set(SubLogsNewReq::setType, 1).set(SubLogsNewReq::setShowType, false).set(SubLogsNewReq::setDTime, LocalDateTime.now())
                        .build()));
            }
        });
    }


    private void insertOrUpdateSt(List<SalesDetailBO.SalesTender> stList) {
        //一次500条,解析异常写入message内
        if (!stList.isEmpty()){
            //批量更新,存在更新
            List<S47RequestBodyBO.OrderCodeUpcBO> stIds = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED, stList, sts -> bzTenantClientMapper.existsStIds(sts));
            List<SalesDetailBO.SalesTender> insertStList = new LinkedList<>();
            List<SalesDetailBO.SalesTender> updateStList = new LinkedList<>();
            for (SalesDetailBO.SalesTender sdsn : stList) {
                Optional<S47RequestBodyBO.OrderCodeUpcBO> orderOpt = stIds.stream()
                        .filter(ocu -> Objects.equals(ocu.getOrderCode(), sdsn.getTransactionNumber()) && updateStList.stream().noneMatch(ust -> Objects.equals(ocu.getId(),ust.getStLocalId())))
                        .findFirst();
                //金额精度处理
                if(sdsn.getTransactionAmount() != null){
                    sdsn.setTransactionAmount(sdsn.getTransactionAmount().setScale(4, RoundingMode.HALF_UP));
                }
                if(orderOpt.isPresent()){
                    updateStList.add(sdsn);
                    sdsn.setStLocalId(orderOpt.get().getId());
                }else{
                    insertStList.add(sdsn);
                }
            }
            //批量更新,存在更新,不存在插入
            if(!insertStList.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,insertStList,sts->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchInsertSaleSt(sts)).commit());
            }
            if(!updateStList.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,updateStList,sts->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateSaleSt(sts)).commit());
            }
        }
    }

    private void insertOrUpdateSns(List<SalesDetailBO.SalesDetailSnInfoBO> snList) {
        //一次500条,解析异常写入message内
        if (!snList.isEmpty()) {
            //批量更新,存在更新
            List<S47RequestBodyBO.OrderCodeUpcBO> snIds = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED, snList, sns -> bzTenantClientMapper.existsSnIds(sns));
            List<SalesDetailBO.SalesDetailSnInfoBO> insertSnList = new LinkedList<>();
            List<SalesDetailBO.SalesDetailSnInfoBO> updateSnList = new LinkedList<>();
            for (SalesDetailBO.SalesDetailSnInfoBO sdsn : snList) {
                Optional<S47RequestBodyBO.OrderCodeUpcBO> snOpt = snIds.stream().filter(ocu -> Objects.equals(ocu.getOrderCode(), sdsn.getTransactionNumber())
                        && Objects.equals(ocu.getUpc(), sdsn.getUpc())).findFirst();
                if(snOpt.isPresent()){
                    updateSnList.add(sdsn);
                    sdsn.setSnLocalId(snOpt.get().getId());
                }else{
                    insertSnList.add(sdsn);
                }
            }
            //批量更新,存在更新,不存在插入
            if(!insertSnList.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.TWO_HUNDRED,insertSnList,sns->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchInsertSaleSn(sns)).commit());
            }
            if(!updateSnList.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.TWO_HUNDRED,updateSnList,sns->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateSaleSn(sns)).commit());
            }

        }
    }

    private void insertOrUpdateSaleDetails(List<SalesDetailBO> salesDetails) {
        //一次500条,解析异常写入message内
        if(!salesDetails.isEmpty()){
            MultiValueMap<String,SalesDetailBO> orderMap = new LinkedMultiValueMap(NumberConstant.SIXTEEN);
            Set<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> orderDetailIds = new HashSet<>(NumberConstant.SIXTEEN);

            for (SalesDetailBO salesDetail : salesDetails) {
                orderMap.add(salesDetail.getTransactionNumber(),salesDetail);
                orderDetailIds.add(new S47RequestBodyBO.InventoryFrezzingAndReleaseBO().setOrderCode(salesDetail.getTransactionNumber())
                        .setUpc(salesDetail.getUpc()));
            }
            List<S47RequestBodyBO.OrderSubIdBO> orderSubIds = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED,orderMap.keySet(),ids->bzTenantClientMapper.existsOrderIds(ids));
            //订单详情
            List<S47RequestBodyBO.OrderCodeUpcBO> orderCodeUpcs = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED,orderDetailIds,ids->bzTenantClientMapper.existsOrderUpcs(ids));

            List<SalesDetailBO> insertOrders = new LinkedList<>();
            List<SalesDetailBO> updateOrders = new LinkedList<>();
            List<SalesDetailBO> refundOrders = new LinkedList<>();
            List<SalesDetailBO> insertOrderDetails = new LinkedList<>();
            List<SalesDetailBO> updateOrderDetails = new LinkedList<>();
            List<SalesDetailBO> refundDetails = new LinkedList<>();
            orderMap.forEach((key,value)->{
                Optional<SalesDetailBO> salesDetailOpt = value.stream().findFirst();
                if(orderSubIds.stream().anyMatch(os->Objects.equals(os.getOrderCode(),key))){
                    salesDetailOpt.ifPresent(updateOrders::add);
                }else{
                    salesDetailOpt.ifPresent(insertOrders::add);
                }
                salesDetailOpt.filter(salesDetail -> TransactionTypeEnum.listReturnTypeCode().contains(salesDetail.getTransactionType()))
                        .ifPresent(refundOrders::add);
            });
            for (SalesDetailBO salesDetail : salesDetails) {
                salesDetail.setUnitPrice(NumberUtil.null2Zero(salesDetail.getUnitPrice()).setScale(4, RoundingMode.HALF_UP));
                salesDetail.setActualPrice(NumberUtil.null2Zero(salesDetail.getActualPrice()).setScale(4, RoundingMode.HALF_UP));
                salesDetail.setDiscount(NumberUtil.null2Zero(salesDetail.getDiscount()).setScale(4, RoundingMode.HALF_UP));
                salesDetail.setLineTotal(NumberUtil.null2Zero(salesDetail.getLineTotal()).setScale(4, RoundingMode.HALF_UP));
                salesDetail.setTransferFee(NumberUtil.null2Zero(salesDetail.getTransferFee()).setScale(4, RoundingMode.HALF_UP));
                Optional<S47RequestBodyBO.OrderCodeUpcBO> upcOpt = orderCodeUpcs.stream()
                        .filter(ocu -> Objects.equals(ocu.getOrderCode(), salesDetail.getTransactionNumber())
                                && Objects.equals(ocu.getUpc(), salesDetail.getUpc()))
                        .filter(ocu -> salesDetails.stream().noneMatch(sd -> Objects.equals(sd.getOrderDetailId(), ocu.getId())))
                        .findFirst();
                if(upcOpt.isPresent()){
                    updateOrderDetails.add(salesDetail);
                    salesDetail.setOrderDetailId(upcOpt.get().getId());
                }else{
                    insertOrderDetails.add(salesDetail);
                }
                if(TransactionTypeEnum.listReturnTypeCode().contains(salesDetail.getTransactionType())){
                    refundDetails.add(salesDetail);
                }

            }
            //批量更新,存在更新,不存在插入
            if(!insertOrders.isEmpty()){
                concatBySlipCode(insertOrders);
                CommonUtils.bigDataPage(NumberConstant.ONE_HUNDRED, insertOrders, isoList ->
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchInsertSaleOrders(isoList)).commit());
            }
            if(!insertOrderDetails.isEmpty()){
                concatDetailByTransactionNumber(insertOrders.stream().filter(io->Objects.nonNull(io.getOriginSalesOrder())).collect(Collectors.toList()), insertOrderDetails);
                CommonUtils.bigDataPage(NumberConstant.ONE_HUNDRED,insertOrderDetails,isodList->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchInsertSaleDetails(isodList)).commit());
            }
            if(!updateOrders.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.FIFTY,updateOrders,uoList->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateSaleOrders(uoList)).commit());
            }
            if(!updateOrderDetails.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.EIGHTY,updateOrderDetails,uodList->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateSaleDetails(uodList)).commit());
            }
            //批量更新所有退款的 subId areaId fk_tenant_id ppid
            if(!refundOrders.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,refundOrders,rfoList->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->
                                bzTenantClientMapper.batchUpdateRefundOrderLocalInfo(rfoList.stream().map(SalesDetailBO::getTransactionNumber).collect(Collectors.toList()))).commit());
            }
            if(!refundDetails.isEmpty()){
                CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,refundDetails,rfdList->
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->
                                bzTenantClientMapper.batchUpdateRefundDetailLocalInfo(rfdList.stream().map(SalesDetailBO::getTransactionNumber).collect(Collectors.toList()))).commit());
            }
            //更新宝尊明细里面的basketId信息
            orderMap.forEach((key,value)->{
                updateDetailBasketId(key);
            });
            //删除预售单旧单信息
            if(!insertOrders.isEmpty()){
                //产生新的订单之后, 旧订单信息标记为已删除
                List<String> delOrderIds = insertOrders.stream().map(SalesDetailBO::getOriginSalesOrder).filter(Objects::nonNull)
                        .filter(oso -> Boolean.TRUE.equals(oso.getIsDel())).map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.toList());
                if(!delOrderIds.isEmpty()){
                    //删除旧单
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->{
                        boolean delOrderR = SpringUtil.getBean(BzTenantSalesOrderService.class).lambdaUpdate()
                                .in(BzTenantSalesOrder::getTransactionNumber, delOrderIds).eq(BzTenantSalesOrder::getIsDel, Boolean.FALSE)
                                .set(BzTenantSalesOrder::getIsDel, Boolean.TRUE)
                                .update();
                        if(delOrderR){
                            //删除旧单明细
                            SpringUtil.getBean(BzTenantSalesDetailService.class).lambdaUpdate()
                                    .in(BzTenantSalesDetail::getFkTransactionNumber, delOrderIds).eq(BzTenantSalesDetail::getIsDel, Boolean.FALSE)
                                    .set(BzTenantSalesDetail::getIsDel, Boolean.TRUE)
                                    .update();
                        }
                    }).commit();
                }
            }
        }
    }

    private void concatDetailByTransactionNumber(List<SalesDetailBO> withOriginOrders, List<SalesDetailBO> insertOrderDetails) {
        if(withOriginOrders.isEmpty()){
            //原订单号为空,不用挂接
            return;
        }
        //根据平台订单信息 和平台来源匹配信息 和 upc 获取本地ppid
        List<BzTenantSalesDetail> originSalesDetails = CommonUtils.bigDataInQuery(insertOrderDetails, iods-> queryOriginSalesDetail(withOriginOrders, iods));
        //挂接平台来源信息
        for (SalesDetailBO salesOrder : insertOrderDetails) {
            originSalesDetails.stream()
                    //商品upc 相等
                    .filter(osd->Objects.equals(osd.getUpc(),salesOrder.getUpc()))
                    .filter(osd ->
                            withOriginOrders.stream()
                                    //找到原订单
                                    .anyMatch(woo->Objects.equals(woo.getOriginSalesOrder().getTransactionNumber(),osd.getFkTransactionNumber())
                                            //匹配当前单号
                                            && Objects.equals(woo.getTransactionNumber(),salesOrder.getTransactionNumber())))
                    .findFirst()
                    .ifPresent(osd->salesOrder.setPpid(osd.getPpid()));
        }
    }

    private List<BzTenantSalesDetail> queryOriginSalesDetail(List<SalesDetailBO> withOriginOrders, List<SalesDetailBO> iods) {
        LambdaQueryWrapper<BzTenantSalesDetail> lamdQuery = iods.stream()
                .reduce(new LambdaQueryWrapper<>()
                        , (query, iod) -> withOriginOrders.stream().filter(woo -> Objects.equals(woo.getTransactionNumber(), iod.getTransactionNumber())).findFirst()
                                .map(woo -> query.or(q -> q.eq(BzTenantSalesDetail::getFkTransactionNumber, woo.getOriginSalesOrder().getTransactionNumber())
                                        .eq(BzTenantSalesDetail::getUpc, iod.getUpc()))).orElse(query), (query1, query2) -> query1);
        if(lamdQuery.isEmptyOfWhere()){
            return Collections.emptyList();
        }
        return salesDetailService.list(lamdQuery);
    }

    /**
     * 挂接订单信息通过平台订单号
     * @param insertOrders
     */
    @Override
    public void concatBySlipCode(List<SalesDetailBO> insertOrders) {
        //根据平台订单信息 和平台来源匹配信息
        List<BzTenantSalesOrder> originSalesOrders = CommonUtils
                .bigDataInQuery(insertOrders, ios -> {
                    LambdaQueryWrapper<BzTenantSalesOrder> lambdaQuery = ios.stream()
                            .reduce(new LambdaQueryWrapper<>()
                                    , (query, io) -> query.or(q -> q.eq(BzTenantSalesOrder::getPlatformSource, io.getPlatformSource())
                                            .eq(BzTenantSalesOrder::getSlipCode, io.getSlipCode()))
                                    , (query1, query2) -> query1);
                    lambdaQuery.orderByDesc(BzTenantSalesOrder::getPlatformSource, BzTenantSalesOrder::getSlipCode, BzTenantSalesOrder::getCreateTime);
                    return salesOrderService.list(lambdaQuery);
                });
        //根据平台单号批量获取商品信息
        List<String> transactionNumbers = originSalesOrders.stream().map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.toList());
        Map<String,List<BzTenantSalesDetail>> tnSalesDetailsMap = CommonUtils
                .bigDataInQuery(transactionNumbers, ids -> salesDetailService.lambdaQuery()
                        .in(BzTenantSalesDetail::getFkTransactionNumber, ids).list()).stream()
                .collect(Collectors.groupingBy(BzTenantSalesDetail::getFkTransactionNumber));
        //查询订单中匹配的upc
        BiFunction<List<BzTenantSalesDetail>, SalesDetailBO, BzTenantSalesDetail> findMathDetailFun = (details, currSalesDetail) -> {
            if(details == null){
                return null;
            }
            //确保一个只能对应一个,不能对应多个
            BzTenantSalesDetail originSaleDetail = details.stream()
                    .filter(detail -> ObjectUtil.equals(detail.getUpc(), currSalesDetail.getUpc())
                            && ObjectUtil.equals(detail.getQuantity(), currSalesDetail.getQuantity())
                            && (Objects.isNull(detail.getLineNumber()) || Objects.equals(detail.getLineNumber(), currSalesDetail.getLineNumber()))
                    )
                    .findFirst().map(sd -> {
                        if (TransactionTypeEnum.listReturnTypeCode().contains(sd.getTransactionType())){
                            // 最近的是退款单, 那就是新的单号
                            return null;
                        }
                        currSalesDetail.setBasketId(sd.getBasketId());
                        sd.setLineNumber(currSalesDetail.getLineNumber());
                        return sd;
                    }).orElse(null);
            return originSaleDetail;
        };
        //挂接平台来源信息
        for (SalesDetailBO salesOrder : insertOrders) {
            originSalesOrders.stream().filter(oso -> Objects.equals(oso.getPlatformSource(), salesOrder.getPlatformSource())
                    && Objects.equals(oso.getSlipCode(), salesOrder.getSlipCode())).findFirst()
                    .ifPresent(oso->{
                        BzTenantSalesDetail salesDetail = findMathDetailFun.apply(tnSalesDetailsMap.get(oso.getTransactionNumber()), salesOrder);
                        if(salesDetail == null){
                            return;
                        }
                        salesOrder.setAreaId(oso.getAreaId());
                        salesOrder.setTenantId(oso.getFkTenantId());
                        if (!TransactionTypeEnum.listReturnTypeCode().contains(salesOrder.getTransactionType())){
                            //退款的本地订单信息后面单独处理,这里不进行处理
                            salesOrder.setUserId(oso.getUserId());
                            salesOrder.setSubId(oso.getSubId());
                            salesOrder.setOrderDate(oso.getOrderDate());
                        }
                        salesOrder.setOriginSalesOrder(oso);
                        salesOrder.setOriginSalesDetail(salesDetail);

                        if(ObjectUtil.notEqual(oso.getTransactionNumber(), salesOrder.getTransactionNumber())
                                // 排除销售单
                                && !StrUtil.startWith(oso.getTransactionNumber(), "DO")
                                && Objects.equals(salesOrder.getTransactionType(), TransactionTypeEnum.S.getCode())){
                            //销售单,不同单号的情况下, 旧的明细和订单都标记为已删除
                            if(salesOrder.getOriginSalesDetail() != null){
                                salesOrder.getOriginSalesDetail().setIsDel(Boolean.TRUE);
                            }
                            oso.setIsDel(Boolean.TRUE);
                        }
                    });
        }
    }

    @Override
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{a30RequestBody.transactionNumber}")
    public BzOrderResult a30(A30RequestBodyBO a30RequestBody, DecryptBodyBO decryptBody) {
        if(CollUtil.isEmpty(a30RequestBody.getSalesDetails())){
            return BzOrderResult.error(BzResultCode.MISSING_PARAMETERS_1006, "订单明细不能为空");
        }
        try {
            // 获取会员信息
            Optional<A30RequestBodyBO.ExtProps> extPropsOpt = Optional.ofNullable(JSON.parseObject(a30RequestBody.getExtProps(), A30RequestBodyBO.ExtProps.class));
            Optional<A30RequestBodyBO.ExtProps.Member> memberOpt = extPropsOpt.map(A30RequestBodyBO.ExtProps::getMember);

            List<SalesDetailBO.SalesDetailSnInfoBO> snList = new LinkedList<>();
            List<SalesDetailBO> salesDetails = a30RequestBody.getSalesDetails().stream()
                    .map(sd -> a30ToA01DetailInfo(a30RequestBody, sd, memberOpt, snList))
                    .collect(Collectors.toList());
            //构建st信息
            List<SalesDetailBO.SalesTender> stList = a30RequestBody.getSalesTenders().stream()
                    .map(st -> SalesDetailBO.SalesTender.builder().paymentType(st.getPaymentType()).transactionAmount(st.getPaymentAmount())
                            .transactionNumber(a30RequestBody.getTransactionNumber()).paymentChannel(st.getPaymentChannel()).build())
                    .collect(Collectors.toList());
            //构建增值服务信息
            List<A30ValueAddedServiceBO> valueAddedServices = ObjectUtil.defaultIfNull(a30RequestBody.getValueAddedService(), Collections.emptyList());
            List<BzTenantSalesAddService> addServiceList = valueAddedServices.stream().map(vas -> {
                BzTenantSalesAddService as = new BzTenantSalesAddService();
                as.setFkTransactionNumber(a30RequestBody.getTransactionNumber());
                as.setAmount(vas.getAmount());
                as.setPaymentType(Convert.toStr(vas.getType()));
                return as;
            }).collect(Collectors.toList());
            //清理无效的数据
            clearData(salesDetails, snList, stList, addServiceList);
            insertOrUpdateSaleDetails(salesDetails);
            insertOrUpdateSns(snList);
            insertOrUpdateSt(stList);
            insertOrUpdateAddService(addServiceList);
        } catch (Exception e) {
            // 结算异常通知
            RRExceptionHandler.logError(StrUtil.format("宝尊A30订单[{}]结算", a30RequestBody.getTransactionNumber()), a30RequestBody,
                    e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        BaozunMqReceiver baozunMqReceiver = SpringUtil.getBean(BaozunMqReceiver.class);
        Stream.of(a30RequestBody.getTransactionNumber())
                //解析云仓订单信息,并进行预占
                .peek(baozunMqReceiver::baozunParseA01AfterCloudStoreNoEx)
                //广播解析后的订单号
                .forEach(transactionNumber -> oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_PARSE_A01_TOPIC,"", transactionNumber));

        //异步触发完单
        CompletableFuture.runAsync(()-> SpringUtil.getBean(CsharpCloud.class).baoZunAutoCompleteSub(a30RequestBody.getTransactionNumber()));
        return BzOrderResult.success(null);
    }

    @Override
    public BzOrderResult m09(M09ReqBO req, DecryptBodyBO decryptBody) {
        // req.code 不能为空
        if(StrUtil.isBlank(req.getCode())){
            return BzOrderResult.error(BzResultCode.MISSING_PARAMETERS_1006, "供应商编码不能为空");
        }
        // code 更新到 baozun_tenant 表中的
        Integer tenantId = decryptBody.getTenantId();
        BzTenant bzTenant = bzTenantService.getById(tenantId);
        Map<String, BzTenant.ChannelInfo> channelInfoMap = BzTenantClientService.getChannelInfoMap(bzTenant);
        if(channelInfoMap.get(req.getCode()) != null){
            return BzOrderResult.success(null);
        }
        channelInfoMap.put(req.getCode(), new BzTenant.ChannelInfo());
        boolean upR = bzTenantService.lambdaUpdate().eq(BzTenant::getId, tenantId).eq(BzTenant::getNppChannel, bzTenant.getNppChannel())
                .set(BzTenant::getNppChannel, JSON.toJSONString(channelInfoMap, SerializerFeature.WriteMapNullValue))
                .update();
        // 更新失败
        if(!upR){
            return BzOrderResult.error(BzResultCode.SYSTEM_ERROR_9001, "更新渠道信息失败");
        }
        return BzOrderResult.success(null);
    }

    @Override
    public R<List<String>> tradeDateCompensate(Integer timeout) {
        //异步触发完单
        List<String> transactionNumbers = bzTenantClientMapper.listTradeDateCompensate(timeout);
        for (String transactionNumber : transactionNumbers) {
            CompletableFuture.runAsync(()-> SpringUtil.getBean(CsharpCloud.class).baoZunAutoCompleteSub(transactionNumber));
        }
        return R.success(transactionNumbers);
    }

    private void insertOrUpdateAddService(List<BzTenantSalesAddService> addServiceList) {
        if(CollUtil.isEmpty(addServiceList)){
            return;
        }
        //一次500条,解析异常写入message内
        //批量更新,存在更新
        List<S47RequestBodyBO.OrderCodeUpcBO> asIds = CommonUtils.bigDataInQuery(NumberConstant.FIVE_HUNDRED, addServiceList, ass -> bzTenantClientMapper.existsAsIds(ass));
        List<BzTenantSalesAddService> insertAsList = new LinkedList<>();
        List<BzTenantSalesAddService> updateAsList = new LinkedList<>();
        for (BzTenantSalesAddService avas : addServiceList) {
            Optional<S47RequestBodyBO.OrderCodeUpcBO> orderOpt = asIds.stream()
                    .filter(ocu -> Objects.equals(ocu.getOrderCode(), avas.getFkTransactionNumber()) && updateAsList.stream().noneMatch(ust -> Objects.equals(ocu.getId(),ust.getId())))
                    .findFirst();
            //金额精度处理
            if(avas.getAmount() != null){
                avas.setAmount(avas.getAmount().setScale(4, RoundingMode.HALF_UP));
            }
            if(orderOpt.isPresent()){
                updateAsList.add(avas);
                avas.setId(orderOpt.get().getId());
            }else{
                insertAsList.add(avas);
            }
        }
        //批量更新,存在更新,不存在插入
        if(!insertAsList.isEmpty()){
            CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,insertAsList,ass->
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchInsertSaleAs(ass)).commit());
        }
        if(!updateAsList.isEmpty()){
            CommonUtils.bigDataPage(NumberConstant.FIVE_HUNDRED,updateAsList,ass->
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.batchUpdateSaleAs(ass)).commit());
        }
    }

    private static SalesDetailBO a30ToA01DetailInfo(A30RequestBodyBO a30RequestBody, A30SalesDetailBO sd,
                                                    Optional<A30RequestBodyBO.ExtProps.Member> memberOpt,
                                                    List<SalesDetailBO.SalesDetailSnInfoBO> snList) {
        String transactionTime = a30RequestBody.getTransactionTime();
        List<String> transactionTimes = StrUtil.splitTrim(StrUtil.removeAll(transactionTime,'-', ':'), StringPool.SPACE);

        //订单明细信息
        List<A30RequestBodyBO.GuideInfo> guideInfoList = ObjectUtil.defaultIfNull(a30RequestBody.getGuideInfo(), Collections.emptyList());
        SalesDetailBO detailBO = SalesDetailBO.builder().transactionNumber(a30RequestBody.getTransactionNumber())
                        .orderDate(a30RequestBody.getPaymentTime()).transactionDate(CollUtil.get(transactionTimes, 0))
                        .transactionTime(CollUtil.get(transactionTimes, 1)).storeCode(a30RequestBody.getStoreCode())
                        .slipCode(a30RequestBody.getPlatformOrderCode()).platformSource(a30RequestBody.getPlatformSource())
                        .transactionType(a30RequestBody.getTransactionType()).orderLabel(a30RequestBody.getOrderLabel())
                        .lineNumber(sd.getPlatfromLineNo()).upc(sd.getUpc())
                        .comboCode(sd.getComboCode()).unitPrice(sd.getUnitPrice())
                        .actualPrice(sd.getActualPrice()).quantity(sd.getQty())
                        .discount(sd.getDiscount()).lineTotal(sd.getLineTotal())
                        .customerID(null).email(null)
                        .name(memberOpt.map(A30RequestBodyBO.ExtProps.Member::getName).orElse(null))
                        .telephone(memberOpt.map(A30RequestBodyBO.ExtProps.Member::getMobile).orElse(null))
                        .mobile(memberOpt.map(A30RequestBodyBO.ExtProps.Member::getMobile).orElse(null))
                        .transferFee(NumberUtil.null2Zero(a30RequestBody.getTransferFee()).add(NumberUtil.null2Zero(a30RequestBody.getPlatformTransferFee())))
                        .operator(StrUtil.removeAny(guideInfoList.stream()
                                        .map(A30RequestBodyBO.GuideInfo::getGuideId).filter(StrUtil::isNotBlank)
                                .filter(gId -> gId.length() > 10).findFirst().orElse(a30RequestBody.getOperator()), "-A", "-B"))
                        .invoiceAmt(sd.getInvoiceAmt())
                        .buyerMemo(a30RequestBody.getBuyerMemo())
                        .associatedOrderNumber(a30RequestBody.getAssociatedOrderNumber()).build();

        //串号信息
        List<A30SalesDetailSnInfoBO> sdSnList = ObjectUtil.defaultIfNull(sd.getSalesDetailSnInfos(), Collections.emptyList());
        sdSnList
                .stream()
                .map(sn -> SalesDetailBO.SalesDetailSnInfoBO.builder()
                        .transactionNumber(detailBO.getTransactionNumber())
                        .slipCode(detailBO.getSlipCode())
                        .upc(sd.getUpc())
                        .lineNumber(sd.getPlatfromLineNo())
                        .name(detailBO.getName())
                        .email(null)
                        .sn(sn.getSn())
                        .invStatus(sn.getInvStatus())
                        .mobile(detailBO.getMobile())
                .build())
                .forEach(snList::add);
        return detailBO;
    }

    /**
     *  内容转为对象集合
     * @param orderMsgMap
     * @param fileInfo
     * @param salesDetails
     * @param snList
     * @param stList
     */
    private static void contentToObjs(MultiValueMap<String, String> orderMsgMap, SftpInfoBO.FileInfo fileInfo, List<SalesDetailBO> salesDetails
            , List<SalesDetailBO.SalesDetailSnInfoBO> snList, List<SalesDetailBO.SalesTender> stList) {
        boolean isSalesDetail = StrUtil.startWithIgnoreCase(fileInfo.getName(),SalesDetailBO.FILE_NAME_PREV);
        boolean isSn = StrUtil.startWithIgnoreCase(fileInfo.getName(),SalesDetailBO.SalesDetailSnInfoBO.FILE_NAME_PREV);
        boolean isSt = StrUtil.startWithIgnoreCase(fileInfo.getName(),SalesDetailBO.SalesTender.FILE_NAME_PREV);
        //字符串转字节流,按行读取,一个文件,一个事务
        Charset charSet = StandardCharsets.UTF_8;
        IoUtil.readLines(IoUtil.toStream(fileInfo.getContent(), charSet), charSet
                , (LineHandler) line -> lineToObj(orderMsgMap, salesDetails, snList, stList, isSalesDetail, isSn, isSt, line));
    }

    private static void lineToObj(MultiValueMap<String, String> orderMsgMap, List<SalesDetailBO> salesDetails, List<SalesDetailBO.SalesDetailSnInfoBO> snList
            , List<SalesDetailBO.SalesTender> stList, boolean isSalesDetail, boolean isSn, boolean isSt, String line) {
        if(isSalesDetail){
            SalesDetailBO salesDetail = new SalesDetailBO();
            salesDetails.add(salesDetail);
            Class<SalesDetailBO> clas = SalesDetailBO.class;
            String[] linkFiles = SalesDetailBO.LINK_FILE;
            setLineValueToObject(line, salesDetail, clas, linkFiles, orderMsgMap);
        } else if(isSn){
            SalesDetailBO.SalesDetailSnInfoBO sn = new SalesDetailBO.SalesDetailSnInfoBO();
            snList.add(sn);
            Class<SalesDetailBO.SalesDetailSnInfoBO> clas = SalesDetailBO.SalesDetailSnInfoBO.class;
            String[] linkFiles = SalesDetailBO.SalesDetailSnInfoBO.LINK_FILE;
            setLineValueToObject(line, sn, clas, linkFiles, orderMsgMap);
        } else if(isSt){
            SalesDetailBO.SalesTender st = new SalesDetailBO.SalesTender();
            stList.add(st);
            Class<SalesDetailBO.SalesTender> clas = SalesDetailBO.SalesTender.class;
            String[] linkFiles = SalesDetailBO.SalesTender.LINK_FILE;
            setLineValueToObject(line, st, clas, linkFiles, orderMsgMap);
        }
    }

    private static void setLineValueToObject(String line, Object obj, Class<?> clas, String[] linkFiles, MultiValueMap<String, String> orderMsgMap) {
        String[] values = line.split(SignConstant.COMMA);
        for (int i = 0; i < values.length && i< linkFiles.length; i++) {
            String value = values[i];
            Field field = ReflectUtil.getField(clas, linkFiles[i]);
            try {
                //转换类型,类型错误,写入日志
                ReflectUtil.setFieldValue(obj,field,Convert.convert(field.getType(),value));
            } catch (ConvertException e) {
                String transactionNumber = (String) ReflectUtil.getFieldValue(obj, "transactionNumber");
                StaticLog.error(e,"单号:{},字段{}类型转换异常", transactionNumber,field.getName());
                orderMsgMap.set(transactionNumber,StrFormatter.format("字段{}类型转换异常",field.getName()));
            }
        }
    }

    /**
     * 文件名称排序
     * @param fn
     * @return
     */
    private static Integer fileNameRank(String fn) {
        if(StrUtil.startWithIgnoreCase(fn,SalesDetailBO.FILE_NAME_PREV)){
            return NumberConstant.THREE;
        }else if(StrUtil.startWithIgnoreCase(fn,SalesDetailBO.SalesDetailSnInfoBO.FILE_NAME_PREV)){
            return NumberConstant.TWO;
        }else if(StrUtil.startWithIgnoreCase(fn,SalesDetailBO.SalesTender.FILE_NAME_PREV)){
            return NumberConstant.ONE;
        }
        return NumberConstant.FOUR;
    }

    /**
     * 创建提交订单信息
     * @param orderDetails
     * @return
     */
    private static BzSubmitOrderInput createSubmitOrder(List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> orderDetails) {
        BzSubmitOrderInput submitOrderInput = new BzSubmitOrderInput().setDetails(new LinkedList<>());

        for (int i = 0; i < orderDetails.size(); i++) {
            S47RequestBodyBO.InventoryFrezzingAndReleaseBO far = orderDetails.get(i);
            if (i == 0){
                String remark = String.format("【BJ】【%s】%s", far.getPlatformSource(), far.getPlatformOrderCode());
                if(StrUtil.isNotBlank(far.getBuyerMemo())){
                    remark = StrUtil.format("{} {}", remark, far.getBuyerMemo());
                }
                submitOrderInput.setOrderNum(far.getOrderCode()).setDelivery(1).setRemark(remark)
                        .setSubType(BzSubmitOrderInput.SubType.valueByPlatform(far.getPlatformSource()).map(BzSubmitOrderInput.SubType::getCode)
                                .orElse(BzSubmitOrderInput.SubType.MINI_PROGRAM.getCode()))
                        .setUserId(far.getUserId()).setAreaId(far.getAreaId());
            }

            submitOrderInput.getDetails().add(new BzSubmitOrderInput.SubmitOrderDetailInput().setPpid(far.getPpid())
                    .setCount(Math.abs(far.getQty())).setId(far.getOrderDetailId())
                    .setPrice(far.getUnitPrice()).setNewProductPresaleFlag(far.getNewProductPresaleFlag()));
        }
        return submitOrderInput;
    }

    /**
     * 校验和拆分 占用和释放库存数据
     * @param entry
     * @param frezzingList
     * @param releaseList
     * @param msgJoiner
     * @param orderMsg
     */
    private static void checkAndSplitData(Map.Entry<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>> entry
            , List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingList, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> releaseList
            , Set<String> msgJoiner, S47RequestBodyBO.OrderSubIdBO orderMsg) {
        for (S47RequestBodyBO.InventoryFrezzingAndReleaseBO far : entry.getValue()) {
            if(Boolean.TRUE.equals(far.getIsOrderDel())){
                orderMsg.getMessageMark().add(Boolean.FALSE);
                msgJoiner.add("订单已删除");
            }
            if(far.getPpid() == null){
                orderMsg.getMessageMark().add(Boolean.TRUE);
                msgJoiner.add(String.format("未配置upc为[%s]的本地商品",far.getUpc()));
            }
            if (far.getAreaId() == null){
                orderMsg.getMessageMark().add(Boolean.TRUE);
                msgJoiner.add(String.format("未配置门店为[%s]的本地门店",far.getWarehouseCode()));
            }
            if(far.getUserId() == null){
                orderMsg.getMessageMark().add(Boolean.TRUE);
                msgJoiner.add(String.format("未配置平台为[%s]的子商户",far.getPlatformSource()));
            }
            addData(frezzingList, releaseList, far, msgJoiner,orderMsg);
        }
        if(!frezzingList.isEmpty() && !releaseList.isEmpty()){
            //不能同时存在释放库存和占用库存
            orderMsg.getMessageMark().add(Boolean.TRUE);
            msgJoiner.add("不能同时进行占用和释放库存");
        }
    }

    private static void addData(List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingList
            , List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> releaseList
            , S47RequestBodyBO.InventoryFrezzingAndReleaseBO far, Set<String> msgJoiner, S47RequestBodyBO.OrderSubIdBO orderMsg) {
        Integer qty = DecideUtil.isNull(far.getQty(), 0);
        if(qty == 0){
            orderMsg.getMessageMark().add(Boolean.TRUE);
            msgJoiner.add(String.format("upc[%s]数量为零", far.getUpc()));
        }else if(qty >0){
            if (far.getSubId() == null) {
                frezzingList.add(far);
            } else {
                Sub sub = SpringUtil.getBean(SubService.class).getSub(far.getSubId());
                if (Objects.nonNull(sub) && SubCheckEnum.SUB_CHECK_DELETED.getCode().equals(sub.getSubCheck())) {
                    BzTenantSubMapper bzTenantSubMapper = SpringUtil.getBean(BzTenantSubMapper.class);
                    MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,
                            () -> bzTenantSubMapper.emptyBaozunTenantSalesOrderSubId(Collections.singletonList(far.getOrderCode()))).commit();
                    orderMsg.getMessageMark().add(Boolean.FALSE);
                    msgJoiner.add("客户取消部分商品,清空单号:" + far.getSubId());
                    far.setSubId(null);
                    frezzingList.add(far);
                } else {
                    orderMsg.getMessageMark().add(Boolean.FALSE);
                    msgJoiner.add("请勿重复占用库存");
                }
            }
        }else if(qty <0){
            if(far.getSubId() == null){
                orderMsg.getMessageMark().add(Boolean.TRUE);
                msgJoiner.add("订单不存在,无法释放库存");
            }else if(far.getOrderDetailId() == null){
                orderMsg.getMessageMark().add(Boolean.FALSE);
                msgJoiner.add(StrUtil.format("upc[{}]ppid[{}]详情不存在,无法释放库存",far.getUpc(),far.getPpid()));
            }else{
                releaseList.add(far);
            }
        }
    }

    /**
     * 挂接本地配置信息 areaId user_id ppid subId
     * @param frezzingAndReleases
     * @param allOrderIds
     * @param tenantId
     */
    private void concatLocalInfo(List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingAndReleases, Set<String> allOrderIds, Integer tenantId) {
        //获取配置的ppid信息
        Set<String> upcSet = Sets.newHashSetWithExpectedSize(frezzingAndReleases.size());
        //宝尊门店集合
        Set<String> houseSet = Sets.newHashSetWithExpectedSize(frezzingAndReleases.size());
        //平台来源集合
        Set<String> platformSet = Sets.newHashSetWithExpectedSize(frezzingAndReleases.size());
        for (S47RequestBodyBO.InventoryFrezzingAndReleaseBO far : frezzingAndReleases) {
            upcSet.add(far.getUpc());
            houseSet.add(far.getWarehouseCode());
            platformSet.add(far.getPlatformSource());
        }
        //获取门店配置映射
        List<S47RequestBodyBO.HouseAreaBO> houseAreaList = bzTenantClientMapper.listHouseArea(houseSet,tenantId);
        //获取下单用户id映射
        List<S47RequestBodyBO.PlatformUserBO> platformUserList = bzTenantClientMapper.listPlatformUser(platformSet,tenantId);
        List<S47RequestBodyBO.UpcPpidBO> upcPpidList = bzTenantClientMapper.listUpcPpid(upcSet, tenantId);
        //获取订单信息
        List<S47RequestBodyBO.OrderSubIdBO> existsOrderSubIds = bzTenantClientMapper.existsOrderIds(allOrderIds);
        //获取订单详情
        List<S47RequestBodyBO.OrderCodeUpcBO> existsOrderUpcList = bzTenantClientMapper.existsOrderUpcs(frezzingAndReleases);
        // 获取线下订单的会员电话
        Map<String, S47RequestBodyBO.InventoryFrezzingAndReleaseBO> memberMobileMap = frezzingAndReleases.stream()
                .filter(far -> Objects.equals(far.getPlatformSource(), ChildMerchantNameEnum.HDPOS.getCode()))
                .filter(far -> StrUtil.isNotBlank(far.getMobile()))
                .collect(Collectors.toMap(S47RequestBodyBO.InventoryFrezzingAndReleaseBO::getMobile, Function.identity(), (v1,v2) -> v1));
        //批量查询会员信息
        Map<String,Integer> mobileUserIdMap = CollUtil.newHashMap(memberMobileMap.size());
        memberMobileMap.keySet().stream()
                .forEach(mobile -> {
                    MemberReq req = new MemberReq();
                    req.setMobile(mobile);
                    req.setUserName(ObjectUtil.defaultIfBlank(memberMobileMap.get(mobile).getUserName(), mobile));
                    req.setRealName(req.getUserName());
                    houseAreaList.stream().findFirst().ifPresent(ha -> req.setXtenant(ha.getXtenant()));
                    //设置真实的会员id,不存在进行注册
                    R<Integer> userIdR = null;
                    try {
                        userIdR = memberClient.checkAndRegisterUser(req);
                    } catch (Exception e) {
                        throw new CustomizeException("注册会员发生异常", BzTenantCloudStoreService.RETRY_CODE, e);
                    }
                    if(!userIdR.isSuccess()){
                        log.warn("查询会员注册结果失败, 原因: {}", JSON.toJSONString(userIdR));
                    }
                    mobileUserIdMap.put(mobile, userIdR.getData());
                });
        for (S47RequestBodyBO.InventoryFrezzingAndReleaseBO far : frezzingAndReleases) {
            //挂接subid
            existsOrderSubIds.stream().filter(eos -> Objects.equals(eos.getOrderCode(), far.getOrderCode()))
                    .findFirst()
                    .ifPresent(eos -> {
                        far.setSavedOrder(Boolean.TRUE);
                        far.setSubId(eos.getSubId());
                        far.setIsOrderDel(eos.getIsDel());
                        far.setTenantId(eos.getTenantId());
                    });
            //挂接areaId
            houseAreaList.stream().filter(ha->Objects.equals(ha.getWarehouseCode(),far.getWarehouseCode()))
                    .findFirst().map(S47RequestBodyBO.HouseAreaBO::getAreaId).ifPresent(far::setAreaId);
            //挂接user_id
            platformUserList.stream().filter(pu->Objects.equals(pu.getPlatformSource(),far.getPlatformSource()))
                    .findFirst().map(S47RequestBodyBO.PlatformUserBO::getUserId).ifPresent(far::setUserId);
            // 设置真实下单会员id
            if(mobileUserIdMap.get(far.getMobile()) != null){
                far.setUserId(mobileUserIdMap.get(far.getMobile()));
            }
            //挂接ppid
            upcPpidList.stream().filter(up->Objects.equals(up.getUpc(),far.getUpc()))
                    .findFirst().map(S47RequestBodyBO.UpcPpidBO::getPpriceid).ifPresent(far::setPpid);
            //挂接订单详情id upc相同情况处理
            existsOrderUpcList.stream().filter(eou->Objects.equals(eou.getOrderCode(),far.getOrderCode()))
                    .filter(eou->Objects.equals(eou.getUpc(),far.getUpc()))
                    .filter(eou->frezzingAndReleases.stream().noneMatch(far2->Objects.equals(eou.getId(),far2.getOrderDetailId())))
                    .findFirst()
                    .ifPresent(eou ->{
                        //释放的时候,只能整行释放
                        if(far.getQty()<0 && far.getQty()+eou.getQuantity() != 0){
                            far.setQty(-eou.getQuantity());
                        }
                        far.setOrderDetailId(eou.getId());
                    });
        }
    }

    /**
     * 插入不存在订单详情信息
     * @param frezzingAndReleases
     * @param allOrderIds
     */
    private void insertOrUpdateOrderDetails(List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> frezzingAndReleases, Set<String> allOrderIds) {
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> insertOrderDetails = frezzingAndReleases.stream()
                //商品详细信息不存在,进行插入 orderCode upc 进行判断
                .filter(far -> CommonUtil.isNullOrZero(far.getOrderDetailId()))
                //必须为占用库存才进行插入
                .filter(far-> DecideUtil.isNull(far.getQty(),0)>0)
                .collect(Collectors.toList());
        //订单详情存在且占用库存且sub_id为空 更新订单详情信息并软删不存在的订单详情,更新的同时设置is_del为0 todo
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> updateOrderDetails = frezzingAndReleases.stream()
                //商品详细信息不存在,进行插入 orderCode upc 进行判断
                .filter(far -> CommonUtil.isNotNullZero(far.getOrderDetailId())).filter(far-> DecideUtil.isNull(far.getQty(),0)>0)
                .filter(far->far.getSubId() == null)
                .collect(Collectors.toList());
        if(!insertOrderDetails.isEmpty()){
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.insertOrderDetails(insertOrderDetails)).commit();
        }
        if(!updateOrderDetails.isEmpty()){
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.updateOrderDetails(updateOrderDetails,allOrderIds)).commit();
        }


    }

    /**
     * 插入不存在的订单信息
     * @param orderGoodMap 解密的实体
     * @param tenantId 订单map
     */
    private void insertOrUpdateOrders(Map<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>> orderGoodMap, Integer tenantId) {
        //插入订单信息
        Set<Map.Entry<String, List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO>>> entries = orderGoodMap.entrySet();
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> insertOrders = entries.stream()
                .map(entry->entry.getValue().stream().findFirst()
                        //订单不存在 获取第一个订单详情信息
                        .filter(far->!Boolean.TRUE.equals(far.getSavedOrder()))
                        //必须为占用库存
                        .filter(far->DecideUtil.isNull(far.getQty(),0)>0))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        //已存在且为占用库存且sub_id为空 可以进行更新存在的订单信息
        List<S47RequestBodyBO.InventoryFrezzingAndReleaseBO> updateOrders = entries.stream()
                .map(entry -> entry.getValue().stream().findFirst()
                        //订单存在
                        .filter(far -> Boolean.TRUE.equals(far.getSavedOrder()))
                        //必须为占用库存
                        .filter(far -> DecideUtil.isNull(far.getQty(), 0) > 0)
                        //sub_id为空
                        .filter(far -> far.getSubId() == null))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        //插入订单和详情不需要放到一个事务中,允许部分成功
        if(!insertOrders.isEmpty()){
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.insertOrders(insertOrders, tenantId)).commit();
        }
        if (!updateOrders.isEmpty()){
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantClientMapper.updateOrders(updateOrders, tenantId)).commit();
        }
    }

    /**
     * 保存商品信息
     * @param m07RequestBody
     * @param decryptBody
     */
    private void saveMaster(M07RequestBodyBO m07RequestBody, DecryptBodyBO decryptBody) {
        if(m07RequestBody.getMaster() != null){
            //查询是否存在对应的数据,保存主商品数据
            //先同步到商品表 校验商品是否已经存在,存在进行更新操作 ? 更新不为空的字段,为空的字段必须存在操作里面
            Integer productId = bzTenantClientMapper.selectProductId(m07RequestBody.getMaster(), decryptBody.getTenantId());
            if(productId == null){
                //由使用的地方指定库
                bzTenantClientMapper.insertProduct(m07RequestBody.getMaster(), decryptBody.getTenantId());
            }else{
                //由使用的地方指定库
                m07RequestBody.getMaster().setId(productId);
                bzTenantClientMapper.updateProduct(m07RequestBody.getMaster(), decryptBody.getTenantId());
            }
        }
    }

    /**
     * 保存商品规格
     * @param m07RequestBody
     * @param decryptBody
     */
    private void saveVariants(M07RequestBodyBO m07RequestBody, DecryptBodyBO decryptBody) {
        if (CollUtil.isNotEmpty(m07RequestBody.getVariants())) {
            //同步到商品详情表 校验商品详情是否已经存在,存在进行更新操作 ? 更新不为空的字段,为空的字段必须存在操作里面
            List<String> variantIds = bzTenantClientMapper.listVariantId(m07RequestBody.getVariants(), decryptBody.getTenantId());
            //拆分为更新和拆入的对象
            List<VariantsBO> insertVariants = new LinkedList<>();
            List<VariantsBO> updateVariants = new LinkedList<>();
            for (VariantsBO variant : m07RequestBody.getVariants()) {
                //已经预处理这里properties不为空
                if (variantIds.contains(variant.getProperties().getBrandSkuCode())) {
                    updateVariants.add(variant);
                } else {
                    insertVariants.add(variant);
                }
            }

            if (!insertVariants.isEmpty()) {
                //由使用的地方指定库
                bzTenantClientMapper.insertVariants(insertVariants, m07RequestBody.getMaster(), decryptBody.getTenantId());
            }
            if (!updateVariants.isEmpty()) {
                //由使用的地方指定库
                bzTenantClientMapper.updateVariants(updateVariants, m07RequestBody.getMaster(), decryptBody.getTenantId());
            }
        }
    }

    /**
     * 校验并预处理同步数据
     * @param m07RequestBody 请求体
     * @param decryptBody 解密对象
     * @return
     */
    private static BzResult<Object> checkAndPreDealProductInfo(M07RequestBodyBO m07RequestBody, DecryptBodyBO decryptBody) {
        if(CollUtil.isNotEmpty(m07RequestBody.getVariants())){
            List<VariantsBO> variants = m07RequestBody.getVariants().stream()
                    .peek(variant -> {
                        if(CollUtil.isNotEmpty(variant.getFragments())){
                            variant.getFragments().stream().map(fragment -> fragment.getProperties()).filter(Objects::nonNull)
                                    .map(properties -> properties.getMarketClassification()).filter(StrUtil::isNotBlank)
                                    .findFirst().ifPresent(mcf -> {
                                        if(variant.getProperties() != null){
                                            variant.getProperties().setMarketClassification(mcf);
                                        }
                                    });
                        }
                    })
                    //过滤不存在唯一属性的商品规格
                    .filter(variant -> !Optional.ofNullable(variant.getProperties()).map(VariantsBO.PropertiesBO::getBrandSkuCode)
                            .filter(StrUtil::isNotBlank).isPresent()).collect(Collectors.toList());
            Optional.of(variants).filter(CollUtil::isNotEmpty).ifPresent(vars->{
                StaticLog.error("brandSkuCode为空无法保存的商品规格:{}", JSON.toJSONString(vars));
                m07RequestBody.getVariants().removeAll(vars);
            });
        }
        if(m07RequestBody.getMaster() != null && StrUtil.isBlank(m07RequestBody.getMaster().getProductCode())){
            StaticLog.error("productCode为空无法保存的主商品:{}",JSON.toJSONString(m07RequestBody.getMaster()));
            m07RequestBody.setMaster(null);
        }
        if(m07RequestBody.getMaster() == null && CollUtil.isEmpty(m07RequestBody.getVariants())){
            //主商品信息和商品规格不能同时为空
            StaticLog.error("主商品信息和商品规格不能同时为空,内容:{}",decryptBody.getDecryptBody());
            return BzResult.error(BzResultCode.MISSING_PARAMETERS_1006,"主商品信息和商品规格不能同时为空");
        }
        return BzResult.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> updateRefundOrderLocalInfo(String transactionNumber) {
        if (StrUtil.isBlank(transactionNumber)) {
            return R.error("交易单号不能为空");
        }

        // 使用 bzTenantSalesOrderService 的 lambda 更新字段
        bzTenantSalesOrderService.lambdaUpdate()
                .eq(BzTenantSalesOrder::getTransactionNumber, transactionNumber)
                .isNull(BzTenantSalesOrder::getSubId)
                .set(BzTenantSalesOrder::getCreateTime, new Date())
                .update();

        // 批量更新退款订单本地信息
        bzTenantClientMapper.batchUpdateRefundOrderLocalInfo(Collections.singletonList(transactionNumber));
        return R.success(transactionNumber);
    }

}
