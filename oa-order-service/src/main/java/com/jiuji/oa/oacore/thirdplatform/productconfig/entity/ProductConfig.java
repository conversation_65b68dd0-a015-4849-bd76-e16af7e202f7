/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.common.util.FieldModified;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商户配置Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@TableName("third_platform_product_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "商品关系配置")
public class ProductConfig extends Model<ProductConfig> {
    private static final long serialVersionUID = 2L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编码
     */
    private String tenantCode;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    private String platCode;

    /**
     * 平台商品spuid
     */
    @FieldModified(displayName = "平台商品")
    private String productCode;

    /**
     * 平台skuid
     */
    @FieldModified(displayName = "skuid")
    @TableField("sku_id")
    private String skuId;

    /**
     * 平台售价
     */
    @FieldModified(displayName = "平台售价")
    private BigDecimal platformCost;

    /**
     * 本地ppid
     */
    @FieldModified(displayName = "ppid")
    private Integer ppriceid;

    /**
     * 价格分摊比（下单使用）
     */
    @FieldModified(displayName = "价格分摊比")
    private Double priceSplit;

    /**
     * 同步开关
     */
    @FieldModified(displayName = "同步开关")
    private boolean syncOff;


    /**
     * 赠品开关
     */
    @FieldModified(displayName = "赠品开关")
    private boolean giftOff;

    /**
     * 同步库存计算方式
     */
    @FieldModified(displayName = "同步库存计算方式")
    private Integer syncType;

    /**
     * 同步系数
     */
    @FieldModified(displayName = "同步系数")
    private Double syncRatio;

    /**
     * 同步上限
     */
    @FieldModified(displayName = "同步上限")
    private Integer syncLimit;

    /**
     * 是否优先同步
     */
    @FieldModified(displayName = "是否优先同步")
    private Boolean syncFirst;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 默认0 0是普通商品 1是良品 抖音有效
     */
    private Integer type;

    /**
     * 良品mkcid
     */
    private Integer mkcId;

    /**
     * 库存锁定标识 1是锁定状态
     */
    @FieldModified(displayName = "库存是否锁定")
    private Boolean libraryLock;

    /**
     * 良品是否售出状态 1是已售出
     */
    private Integer sellType;

    /**
     * 团购: 规则码
     * 抖店: 良品记录预占的商品id
     */
    @TableField("rule_code")
    @FieldModified(displayName = "规则编码")
    private String ruleCode;

    /**
     * 优惠码金额
     */
    @TableField("coupon_price")
    @FieldModified(displayName = "优惠码金额")
    public BigDecimal couponPrice;
    /**
     * 规则码
     */
    @TableField("product_name")
    private String productName;
    /**
     * 配置标签
     */
    @TableField("label")
    @FieldModified(displayName = "配置标签")
    private Integer label;

    /**
     * 服务ppid
     */
    @TableField(value = "service_ppid",strategy = FieldStrategy.IGNORED)
    @FieldModified(displayName = "服务ppid")
    private String servicePpid;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    @FieldModified(displayName = "优惠金额")
    private BigDecimal discountAmount;


    @TableLogic
    private Boolean isDel;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 客户实付收款方式
     */
    @TableField("actual_payment_type")
    @FieldModified(displayName = "客户实付收款方式")
    private String actualPaymentType;
}
