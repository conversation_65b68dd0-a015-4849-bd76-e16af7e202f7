package com.jiuji.oa.oacore.electronicTicket.enums;

import com.jiuji.oa.oacore.oaorder.enums.CodeMessageEnumInterface;
import com.jiuji.oa.oacore.oaorder.enums.SubDeliveryEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @author: gengjiaping
 * @date: 2019/11/27
 */
@AllArgsConstructor
@Getter
public enum DeliveryEnum implements CodeMessageEnumInterface{
    DELIVERY_ONE(1,"到店自取"),
    DELIVERY_TWO(2,"九机快送"),
    DELIVERY_THREE(3,"自提点"),
    DELIVERY_FOUR(4,"快递运输"),
    DELIVERY_FIVE(5,"加急配送"),
    DELIVERY_SIX(6,"体验店"),
    DELIVERY_SEVEN(7,"第三方派送");
    /**
     * 编码
     */
    private Integer code;
    /**
     * 对应编码信息
     */
    private String message;

    public static String valueOfByCode(Integer code) {
        if(code==null){
            return "";
        }
        for (SubDeliveryEnum enumConstant : SubDeliveryEnum.class.getEnumConstants()) {
            if (Objects.equals(enumConstant.getCode(), code)) {
                return enumConstant.getMessage();
            }
        }
        return "";
    }
}
