package com.jiuji.oa.oacore.subRecommended.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商品绑定服务表(SubRecommendedProductServiceConfig)表实体类
 *
 * <AUTHOR>
 * @since 2024-04-24 14:49:05
 */
@Data
@Accessors(chain = true)
@TableName("sub_recommended_product_service_config")
public class SubRecommendedProductServiceConfig  {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ppid
     */
    @TableField("ppid")
    private Integer ppid;
    /**
     * 关联套餐(sub_recommended_package_config)id
     * */
    @TableField("fk_package_id")
    private Integer fkPackageId;
    /**
     * 关联分类(sub_recommended_category_config) 的 id
     * */
    @TableField("fk_category_id")
    private Integer fkCategoryId;
    /**
     * sub_recommended_product_config 表的主键
     */
    @TableField("fk_product_id")
    private Integer fkProductId;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableLogic
    @TableField("is_del")
    private Integer isDel;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 绑定分类配置id(虚拟)
     */
    @TableField(exist = false)
    private String fkCategoryUuid;
    /**
     * 绑定套餐配置id(虚拟)
     */
    @TableField(exist = false)
    private String fkPackageUuid;

    /**
     * 虚拟关联主键
     */
    @TableField(exist = false)
    private String fkProductUuid;

}

