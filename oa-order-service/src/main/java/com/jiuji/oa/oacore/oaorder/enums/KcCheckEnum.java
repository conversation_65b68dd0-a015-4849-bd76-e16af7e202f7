package com.jiuji.oa.oacore.oaorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: <订单库存状态枚举类>
 * translation: <Order stock status enumeration class>
 *
 * <AUTHOR>
 * @date 2019/11/13
 * @since 1.0.0
 */
@AllArgsConstructor
@Getter
public enum KcCheckEnum implements CodeMessageEnumInterface {

    /**
     * 库存状态 编码-信息
     */
    KC_CHECK_WAITING_STOCKING(null, "等待备货"),
    KC_CHECK_NOT_STOCKED(0, "未备货"),
    KC_CHECK_SUBMITTED(1, "已提交"),
    KC_CHECK_ARRIVED(2, "已到货"),
    KC_CHECK_IN_STOCK(3, "库存"),
    KC_CHECK_DELETED(4, "已删除"),
    KC_CHECK_COMPLETED(5, "已完成"),
    KC_CHECK_AFTER(6, "售后"),
    KC_CHECK_NOT_ISSUED(7, "未发出"),
    KC_CHECK_ARREARS(8, "欠款"),
    KC_CHECK_WAITING_REPLACEMENT(9, "待换机"),
    KC_CHECK_ON_THE_WAY(10, "在途"),
    KC_CHECK_INSIDE_COMPLETED(11, "内完成"),
    KC_CHECK_AFTER_INSIDE_COMPLETED(12, "内完成售后"),
    KC_CHECK_AFTER_TWO(13, "售后2"),
    KC_CHECK__RETURN(14, "退货中");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
