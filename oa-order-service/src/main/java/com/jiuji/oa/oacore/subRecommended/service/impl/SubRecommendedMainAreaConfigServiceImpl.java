package com.jiuji.oa.oacore.subRecommended.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedMainAreaConfig;
import com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedMainAreaConfigMapper;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedMainAreaConfigService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

@Service
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class SubRecommendedMainAreaConfigServiceImpl extends ServiceImpl<SubRecommendedMainAreaConfigMapper, SubRecommendedMainAreaConfig> implements SubRecommendedMainAreaConfigService {
}
