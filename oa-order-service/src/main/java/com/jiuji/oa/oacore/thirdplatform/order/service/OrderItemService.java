package com.jiuji.oa.oacore.thirdplatform.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ProductKcBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem;

import java.util.List;

/**
 * 订单管理接口类
 *
 * <AUTHOR>
 */
public interface OrderItemService extends IService<OrderItem> {

    /**
     * 根据订单表ID查询订单明细
     *
     * @param outId
     * @return
     */
    List<OrderItem> listOrderItemByOutId(Integer outId);


    /**
     * 根据订单表ID查询订单明细
     *
     * @param outId
     * @return
     */
    List<OrderItem> listOrderItemByOutIdV2(Integer outId,String platCode);

    /**
     * 根据订单表IDList查询订单明细
     *
     * @param outIdList
     * @return
     */
    List<OrderItem> listOrderItemByOutIdList(List<Integer> outIdList);

    /**
     * 批量保存
     *
     * @param itemList
     */
    void saveBatch(List<OrderItem> itemList);

    /**
     * 查询库存数量
     *
     * @param ppidList
     * @param areaId
     * @return
     */
    List<ProductKcBO> getKcCount(List<Integer> ppidList, Integer areaId);

    /**
     * 根据订单表ID查询订单明细
     * @param id
     * @return
     */
    List<OrderItem> listOrderItemByMkcId(Integer id);

    /**
     * 根据订单表outSubOrderId查询订单明细
     * @param outSubOrderId
     * @return
     */
    List<OrderItem> listOrderItemByOutSubOrderId(String outSubOrderId);
}
