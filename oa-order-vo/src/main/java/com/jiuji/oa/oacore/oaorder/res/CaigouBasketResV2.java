package com.jiuji.oa.oacore.oaorder.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 采购单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CaigouBasket对象", description="采购单明细")
public class CaigouBasketResV2 implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer skuId;

    @ApiModelProperty(value = "条码")
    private String barCode;

    private List<String> barCodeList;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格")
    private String productColor;

    @ApiModelProperty(value = "数量")
    private Integer lcount;

    private Integer leftcount;

    private Integer sendcount;

    private Integer toSendcount;

    /**
     * 入库数量
     */
    private Integer inputcount;

    @ApiModelProperty(value = "单价")
    private BigDecimal inprice;

    /**
     * 合计
     */
    private BigDecimal totalPrice;

    private Integer basketId;
}
