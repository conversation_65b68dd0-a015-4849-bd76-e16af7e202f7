package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/1/26
 * @description 官方延保
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("official_insurance")
public class OfficialInsurance extends Model<OfficialInsurance> {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * ppid
     */
    @TableField("ppid")
    private Integer ppid;
    /**
     * 延保名
     */
    @TableField("insurance_name")
    private String insuranceName;
    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Integer deleted;

}
