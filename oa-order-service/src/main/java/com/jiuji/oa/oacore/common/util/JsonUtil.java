package com.jiuji.oa.oacore.common.util;

import com.alibaba.fastjson.serializer.ValueFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final ObjectMapper noFieldMapper=new ObjectMapper();

    private static final ObjectMapper nullIgnoreMapper=new ObjectMapper();


    public static final String SERVICE_EXCEPTION = "服务器异常";

    static {
        noFieldMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        nullIgnoreMapper.registerModule(new JavaTimeModule());
        nullIgnoreMapper.registerModule(new Jdk8Module());
        nullIgnoreMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

    }
    public static <T> T json2Object(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JsonUtil json2Object失败，json串：" + json + " 原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
    }

    public static <T> T json2ObjectIgnoreField(String json, Class<T> clazz) {
        try {
            return noFieldMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JsonUtil json2Object失败，json串：" + json + " 原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
    }

    public static <T> T json2ObjectIgnoreField(String json, TypeReference<T> typeReference) {
        try {
            return noFieldMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("JsonUtil json2Object失败，json串：" + json + " 原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
    }


    /**
     * 序列化java对象
     *
     * @param entity
     * @param <T>
     * @return
     */
    public static <T> String object2Json(T entity) {
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (IOException e) {
            log.error("JsonUtil object2Json，原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
    }

    public static <T> String object2JsonIgnoreNull(T entity) {
        try {
            return nullIgnoreMapper.writeValueAsString(entity);
        } catch (IOException e) {
            log.error("JsonUtil object2Json，原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
    }

    public static <T> T json2Object(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("JsonUtil toCollection，原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);

        }
    }

    /**
     * json string 转换为 map 对象
     */
    public static Map<String, Object> jsonToMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return new HashMap<>();
        }
        Map<String, Object> map;
        try {
            map = objectMapper.readValue(jsonString, new TypeReference<HashMap<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("JsonUtil toCollection，原因：" + e.getMessage(), e);
            throw new CustomizeException(e.getMessage()+ SERVICE_EXCEPTION);
        }
        return map;
    }


    public static <T> String object2JsonNoException(T entity) {
        if (entity == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (IOException e) {
            log.error("JsonUtil object2Json，原因：" + e.getMessage(), e);
            return null;
        }
    }

    public static <T> List<T> listMapToListObject(List<Map<String, String>> listMap, TypeReference<List<T>> typeReference) {
        return noFieldMapper.convertValue(listMap, typeReference);
    }

    public static boolean stringIsJson(String resource) {
        if (StringUtils.isBlank(resource)) {
            return false;
        }
        try {
            objectMapper.readTree(resource);
            return true;
        } catch (JsonParseException e) {
            log.error(resource);
        } catch (Exception e) {
            log.error("JsonUtil stringIsJson，原因：" + e.getMessage(), e);
        }
        return false;
    }

    public static ValueFilter  getIntegerMapValueFilter(Set<String> set) {
        return (object, name, value) -> {
            if (set.contains(name)) {
                if (value == null) {
                    return value;
                }
                Map<Integer, Boolean> map = (HashMap<Integer, Boolean>) value;
                Map<String, Boolean> resultMap = map.entrySet().stream().collect(Collectors.toMap(e -> String.valueOf(e.getKey()), Map.Entry::getValue, (x, y) -> x));
                return resultMap;
            }
            return value;
        };
    }

    public static ValueFilter getDistributionJumpParamFilter() {
        return getDistributionJumpParamFilter(Boolean.FALSE);
    }
    public static ValueFilter getDistributionJumpParamFilter(boolean areaTypeStringFlag) {
        return (object, name, value) -> {
            if (value == null) {
                return value;
            }
            if ("inSourceIdList".equals(name)) {
                List<Long> inSourceIdList = (List<Long>) value;
                List<String> inSourceIdStrList = inSourceIdList.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
                return inSourceIdStrList;
            } else if ("orderDelivery".equals(name)) {
                return String.valueOf(value);
            }else if ("areaType".equals(name) && areaTypeStringFlag) {
                return String.valueOf(value);
            } else if ("serialNumberMarks".equals(name)) {
                List<Long> serialNumberMarks = (List<Long>) value;
                return serialNumberMarks.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
            }
            return value;
        };
    }

}
