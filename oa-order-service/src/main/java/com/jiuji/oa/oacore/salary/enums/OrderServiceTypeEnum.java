package com.jiuji.oa.oacore.salary.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/2/23
 * @description 售后九机服务
 */
@AllArgsConstructor
@Getter
public enum OrderServiceTypeEnum implements CodeMessageEnumInterface {

    /**
     * 售后类型服务
     */
    /**1 意外保一年*/
    YWB(1,"意外保"),
    /**2 意外二年*/
    YB(2,"延保"),
    /**4:电池保两年*/
    DCB(4,"电池保"),
    /** 5 碎屏保一年*/
    SPB(5,"碎屏保"),

    /** 6:进水保*/
    JSB(6,"进水保"),
    /** 7 屏背保*/
    PBB(7,"屏背保"),
    /** 8:以换代修*/
    YHDX(8,"以换代修"),
    /** 9 售后屏幕保*/
    SHPMB(9,"售后屏幕保"),
    /** 10: 两年质保*/
    SHDCB(10,"售后电池保"),
    /** 11 售后后盖险*/
    SHHGX(11,"售后后盖险"),
    /**110：值享换新保  111:意外屏背摄像保 112:意外小部件保 ZHI_XIAN_HUAN_XIN(14,"值享换新保"), */
    YI_WAI_PING_BEI(12,"意外屏背摄像保"),
    YI_WAI_XIAO_BU_JIAN(13,"意外小部件保");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 编码对应信息
     */
    private final String message;

}
