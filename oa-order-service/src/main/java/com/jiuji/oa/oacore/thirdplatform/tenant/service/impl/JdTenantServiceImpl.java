package com.jiuji.oa.oacore.thirdplatform.tenant.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.tenant.bo.JdTokenBO;
import com.jiuji.oa.oacore.thirdplatform.tenant.bo.TenantSearchBO;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.JdTenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser;
import com.jiuji.oa.oacore.thirdplatform.tenant.mapper.JdTenantMapper;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.JdTenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.ThirdPlatformTenantUserService;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantVO;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.ThirdPlatformTenantUserVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/23 14:02
 * @Description 京东商户配置查询实现类
 */
@Service
@Slf4j
public class JdTenantServiceImpl extends ServiceImpl<JdTenantMapper, JdTenant> implements JdTenantService {


    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ThirdPlatformTenantUserService thirdPlatformTenantUserService;
    @Resource
    private OaSysConfigService sysConfigService;

    /**
     * @param tenantCode 平台编码
     * @return
     */
    @Override
    public JdTenant getOneTenantByJd(String tenantCode) {
        QueryWrapper<JdTenant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_code", tenantCode);
        List<JdTenant> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * @param search 查询参数
     * @return
     */
    @Override
    public Page<TenantVO> listByJdPage(TenantSearchBO search) {
        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
        boolean mdspFlag = currentStaffId.getRank().contains("mdsp");
        Page<TenantVO> page = new Page<>(search.getCurrent(), search.getSize());
        List<TenantVO> list = baseMapper.jdTenantList(search, page);
        Map<String, String> jdTokenExpirationTimeMap = getJdTokenExpirationTime();
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> idList = list.stream().map(TenantVO::getId).collect(Collectors.toList());
            List<SysConfigVo> sysConfigVoList = sysConfigService.getListByCoode(SysConfigConstant.PLATE_FORM);
            Map<String, String> xtenantNameMap = sysConfigVoList.stream().collect(Collectors.toMap(SysConfigVo::getValue, SysConfigVo::getName));
            List<ThirdPlatformTenantUser> userList = thirdPlatformTenantUserService.getTenantUserListByTenantId(ThirdPlatformCommonConst.THIRD_PLAT_JD,idList);
            Map<Integer, List<ThirdPlatformTenantUserVO>> tenantUserMap = userList.stream()
                    .map(v -> ThirdPlatformTenantUserVO.builder().id(v.getId())
                            .thirdPlatformTenantId(v.getThirdPlatformTenantId())
                            .userId(v.getUserId()).xtenant(Convert.toStr(v.getXtenant()))
                            .xtenantName(xtenantNameMap.get(Convert.toStr(v.getXtenant())))
                            .platKemu(v.getPlatKemu())
                            .refundKemu(v.getRefundKemu())
                            .venderKemu(v.getVenderKemu())
                            .governmentSubsidyKemu(v.getGovernmentSubsidyKemu())
                            .platCode(v.getPlatCode()).build())
                    .collect(Collectors.groupingBy(ThirdPlatformTenantUserVO::getThirdPlatformTenantId));
            for (TenantVO item : list) {
                item.setPlatCode("JD");
                item.setTenantUserList(tenantUserMap.get(item.getId()));
                item.setPlatform(PlatfromEnum.getCodeByName(item.getPlatCode()));
                item.setTokenExpirationDate(jdTokenExpirationTimeMap.getOrDefault(item.getAppKey(),""));
                if (!mdspFlag){
                    item.setAppKey("************");
                    item.setAppSecret("************");
                }
            }
            page.setRecords(list);
        }
        return page;
    }

    /**
     * 查询京东token过期时间
     * @return
     */
    private Map<String, String> getJdTokenExpirationTime() {
        try {
            String baseUrl = Optional.ofNullable(SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.IN_WCF_HOST))
                    .map(R::getData).orElse("");
            if (StringUtils.isBlank(baseUrl)) {
                return new HashMap<>();
            }
            String url = baseUrl + "/oaApi.svc/rest/GetJingDongDaojiaToken";
            String result = HttpUtil.createPost(url).execute().body();
            if (StringUtils.isBlank(result)) {
                return new HashMap<>();
            }
            JSONObject resultJson = JSON.parseObject(result);
            String tokenStr = resultJson.getString("data");
            List<JdTokenBO> tokenList = JSON.parseArray(tokenStr, JdTokenBO.class);
            if (CollectionUtils.isNotEmpty(tokenList)) {
                return tokenList.stream().collect(Collectors.toMap(JdTokenBO::getAppKey, v -> StrUtil.sub(StrUtil.replace(v.getExpiresDate(), "T", " "), 0,19), (v1, v2) -> v1));
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("查询京东token异常！", null, e, null);
        }
        return new HashMap<>();
    }
}
