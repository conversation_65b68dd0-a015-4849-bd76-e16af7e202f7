package com.jiuji.oa.oacore.thirdplatform.baozun.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.stock.vo.response.IsVirtualProductResVo;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.csharp.cloud.CsharpInWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.BaoZhunBatchDiaoBoReq;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.po.ProductMkc;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.DecryptBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.eapi.E02ReqBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.BaozunE01OrderTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BaozunTenantPurchaseDetail;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BaozunTenantPurchaseOrder;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantCloudStoreService;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.IBaozunTenantPurchaseDetailService;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.IBaozunTenantPurchaseOrderService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class BatchDiaoBoProcessor {

    // 预加载的数据 Map
    private final Map<String, Integer> areaIdMap;
    private final Map<String, Integer> ppidMap;
    private final Map<Integer, ProductSimpleBO> pinfoMap;
    private final Map<String, ProductMkc> mkcImeiMap;
    private final Map<Integer, IsVirtualProductResVo> virtualProductMap;
    private final DecryptBodyBO decryptBody;


    // 构造函数注入依赖 (如果BatchDiaoBoProcessor本身不是Spring Bean，则外部创建时传入Map)
    public BatchDiaoBoProcessor(Map<String, Integer> areaIdMap,
                                Map<String, Integer> ppidMap,
                                Map<Integer, ProductSimpleBO> pinfoMap,
                                Map<String, ProductMkc> mkcImeiMap,
                                Map<Integer, IsVirtualProductResVo> virtualProductMap,
                                DecryptBodyBO decryptBody
    ) {
        this.areaIdMap = areaIdMap;
        this.ppidMap = ppidMap;
        this.pinfoMap = pinfoMap;
        this.mkcImeiMap = mkcImeiMap;
        this.virtualProductMap = virtualProductMap;
        this.decryptBody = decryptBody;
    }

    public void processGroupedInboundConfirms(Map<String, List<E02ReqBodyBO.InboundConfirm>> groupedByInboundNo) {
        if (groupedByInboundNo == null || groupedByInboundNo.isEmpty()) {
            log.info("没有需要处理的分组入库确认数据。");
            return;
        }

        // 此行号生成器应在更合适的范围初始化，例如，如果每个调拨单的行号从1开始，则在forEach内创建
        // AtomicInteger lineNumber = new AtomicInteger(0);


        groupedByInboundNo.forEach((transferNumber, group) -> {
            AtomicInteger lineNumber = new AtomicInteger(0); // 每个调拨单的行号从1开始

            if (CollectionUtils.isEmpty(group)) {
                log.warn("调拨单 '{}' 的分组为空，跳过处理。", transferNumber);
                return;
            }

            List<BaoZhunBatchDiaoBoReq.Detail> wcfDetailsForThisTransfer = new ArrayList<>();
            BaozunTenantPurchaseOrder purchaseOrderToSave = new BaozunTenantPurchaseOrder().setInboundNo(transferNumber)
                    .setFkTenantId(this.decryptBody.getTenantId()).setIsDel(Boolean.FALSE)
                    .setPlanInboundTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)))
                    .setCreateTime(LocalDateTime.now());
            List<BaozunTenantPurchaseDetail> purchaseDetailsToSave = new ArrayList<>();
            boolean isTransferOrderValidForWCF = true; // 初始假设调拨单对WCF有效
            StringJoiner validationIssuesLog = new StringJoiner("; ", "调拨单 '" + transferNumber + "' 数据校验问题: [", "]");
            validationIssuesLog.setEmptyValue("");


            for (E02ReqBodyBO.InboundConfirm ibc : group) {
                if (ibc == null || ibc.getInboundConfirmLines() == null ||
                        CollectionUtils.isEmpty(ibc.getInboundConfirmLines().getInboundConfirmLine())) {
                    log.debug("调拨单 '{}' 中的一个 InboundConfirm 对象或其行项目为空，跳过此对象。", transferNumber);
                    continue;
                }
                purchaseOrderToSave.setStoreCode(ibc.getFromOwner()).setPlatformNo(ibc.getPlatformNo());
                Integer inAreaIdInt = areaIdMap.get(ibc.getToLocation());
                Integer outAreaIdInt = areaIdMap.get(ibc.getFromLocation());
                boolean currentIbcLocationsValid = true;

                if (inAreaIdInt == null) {
                    validationIssuesLog.add("入向门店ID (toLocation: '" + ibc.getToLocation() + "') 未找到");
                    isTransferOrderValidForWCF = false;
                    currentIbcLocationsValid = false;
                }
                if (outAreaIdInt == null) {
                    validationIssuesLog.add("出向门店ID (fromLocation: '" + ibc.getFromLocation() + "') 未找到");
                    isTransferOrderValidForWCF = false;
                    currentIbcLocationsValid = false;
                }

                for (E02ReqBodyBO.InboundConfirmLine line : ibc.getInboundConfirmLines().getInboundConfirmLine()) {
                    boolean currentLineDataValidForWCF = currentIbcLocationsValid; // 继承父级ibc的有效性

                    if (line == null || StringUtils.isBlank(line.getUpc()) ||
                            line.getInboundInvLineConfirms() == null ||
                            CollectionUtils.isEmpty(line.getInboundInvLineConfirms().getInboundInvLineConfirm())) {
                        log.debug("调拨单 '{}', UPC '{}': 行项目或其库存确认信息为空，不为此行生成采购明细。", transferNumber, line != null ? line.getUpc() : "未知UPC");
                        continue; // 不生成任何明细，也不影响WCF有效性判断（除非这是唯一行）
                    }

                    Integer ppidInt = ppidMap.get(line.getUpc());
                    if (ppidInt == null) {
                        validationIssuesLog.add("PPID (upc: '" + line.getUpc() + "') 未找到");
                        isTransferOrderValidForWCF = false;
                        currentLineDataValidForWCF = false;
                        // 仍然尝试保存PurchaseDetail，但标记为不完整/错误
                    }

                    IsVirtualProductResVo virtualInfo = (ppidInt != null) ? virtualProductMap.get(ppidInt) : null;
                    if (virtualInfo != null && Boolean.TRUE.equals(virtualInfo.getIsVirtual())) {
                        log.warn("调拨单 '{}', ppid {} (upc {}): 商品为虚拟商品，不为此行生成WCF明细，但可能保存采购记录。", ppidInt, line.getUpc(), transferNumber);
                    }

                    ProductSimpleBO pInfo = (ppidInt != null) ? pinfoMap.get(ppidInt) : null;
                    if (pInfo == null && ppidInt != null) { // 只有当ppidInt有效但pInfo找不到时才认为是错误
                        validationIssuesLog.add("PPID " + ppidInt + " (upc: '" + line.getUpc() + "') 商品信息未找到");
                        isTransferOrderValidForWCF = false;
                        currentLineDataValidForWCF = false;
                    }
                    boolean isMobile = (pInfo != null) && Boolean.TRUE.equals(pInfo.getIsMobile());


                    for (E02ReqBodyBO.InboundInvLineConfirm invConfirm : line.getInboundInvLineConfirms().getInboundInvLineConfirm()) {
                        if (invConfirm == null) continue;

                        boolean hasSns = invConfirm.getInboundSnLineConfirms() != null &&
                                CollectionUtils.isNotEmpty(invConfirm.getInboundSnLineConfirms().getInboundSnLineConfirm());
                        boolean lineItemMismatch = false;

                        if (pInfo != null) { // 只有在pInfo存在时才进行大小件匹配校验
                            if ((hasSns && !isMobile) || (!hasSns && isMobile)) {
                                validationIssuesLog.add("PPID [" + ppidInt + "] (upc: '" + line.getUpc() + "') 大小件不匹配 (isMobile=" + isMobile + ", hasSns=" + hasSns + ")");
                                isTransferOrderValidForWCF = false; // 大小件不匹配，整个调拨单对WCF无效
                                currentLineDataValidForWCF = false;
                                lineItemMismatch = true;
                            }
                        } else if (ppidInt != null) { // pInfo为null但ppid已知，无法判断大小件，也视为一个问题
                            currentLineDataValidForWCF = false; // 因为pInfo缺失
                        }


                        BaozunTenantPurchaseDetail purchaseDetail = new BaozunTenantPurchaseDetail();
                        purchaseDetail.setCreateTime(LocalDateTime.now())
                                .setLineNo(Convert.toStr(lineNumber.incrementAndGet()))
                                .setUpc(line.getUpc())
                                .setIsDel(false);

                        // 初始状态和完成状态，后续根据校验结果调整
                        int lineStatus = Convert.toInt(invConfirm.getInvStatus());

                        if (hasSns) { // 大件处理逻辑
                            purchaseDetail.setOrderType(Convert.toInt(BaozunE01OrderTypeEnum.TWO.getCode()));
                            List<String> snListForThisInvConfirm = new ArrayList<>();
                            boolean allSnsValidForWCFInThisInvConfirm = currentLineDataValidForWCF;

                            for (E02ReqBodyBO.InboundSnLineConfirm snConfirm : invConfirm.getInboundSnLineConfirms().getInboundSnLineConfirm()) {
                                if (snConfirm == null || StringUtils.isBlank(snConfirm.getSn())) continue;
                                snListForThisInvConfirm.add(snConfirm.getSn());

                                ProductMkc mkcInfo = mkcImeiMap.get(snConfirm.getSn());
                                Integer mkcIdForWCF = null;
                                if (mkcInfo == null) {
                                    validationIssuesLog.add("大件库存信息 (sn: '" + snConfirm.getSn() + "') 未找到");
                                    isTransferOrderValidForWCF = false;
                                    allSnsValidForWCFInThisInvConfirm = false;
                                } else {
                                    mkcIdForWCF = mkcInfo.getId(); // 用于WCF Detail
                                    // 假设 ProductMkc 有 ppriceid 字段
                                    purchaseDetail.setPpriceid(ppidInt); // 设置采购明细的ppriceid
                                    if (mkcIdForWCF == null) { // 即使mkcInfo存在，ID也可能为空
                                        validationIssuesLog.add("大件库存信息 (sn: '" + snConfirm.getSn() + "') 的 MKC ID 为空");
                                        isTransferOrderValidForWCF = false;
                                        allSnsValidForWCFInThisInvConfirm = false;
                                    }
                                }
                                // 如果ppidInt或pInfo缺失，也影响此SN的有效性
                                if (ppidInt == null || pInfo == null || lineItemMismatch) {
                                    allSnsValidForWCFInThisInvConfirm = false;
                                }


                                // 仅当此SN及其上下文数据对WCF有效时，才创建WCF Detail
                                if (allSnsValidForWCFInThisInvConfirm && currentIbcLocationsValid
                                        && mkcIdForWCF != null && !lineItemMismatch && ppidInt != null
                                        && pInfo != null && (virtualInfo == null || !virtualInfo.getIsVirtual())) {
                                    wcfDetailsForThisTransfer.add(BaoZhunBatchDiaoBoReq.Detail.builder()
                                            .ppid(ppidInt)
                                            .inAreaId(inAreaIdInt)
                                            .outAreaId(outAreaIdInt)
                                            .num(1) // 每个SN对应一个Detail，数量为1
                                            .ismobile(isMobile) // 应该是true
                                            .mkcId(mkcIdForWCF)
                                            .build());
                                }
                            }
                            purchaseDetail.setSn(String.join(",", snListForThisInvConfirm)); // Hutool: StrUtil.join(",", snList)
                            purchaseDetail.setQty(snListForThisInvConfirm.size()); // 大件数量是SN的数量
                            if (ppidInt != null) purchaseDetail.setPpriceid(ppidInt); // 示例：大件也用ppid

                        } else { // 小件处理逻辑 (无SN)
                            purchaseDetail.setOrderType(Convert.toInt(BaozunE01OrderTypeEnum.ONE.getCode()));
                            int quantity = 0;
                            try {
                                quantity = Integer.parseInt(invConfirm.getQtyReceived());
                                purchaseDetail.setQty(quantity);
                            } catch (NumberFormatException e) {
                                validationIssuesLog.add("无效的数量 (qtyReceived: '" + invConfirm.getQtyReceived() + "', upc: '" + line.getUpc() + "')");
                                isTransferOrderValidForWCF = false;
                                currentLineDataValidForWCF = false;
                            }
                            // 假设小件的 ppriceid 直接用 ppid
                            if (ppidInt != null) {
                                purchaseDetail.setPpriceid(ppidInt);
                            } else { // ppid缺失是严重问题
                                currentLineDataValidForWCF = false;
                            }
                            if (lineItemMismatch) { // 如果前面判定了大小件不匹配
                                currentLineDataValidForWCF = false;
                            }


                            // 仅当此小件行及其上下文数据对WCF有效时，才创建WCF Detail
                            if (currentLineDataValidForWCF && currentIbcLocationsValid && ppidInt != null
                                    && pInfo != null && quantity > 0 && !lineItemMismatch
                                    && (virtualInfo == null || !Boolean.TRUE.equals(virtualInfo.getIsVirtual()))) {
                                wcfDetailsForThisTransfer.add(BaoZhunBatchDiaoBoReq.Detail.builder()
                                        .ppid(ppidInt)
                                        .inAreaId(inAreaIdInt)
                                        .outAreaId(outAreaIdInt)
                                        .num(quantity)
                                        .ismobile(isMobile) // 应该是false
                                        .mkcId(null)
                                        .build());
                            }
                        }

                        purchaseDetail.setStatus(lineStatus);
                        purchaseDetail.setIsComplete(0);
                        purchaseDetailsToSave.add(purchaseDetail);
                    }
                }
            }

            // 保存采购订单和明细，无论WCF调用是否发生
            savePurchaseOrderAndDetails(purchaseOrderToSave, purchaseDetailsToSave);


            if (!isTransferOrderValidForWCF) {
                String logMessageContent = validationIssuesLog.toString();
                if (StringUtils.isNotBlank(logMessageContent)) {
                    log.warn("因数据校验问题，调拨单 '{}' 不会发送给WCF. 详情: {}",
                            transferNumber, logMessageContent);
                    SpringUtil.getBean(BzTenantCloudStoreService.class).configErrorNotice(null, transferNumber, logMessageContent);
                } else if (wcfDetailsForThisTransfer.isEmpty() && !group.isEmpty()) {
                    log.warn("调拨单 '{}' 未能生成任何有效的WCF明细 (可能所有商品均为虚拟，或所有行都有问题)，不发送给WCF。", transferNumber);
                } else {
                    log.warn("调拨单 '{}' 因未指明的数据校验问题，不发送给WCF。", transferNumber);
                }
                return; // 不调用WCF
            }

            if (wcfDetailsForThisTransfer.isEmpty()) {
                log.info("调拨单 '{}' 未生成任何适用于WCF的明细 (例如，所有项目均为虚拟商品或被过滤)。跳过WCF调用。", transferNumber);
                return;
            }

            BaoZhunBatchDiaoBoReq req = BaoZhunBatchDiaoBoReq.builder()
                    .input(BaoZhunBatchDiaoBoReq.Input.builder()
                            .title(StrUtil.format("泛中台调拨单（{}）", transferNumber))
                            .transferNumber(transferNumber)
                            .details(wcfDetailsForThisTransfer)
                            .build())
                    .build();

            log.info("已成功为调拨单 '{}' 构建WCF请求，包含 {} 条明细。准备调用WCF服务。", transferNumber, wcfDetailsForThisTransfer.size());
            callBaoZhunBatchDiaoBoService(transferNumber, req);
        });
    }

    private void savePurchaseOrderAndDetails(BaozunTenantPurchaseOrder purchaseOrder, List<BaozunTenantPurchaseDetail> details) {
        final IBaozunTenantPurchaseOrderService orderService = SpringUtil.getBean(IBaozunTenantPurchaseOrderService.class);
        final IBaozunTenantPurchaseDetailService detailService = SpringUtil.getBean(IBaozunTenantPurchaseDetailService.class);
        if(orderService.lambdaQuery().eq(BaozunTenantPurchaseOrder::getInboundNo, purchaseOrder.getInboundNo()).count() >0) {
            // 无需再报错
            return;
        }
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            orderService.save(purchaseOrder); // 保存主订单并获取ID
            if (purchaseOrder.getId() != null) {
                details.stream().peek(rpd -> rpd.setBaozunPurchaseId(purchaseOrder.getId()))
                        .forEach(detailService::save); // 保存明细
            } else {
                log.error("保存调拨主单 '{}' 后未能获取ID，明细未保存。", purchaseOrder.getInboundNo());
                throw new CustomizeException("保存主订单失败，无法关联明细。");
            }
        }).commit();

        log.info("模拟保存调拨单 '{}' (主单) 及其 {} 条明细", purchaseOrder.getInboundNo(), details.size());
    }


    private void callBaoZhunBatchDiaoBoService(String transferNumber, BaoZhunBatchDiaoBoReq req) {
        try {
            R r = SpringUtil.getBean(CsharpInWcfCloud.class).callBaoZhunBatchDiaoBo(req);
            log.warn("调拨单 '{}' 的WCF服务调用成功。响应: {}", transferNumber, JSON.toJSONString(r));
        } catch (Exception e) {
            RRExceptionHandler.logError("调用宝尊批量调拨服务",  req, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }
}
