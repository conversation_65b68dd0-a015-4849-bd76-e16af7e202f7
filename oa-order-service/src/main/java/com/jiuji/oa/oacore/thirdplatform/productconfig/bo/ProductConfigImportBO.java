/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商户配置Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
public class ProductConfigImportBO implements Serializable {
    private static final long serialVersionUID = 2L;

    /**
     * 商户编码
     */
    private String tenantCode;

    /**
     * 平台商品spuid
     */
    private String productCode;

    /**
     * 平台skuid
     */
    private String skuId;

    /**
     * 本地ppid
     */
    private Integer ppriceid;

    /**
     * 平台售价
     */
    private BigDecimal platformCost;

    /**
     * 价格分摊比（下单使用）
     */
    private Double priceSplit;

    /**
     * 同步开关
     */
    private Integer syncOff;

    /**
     * 同步库存计算方式
     */
    private Integer syncType = 0;

    /**
     * 同步系数
     */
    private Double syncRatio;

    /**
     * 同步上限
     */
    private Integer syncLimit = 0;

    /**
     * 是否优先同步
     */
    private Integer syncFirst;
    /**
     * 配置标签
     */
    private Integer label;

}
