package com.jiuji.oa.oacore.operator.po;

import com.baomidou.mybatisplus.annotation.*;
import com.jiuji.tc.utils.common.FieldModified;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date ${Date} 9:38
 * @Description
 */

/**
 * 运营商配置
 */
@ApiModel(value = "运营商配置")
@Data
@TableName(value = "OperatorBusinessConfig")
public class OperatorBusinessConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "ppriceid")
    @ApiModelProperty(value = "ppid")
    private Integer ppriceid;

    @TableField(value = "category")
    @ApiModelProperty(value = "ppid所属分类")
    private Integer category;

    @TableField(value = "channel")
    @ApiModelProperty(value = "归属渠道id")
    @FieldModified(displayName = "归属渠道")
    private Integer channel;

    @TableField(value = "deductionMoney")
    @ApiModelProperty(value = "扣减运营商预存款")
    @FieldModified(displayName = "扣减运营商预存款")
    private BigDecimal deductionMoney;

    @TableField(value = "commission")
    @ApiModelProperty(value = "预估佣金")
    @FieldModified(displayName = "预估佣金")
    private BigDecimal commission;

    @TableField(value = "agentPoints")
    @ApiModelProperty(value = "代理积分")
    @FieldModified(displayName = "代理积分")
    private BigDecimal agentPoints;

    @TableField(value = "[comment]")
    @ApiModelProperty(value = "备注")
    @FieldModified(displayName = "备注")
    private String comment;

    @TableField(value = "xtenant")
    @ApiModelProperty(value = "租户")
    private Integer xtenant;

    @TableField(value = "authid")
    @ApiModelProperty(value = "授权id")
    private Integer authid;

    @TableField(value = "areaids")
    @ApiModelProperty(value = "办理地区限制")
    private String areaids;

    @TableField(value = "offsetName")
    @ApiModelProperty(value = "抵扣配置名称")
    private String offsetName;

    @TableField(value = "offsetMoney")
    @ApiModelProperty(value = "抵扣金额")
    @FieldModified(displayName = "抵扣金额")
    private BigDecimal offsetMoney;

    @TableField(value = "settlementMoney")
    @ApiModelProperty(value = "结算金额")
    @FieldModified(displayName = "结算金额")
    private BigDecimal settlementMoney;

    @TableField(value = "preAbility")
    @ApiModelProperty(value = "预估产能")
    @FieldModified(displayName = "预估产能")
    private BigDecimal preAbility;

    @TableField(value = "statisticType")
    @ApiModelProperty(value = "销售统计归类")
    private Integer statisticType;

    @TableField(value = "inuser")
    @ApiModelProperty(value = "用户")
    private Integer inuser;

    @TableField(value = "dtime")
    @ApiModelProperty(value = "时间")
    private LocalDateTime dtime;

    @TableField(value = "isdel")
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private Integer isdel;

    @TableField(value = "isCheckActName")
    @ApiModelProperty(value = "是否开启活动名称效验")
    private Boolean ischeckactname;

    @TableField(value = "checkRepeatCount")
    @ApiModelProperty(value = "开卡重复效验次数")
    private Integer checkrepeatcount;

    @TableField(value = "actNames")
    @ApiModelProperty(value = "活动名称集合")
    private String actnames;

    @TableField(value = "financeAccountKinds")
    @ApiModelProperty(value = "记账类型，已弃用")
    private Integer financeaccountkinds;

    @TableField(value = "mustInputImei")
    @ApiModelProperty(value = "是否获取串号")
    private Boolean mustinputimei;

    @ApiModelProperty(value = "版本")
    @TableField(exist = false)
    private LocalDateTime operatorbusinessconfigRv;
    /**
     * 运营商套餐ppid集合
     */
    @TableField(value = "bind_ppids")
    @ApiModelProperty(value = "运营商套餐ppid集合")
    private String bindPpids;
    /**
     * 业绩提成
     */
    @TableField(value = "bounty")
    @ApiModelProperty(value = "业绩提成")
    @FieldModified(displayName = "业绩提成")
    private BigDecimal bounty;
    /**
     * 付款方式
     */
    @TableField(value = "pay_way")
    @ApiModelProperty(value = "付款方式")
    private Integer payWay;

    /**
     * 服务延迟生效时间 1即时生效 2次月生效
     */
    @TableField(value = "service_delay_kind")
    @ApiModelProperty(value = "服务延迟生效时间 1即时生效 2次月生效")
    private Integer serviceDelayKind;

    /**
     * 服务有效时间，多少个月
     */
    @TableField(value = "service_effective_month")
    @ApiModelProperty(value = "服务有效时间，多少个月")
    private Integer serviceEffectiveMonth;

    /**
     * 抵扣方式 0订单收银 1优惠码抵扣
     */
    @TableField(value = "pay_kind")
    @ApiModelProperty(value = "抵扣方式 0订单收银 1优惠码抵扣")
    private Integer payKind;

    /**
     * 用户返现
     * 0-不支持
     * 1-支持
     */
    @TableField(value = "business_cash_back")
    private Boolean refundBusinessType;

    /**
     * 运营商活动编码
     */
    @TableField(value = "operator_code")
    @FieldModified(displayName = "运营商活动编码")
    private String operatorCode;

    /**
     * 实体商品ppid
     */
    @TableField(value = "physical_goods_ppid")
    @FieldModified(displayName = "实体商品ppid")
    private Integer physicalGoodsPpid;
}