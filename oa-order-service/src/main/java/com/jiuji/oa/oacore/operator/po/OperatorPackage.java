package com.jiuji.oa.oacore.operator.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("operatorPackage")
public class OperatorPackage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("package_name")
    private String operatorPackage;

    @TableField("remark")
    private String comment;

    private Integer delFlag;

    @TableField("broadband_user")
    private Integer isbroadband;

    @TableField(exist = false)
    private String isbroadbandString;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    private LocalDateTime createTime;


    @TableField("in_user_id")
    private Integer createUserId;

    @TableField("in_user_name")
    private String createUser;

    @TableField("Phone")
    private String phone;

    @TableField("handle_time")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime handleTime;

    @TableField("service_effective_time")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    @TableField("service_end_time")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String errorReason;

}
