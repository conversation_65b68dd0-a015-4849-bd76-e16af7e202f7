package com.jiuji.oa.afterservice.smallpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.smallpro.dao.TiemoCardUserLogMapper;
import com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog;
import com.jiuji.oa.afterservice.smallpro.service.TiemoCardUserLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Service
public class TiemoCardUserLogServiceImpl extends ServiceImpl<TiemoCardUserLogMapper, TiemoCardUserLog> implements TiemoCardUserLogService {

    @Override
    public TiemoCardUserLog getByIdSqlServer(Integer id) {
        return baseMapper.getByIdSqlServer(id);
    }

    @Override
    public List<TiemoCardUserLog> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

}
