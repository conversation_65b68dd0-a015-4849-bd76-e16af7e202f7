package com.jiuji.oa.oacore.thirdplatform.tenant.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.po.BbsxpUsers;
import com.jiuji.oa.oacore.oaorder.service.BbsxpUsersService;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.thirdplatform.tenant.bo.TenantUserBO;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser;
import com.jiuji.oa.oacore.thirdplatform.tenant.mapper.ThirdPlatformTenantUserMapper;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.ThirdPlatformTenantUserService;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【third_platform_tenant_user】的数据库操作Service实现
* @createDate 2024-07-03 20:03:42
*/
@Slf4j
@Service
public class ThirdPlatformTenantUserServiceImpl extends ServiceImpl<ThirdPlatformTenantUserMapper, ThirdPlatformTenantUser>
implements ThirdPlatformTenantUserService{
    @Resource
    private BbsxpUsersService bbsxpUsersService;
    @Resource
    private OaSysConfigService sysConfigService;

    /**
     * 根据商户id查询下单用户配置
     * @return
     */
    @Override
    public Integer getUserIdByTenant(TenantUserBO tenantUser) {
        return this.baseMapper.getUserIdByTenant(tenantUser);
    }

   @Override
    public ThirdPlatformTenantUser getByTenant(TenantUserBO tenantUser) {
        return this.baseMapper.getByTenant(tenantUser);
    }

    /**
     * 根据商户id查询下单用户配置
     *
     * @param tenantIdList
     * @return
     */
    @Override
    public List<ThirdPlatformTenantUser> getTenantUserListByTenantId(String platCode, List<Integer> tenantIdList) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return Collections.emptyList();
        }
        return CommonUtils.bigDataInQuery(tenantIdList, id -> this.lambdaQuery()
                .in(ThirdPlatformTenantUser::getThirdPlatformTenantId, id)
                .eq(ThirdPlatformTenantUser::getPlatCode, platCode)
                .list());
    }

    /**
     * 根据商户id查询下单用户配置
     *
     * @param tenantUserList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateTenantUser(Tenant tenant, List<ThirdPlatformTenantUser> tenantUserList) {
        if (CollectionUtils.isEmpty(tenantUserList)) {
            return true;
        }
        tenantUserList.forEach(tenantUser -> {
            tenantUser.setThirdPlatformTenantId(tenant.getId());
            tenantUser.setPlatCode(tenant.getPlatCode());
        });
        List<ThirdPlatformTenantUser> oldList = this.lambdaQuery()
                .eq(ThirdPlatformTenantUser::getThirdPlatformTenantId, tenant.getId())
                .eq(ThirdPlatformTenantUser::getPlatCode, tenant.getPlatCode())
                .list();
        List<ThirdPlatformTenantUser> addList = new ArrayList<>();
        List<ThirdPlatformTenantUser> updateList = new ArrayList<>();
        List<ThirdPlatformTenantUser> delList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldList)) {
            Map<Integer, ThirdPlatformTenantUser> oldMap = oldList.stream().collect(Collectors.toMap(ThirdPlatformTenantUser::getId, Function.identity(), (v1,v2) -> v1));
            List<Integer> idList = new ArrayList<>();
            for (ThirdPlatformTenantUser tenantUser : tenantUserList) {
                if (CommonUtil.isNotNullZero(tenantUser.getId())) {
                    ThirdPlatformTenantUser oldTenantUser = oldMap.get(tenantUser.getId());
                    if (oldTenantUser != null) {
                        updateList.add(tenantUser);
                    }
                    idList.add(tenantUser.getId());
                } else {
                    addList.add(tenantUser);
                }
            }
            delList = oldList.stream().filter(v -> !idList.contains(v.getId())).collect(Collectors.toList());
        } else {
            addList = tenantUserList;
        }

        boolean flag = true;
        if (CollectionUtils.isNotEmpty(addList)) {
            for (ThirdPlatformTenantUser tenantUser : addList) {
                flag = flag && this.save(tenantUser);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (ThirdPlatformTenantUser tenantUser : updateList) {
                flag = flag && this.lambdaUpdate()
                        .set(ThirdPlatformTenantUser::getUserId, tenantUser.getUserId())
                        .set(ThirdPlatformTenantUser::getXtenant, tenantUser.getXtenant())
                        .set(ThirdPlatformTenantUser::getPlatKemu, tenantUser.getPlatKemu())
                        .set(ThirdPlatformTenantUser::getVenderKemu, tenantUser.getVenderKemu())
                        .set(ThirdPlatformTenantUser::getRefundKemu, tenantUser.getRefundKemu())
                        .set(ThirdPlatformTenantUser::getGovernmentSubsidyKemu, tenantUser.getGovernmentSubsidyKemu())
                        .set(ThirdPlatformTenantUser::getUpdateTime, LocalDateTime.now())
                        .eq(ThirdPlatformTenantUser::getId, tenantUser.getId()).update();
            }
        }
        if (CollectionUtils.isNotEmpty(delList)) {
            for (ThirdPlatformTenantUser tenantUser : delList) {
                flag = flag && this.lambdaUpdate().set(ThirdPlatformTenantUser::getIsDel, 1).eq(ThirdPlatformTenantUser::getId, tenantUser.getId()).update();
            }
        }
        return flag;
    }

    /**
     * 商户下单用户配置日志
     *
     * @param tenantUserList
     * @return
     */
    @Override
    public String getTenantUserLog(Tenant tenant, List<ThirdPlatformTenantUser> tenantUserList) {
        if (CollectionUtils.isEmpty(tenantUserList)) {
            return "";
        }
        List<ThirdPlatformTenantUser> oldList = this.lambdaQuery()
                .eq(ThirdPlatformTenantUser::getThirdPlatformTenantId, tenant.getId())
                .eq(ThirdPlatformTenantUser::getPlatCode, tenant.getPlatCode())
                .list();
        List<ThirdPlatformTenantUser> addList = new ArrayList<>();
        List<ThirdPlatformTenantUser> updateList = new ArrayList<>();
        List<ThirdPlatformTenantUser> delList = new ArrayList<>();
        Map<Integer, ThirdPlatformTenantUser> oldMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oldList)) {
            oldMap = oldList.stream().collect(Collectors.toMap(ThirdPlatformTenantUser::getId, Function.identity(), (v1,v2) -> v1));
            List<Integer> idList = new ArrayList<>();
            for (ThirdPlatformTenantUser tenantUser : tenantUserList) {
                if (CommonUtil.isNotNullZero(tenantUser.getId())) {
                    ThirdPlatformTenantUser oldTenantUser = oldMap.get(tenantUser.getId());
                    if (oldTenantUser != null) {
                        updateList.add(tenantUser);
                    }
                    idList.add(tenantUser.getId());
                } else {
                    addList.add(tenantUser);
                }
            }
            delList = oldList.stream().filter(v -> !idList.contains(v.getId())).collect(Collectors.toList());
        } else {
            addList = tenantUserList;
        }

        List<SysConfigVo> sysConfigVoList = sysConfigService.getListByCoode(SysConfigConstant.PLATE_FORM);
        Map<String, String> xtenantNameMap = sysConfigVoList.stream().collect(Collectors.toMap(SysConfigVo::getValue, SysConfigVo::getName));
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(addList)) {
            for (ThirdPlatformTenantUser tenantUser : addList) {
                sb.append("新增会员id绑定，租户：").append(xtenantNameMap.get(Convert.toStr(tenantUser.getXtenant()))).append("会员id：").append(tenantUser.getUserId()).append("；");
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (ThirdPlatformTenantUser tenantUser : updateList) {
                ThirdPlatformTenantUser oldTenantUser = oldMap.get(tenantUser.getId());
                if (oldTenantUser != null
                        && (!Objects.equals(oldTenantUser.getUserId(),tenantUser.getUserId())
                        || !Objects.equals(oldTenantUser.getXtenant(),tenantUser.getXtenant())
                        || !Objects.equals(oldTenantUser.getPlatKemu(),tenantUser.getPlatKemu())
                        || !Objects.equals(oldTenantUser.getVenderKemu(),tenantUser.getVenderKemu())
                        || !Objects.equals(oldTenantUser.getRefundKemu(),tenantUser.getRefundKemu())
                        || !Objects.equals(oldTenantUser.getGovernmentSubsidyKemu(),tenantUser.getGovernmentSubsidyKemu()))) {
                    sb.append("修改绑定会员id：").append(tenantUser.getId());
                    if (!Objects.equals(oldTenantUser.getUserId(),tenantUser.getUserId())) {
                        sb.append("，会员由").append(oldTenantUser.getUserId()).append("改为").append(tenantUser.getUserId());
                    }
                    if (!Objects.equals(oldTenantUser.getXtenant(),tenantUser.getXtenant())) {
                        sb.append("，租户由").append(xtenantNameMap.get(Convert.toStr(oldTenantUser.getXtenant()))).append("改为").append(xtenantNameMap.get(Convert.toStr(tenantUser.getXtenant())));
                    }
                    if (!Objects.equals(oldTenantUser.getPlatKemu(),tenantUser.getPlatKemu())) {
                        String str = StringUtils.isNotEmpty(Convert.toStr(oldTenantUser.getPlatKemu())) ? Convert.toStr(oldTenantUser.getPlatKemu()) : "空";
                        String platKemu = StringUtils.isNotEmpty(tenantUser.getPlatKemu()) ? tenantUser.getPlatKemu() : "空";
                        sb.append("，平台科目由").append(str).append("改为").append(Convert.toStr(platKemu));
                    }
                    if (!Objects.equals(oldTenantUser.getVenderKemu(),tenantUser.getVenderKemu())) {
                        String str = StringUtils.isNotEmpty(Convert.toStr(oldTenantUser.getVenderKemu())) ? Convert.toStr(oldTenantUser.getVenderKemu()) : "空";
                        String venderKemu = StringUtils.isNotEmpty(tenantUser.getVenderKemu()) ? tenantUser.getVenderKemu() : "空";
                        sb.append("，商家科目由").append(str).append("改为").append(Convert.toStr(venderKemu));
                    }
                    if (!Objects.equals(oldTenantUser.getRefundKemu(),tenantUser.getRefundKemu())) {
                        String str = StringUtils.isNotEmpty(Convert.toStr(oldTenantUser.getRefundKemu())) ? Convert.toStr(oldTenantUser.getRefundKemu()) : "空";
                        String refundKemu = StringUtils.isNotEmpty(tenantUser.getRefundKemu()) ? tenantUser.getRefundKemu() : "空";
                        sb.append("，退款科目配置由").append(str).append("改为").append(Convert.toStr(refundKemu));
                    }
                    if (!Objects.equals(oldTenantUser.getGovernmentSubsidyKemu(),tenantUser.getGovernmentSubsidyKemu())) {
                        String str = StringUtils.isNotEmpty(Convert.toStr(oldTenantUser.getGovernmentSubsidyKemu())) ? Convert.toStr(oldTenantUser.getGovernmentSubsidyKemu()) : "空";
                        String refundKemu = StringUtils.isNotEmpty(tenantUser.getGovernmentSubsidyKemu()) ? tenantUser.getGovernmentSubsidyKemu() : "空";
                        sb.append("，国补收银方式配置由").append(str).append("改为").append(Convert.toStr(refundKemu));
                    }
                    sb.append("；");
                }
            }
        }
        if (CollectionUtils.isNotEmpty(delList)) {
            for (ThirdPlatformTenantUser tenantUser : delList) {
                sb.append("删除会员id绑定，租户：").append(xtenantNameMap.get(Convert.toStr(tenantUser.getXtenant()))).append("会员id：").append(tenantUser.getUserId()).append("；")
                        .append("平台科目：").append(tenantUser.getPlatKemu()).append("；").append("商家科目：").append(tenantUser.getVenderKemu()).append("；")
                        .append("退款科目：").append(tenantUser.getRefundKemu()).append("；");
                if (StrUtil.isNotBlank(tenantUser.getGovernmentSubsidyKemu())) {
                    sb.append("国补收银方式：").append(tenantUser.getGovernmentSubsidyKemu()).append("；");
                }
            }
        }
        return sb.toString();
    }

    /**
     * 校验商户下单用户配置
     *
     * @param tenantUserList
     * @return
     */
    @Override
    public String validateTenantUser(List<ThirdPlatformTenantUser> tenantUserList) {
        if (CollectionUtils.isEmpty(tenantUserList)) {
            return "";
        }
        List<Integer> userIdList = tenantUserList.stream().map(ThirdPlatformTenantUser::getUserId).collect(Collectors.toList());
        List<BbsxpUsers> bbsxpUsers = CommonUtils.bigDataInQuery(userIdList, userId -> bbsxpUsersService.lambdaQuery().in(BbsxpUsers::getId, userId).list());
        Map<Integer, BbsxpUsers> bbsxpUsersMap = Optional.ofNullable(bbsxpUsers).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(BbsxpUsers::getId, Function.identity(), (v1, v2) -> v1));
        Map<Integer, Integer> repeatXtenantMap = new HashMap<>();
        for (ThirdPlatformTenantUser tenantUser : tenantUserList) {
            if (tenantUser.getXtenant() == null || tenantUser.getUserId() == null) {
                return "xtenant，会员id不能为空";
            }
            BbsxpUsers bbsxpUser = bbsxpUsersMap.get(tenantUser.getUserId());
            if (bbsxpUser == null || !Objects.equals(tenantUser.getXtenant(), bbsxpUser.getXtenant())) {
                return "会员id：" + tenantUser.getUserId() + "不存在或不属于这个租户";
            }
            if (repeatXtenantMap.containsKey(tenantUser.getXtenant())) {
                return "xtenant：" + tenantUser.getXtenant() + "配置了多个会员id";
            }
            repeatXtenantMap.put(tenantUser.getXtenant(), tenantUser.getXtenant());
        }
        return "";
    }
}
