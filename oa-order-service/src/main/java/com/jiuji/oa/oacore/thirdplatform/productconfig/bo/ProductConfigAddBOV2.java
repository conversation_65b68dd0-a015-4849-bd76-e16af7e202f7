/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.bo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品关系配置新增业务实体
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "商品关系配置新增业务实体")
public class ProductConfigAddBOV2 extends Model<ProductConfigAddBOV2> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    @ApiModelProperty(value = "商户编码")
    @NotNull(message = "商户编码不能为空")
    private String tenantCode;
    /**
     * 平台编码（JD-京东;MT-美团）
     */
    @ApiModelProperty(value = "平台编码")
    @NotNull(message = "平台编码不能为空")
    private String platCode;
    /**
     * 平台商品skuid
     */
    @ApiModelProperty(value = "平台skuid")
    @NotBlank(message = "平台skuid不能为空")
    public String skuId;

    @ApiModelProperty(value = "平台商品skuid")
    public String productName;

    @ApiModelProperty(value = "规则码")
    public String ruleCode;

    @ApiModelProperty(value = "优惠码金额")
    public BigDecimal couponPrice;

    private Integer type;
    /**
     * 配置标签
     */
    private Integer label;

    /**
     * 服务ppid
     */
    private String servicePpid;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 客户实付收款方式
     */
    private String actualPaymentType;
}
