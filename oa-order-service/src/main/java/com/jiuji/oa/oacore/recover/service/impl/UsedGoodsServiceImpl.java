package com.jiuji.oa.oacore.recover.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.recover.dao.UsedGoodsMapper;
import com.jiuji.oa.oacore.recover.po.UsedGoods;
import com.jiuji.oa.oacore.recover.res.SalfGoodsNewMpicsRes;
import com.jiuji.oa.oacore.recover.service.UsedGoodsService;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DS(DataSourceConstants.ERSHOU)
public class UsedGoodsServiceImpl extends ServiceImpl<UsedGoodsMapper, UsedGoods> implements UsedGoodsService {

    /**
     * 查询良品主图
     * @param mkcIds
     * @return
     */
    @Override
    public List<SalfGoodsNewMpicsRes> getNewMpicsByMkcIds(List<Integer> mkcIds) {
        if (CollUtil.isEmpty(mkcIds)) {
            return new ArrayList<>();
        }
        return this.baseMapper.selectNewMpicsByMkcIds(mkcIds);
    }

    /**
     * 查询拍机堂回收主图
     * @param goodsIds
     * @return
     */
    @Override
    public List<SalfGoodsNewMpicsRes> getRecyclePjtGoodsByGoodsIds(List<Integer> goodsIds) {
        if (CollUtil.isEmpty(goodsIds)) {
            return new ArrayList<>();
        }
        return this.baseMapper.getRecyclePjtGoodsByGoodsIds(goodsIds);
    }
}
