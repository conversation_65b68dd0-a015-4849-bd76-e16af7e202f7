package com.jiuji.oa.oacore.thirdplatform.yading.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Mapper
public interface YadingServiceStateMapper extends BaseMapper<YadingServiceState> {
    /**
     * @Description 查询出九讯服务注册状态，可能会查询不到数据，那就是没注册
     * @Param detailId
     * @returnInteger
     * <AUTHOR>
     * @Date 22:49 2022/5/11
     **/
    Integer getJiuxunServiceRegisterState(@Param("detailId") Long detailId);

}
