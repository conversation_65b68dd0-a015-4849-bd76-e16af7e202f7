package com.jiuji.oa.oacore.thirdplatform.yading.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceMapping;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingAppleProTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingPhoneTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingServiceMappingMapper;
import com.jiuji.oa.oacore.thirdplatform.yading.service.IYadingServiceMappingService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 九讯服务与亚丁服务对应关系表， 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Service
public class YadingServiceMappingServiceImpl extends ServiceImpl<YadingServiceMappingMapper, YadingServiceMapping> implements IYadingServiceMappingService {


    @Override
    public YadingServiceMapping getYadingServiceConfig(Integer skuId, YadingPhoneTypeEnum phoneTypeEnum, YadingAppleProTypeEnum proTypeEnum, BigDecimal androidPrice) {
        return this.baseMapper.getYadingServiceConfig(skuId,phoneTypeEnum.getValue(),proTypeEnum.getValue(),androidPrice);
    }


    @Override
    public YadingServiceMapping getYadingServiceConfig(Integer skuId) {
        if(skuId==null){
            return null;
        }
        return this.baseMapper.getYadingServiceConfigOne(skuId);
    }
}
