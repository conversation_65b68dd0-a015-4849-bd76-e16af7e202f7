package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 维修配件改价请求参数
 * @author: <PERSON>
 * @date: 2020/5/25 9:14
 */
@Data
@ApiModel
public class WxPjEditPriceReq {
    @ApiModelProperty(value = "维修单id")
    private Integer id;

    @ApiModelProperty(value = "配件id")
    private List<Integer> kcOutId;

    @ApiModelProperty(value = "配件价格")
    private List<BigDecimal>kcPrice;

    @ApiModelProperty(value = "工时费")
    private List<BigDecimal>kcPriceGs;

    @ApiModelProperty(value = "配件成本")
    private List<BigDecimal>kcInprice;

}
