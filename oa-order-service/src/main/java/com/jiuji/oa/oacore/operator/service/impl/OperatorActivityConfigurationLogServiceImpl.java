package com.jiuji.oa.oacore.operator.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.operator.dao.OperatorActivityConfigurationLogMapper;
import com.jiuji.oa.oacore.operator.po.OperatorActivityConfigurationLog;
import com.jiuji.oa.oacore.operator.service.OperatorActivityConfigurationLogService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
@DS(DataSourceConstants.OA_LOG)
public class OperatorActivityConfigurationLogServiceImpl extends ServiceImpl<OperatorActivityConfigurationLogMapper, OperatorActivityConfigurationLog> implements OperatorActivityConfigurationLogService {
    @Resource
    private SmsService smsService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchLog(List<OperatorActivityConfigurationLog> list) {
        try {
            boolean falg = this.saveBatch(list);
            if(!falg){
                throw new CustomizeException("保存失败");
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("运营商活动版本配置日志保存异常", JSONUtil.toJsonStr(list), e, smsService::sendOaMsgTo9JiMan);
        }
        
    }

    @Override
    public List<OperatorActivityConfigurationLog> selectLogById(Integer configId) {
        if(ObjectUtil.isNull(configId)){
            throw new CustomizeException("configId不能为空");
        }
        return this.lambdaQuery().eq(OperatorActivityConfigurationLog::getConfigId,configId)
                .orderByDesc(OperatorActivityConfigurationLog::getCreateTime)
                .list();
    }
}
