package com.jiuji.oa.oacore.oaorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 小件商品库存状态
 * <AUTHOR>
 * @date 2019/11/13
 * @since 1.0.0
 */
@AllArgsConstructor
@Getter
public enum SmallStockStateEnum implements CodeMessageEnumInterface {

    /**
     * 库存状态 编码-信息
     */


    STOCK_STATE_WAIT(-2, "等待备货"),
    STOCK_STATE_PUT(1, "已提交-采购中"),
    STOCK_ON_THE_WAY(10, "在途"),
    STOCK_STATE_STOCK(3, "库存-已到店");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
