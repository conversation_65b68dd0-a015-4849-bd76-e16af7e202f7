package com.jiuji.oa.afterservice.bigpro.enums;


import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 亚丁保险注册状态枚举
 * <AUTHOR>
 * @Date   2022/5/11 17:49
 */
@Getter
@AllArgsConstructor
public enum HuiJiBaoRegisterStateEnum implements CodeMessageEnumInterface {


    UNREGISTERED(0, "未注册"),
    REGISTERED(1, "已注册"),
    REGISTING(3, "审核中"),
    /**
     * 被拒了和未注册过的，都算未注册（老蒋）
     */
    UNAPPROVE(2, "审核未通过");


    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;


}
