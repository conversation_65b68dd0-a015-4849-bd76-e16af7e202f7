package com.jiuji.oa.oacore.thirdplatform.taobao.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoService;
import com.jiuji.oa.oacore.thirdplatform.taobao.vo.TaobaoResponseVO;
import com.jiuji.oa.oacore.thirdplatform.taobao.vo.TaobaoToken;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.qimencloud.api.sceneqimen.request.AlibabaTclsAelophyOrderRefundCompleteRequest;
import com.qimencloud.api.sceneqimen.response.AlibabaTclsAelophyOrderRefundCompleteResponse;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/8/16 14:51
 */
@Service
@Slf4j
public class TaoBaoServiceImpl implements TaoBaoService {

    @Value("${taobao.api_url:}")
    private String taoBaoApiUrl;

    @Value("${taobao.msg_url:}")
    private String taoBaoMsgUrl;

    @CreateCache(name = "java_order_taobao_access_token",cacheType = CacheType.BOTH)
    private Cache<String, MyAccessToken> tokenCache;

    @Resource
    private TenantService tenantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Override
    @SneakyThrows
    public R token(String code, String appKey) {
        // code 和 appKey 都不能为空
        if (StrUtil.isBlank(code) || StrUtil.isBlank(appKey)) {
            return R.error("编码和应用key都不能为空");
        }
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(PlatfromEnum.TB.name(), appKey);
        if(tenant == null){
            return R.error("没有对应的应用信息");
        }
        DefaultTaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, appKey, tenant.getAppSecret());
        TopAuthTokenCreateRequest tokenReq = new TopAuthTokenCreateRequest();
        tokenReq.setCode(code);
        TopAuthTokenCreateResponse rsp = client.execute(tokenReq);
        if (rsp.isSuccess()) {
            String cacheKey = StrUtil.format(RedisKeyConstant.TAO_BAO_TOKEN, appKey);
            TaobaoToken taobaoToken = JsonUtils.fromJson(rsp.getTokenResult(), TaobaoToken.class);
            if (taobaoToken != null) {
                taobaoToken.setAppKey(appKey);
                taobaoToken.setAppSecret(tenant.getAppSecret());
                taobaoToken.setExpiresDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(taobaoToken.getExpireTime()), ZoneId.systemDefault()));
                stringRedisTemplate.opsForValue().set(cacheKey, JsonUtils.toJson(taobaoToken), NumberConstant.ONE_HUNDRED_EIGHTY, TimeUnit.DAYS);
            }
            log.warn("淘宝token信息: {}",rsp.getTokenResult());
        }else{
            log.error("获取token失败:{}", JSON.toJSONString(rsp));
            return R.error(StrUtil.format("{} {}", rsp.getMsg(), rsp.getSubMsg()));
        }
        return R.success(null);
    }

    /**
     * 查询token
     * @return
     */
    @Override
    public TaobaoToken getTaobaoToken(String appKey) {
        String cacheKey = StrUtil.format(RedisKeyConstant.TAO_BAO_TOKEN, appKey);
        String tokenStr = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isBlank(tokenStr)) {
            throw new CustomizeException(StrUtil.format("appKey:{}，未获取到token信息，请重新授权", appKey));
        }
        TaobaoToken taobaoToken = JsonUtils.fromJson(tokenStr, TaobaoToken.class);
        if (taobaoToken == null) {
            throw new CustomizeException(StrUtil.format("appKey:{}，解析token失败，请重新授权", appKey));
        }
        return taobaoToken;
    }

    /**
     * 查询token
     * @param appKey
     * @return
     */
    @Override
    public String getAccessTokenByCache(String appKey) {
        String cacheKey = StrUtil.format(RedisKeyConstant.TAO_BAO_TOKEN, appKey);
        String tokenStr = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(tokenStr)) {
            TaobaoToken taobaoToken = JsonUtils.fromJson(tokenStr, TaobaoToken.class);
            if (taobaoToken != null) {
                return taobaoToken.getAccessToken();
            }
        }
        return "";
    }

    /**
     * 淘宝订单状态下发
     *
     * @param request
     * @return
     */
    @Override
    public TaobaoResponseVO orderPush(HttpServletRequest request) {
        try {
            String targetAppkey = request.getParameter("target_appkey");
            String method = request.getParameter("method");
            //验签
            CheckResult checkResult = SpiUtils.checkSign(request, "5f4241a6b2c285cc0331ce7281e26b79");
            if (!checkResult.isSuccess()) {
                return TaobaoResponseVO.signCheckFailure();
            }
            String requestBody = checkResult.getRequestBody();
            log.info("淘宝推送接口请求参数: {}", requestBody);

            return TaobaoResponseVO.success();
        } catch (Exception e) {
            log.error("淘宝推送接口解析异常, checkSign error", e);
        }
        return TaobaoResponseVO.failure();
    }

    @Override
    public R<AlibabaAelophyOrderGetResponse.OrderResponse> getOrderDetail(AlibabaAelophyOrderGetRequest.OrderGetRequest requestParam,
                                                                          TaobaoToken taobaoToken) {
        try {
            //查询订单详情
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyOrderGetRequest orderGetRequest = new AlibabaAelophyOrderGetRequest();
            orderGetRequest.setOrderGetRequest(requestParam);
            AlibabaAelophyOrderGetResponse rsp = client.execute(orderGetRequest, taobaoToken.getAccessToken());
            log.info("查询淘宝小时达订单requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(rsp));
            if (rsp.isSuccess()) {
                return R.success(rsp.getApiResult().getModel());
            } else {
                return R.error(rsp.getSubMsg());
            }
        } catch (Exception e) {
            log.error("查询淘宝小时达订单异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error("查询淘宝小时达订单异常");
    }

    /**
     * 同城零售逆向订单状态完成接口
     *
     * @param requestParam
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> orderRefundComplete(AlibabaTclsAelophyOrderRefundCompleteRequest requestParam, TaobaoToken taobaoToken) {

        try {
            //查询订单详情
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaTclsAelophyOrderRefundCompleteResponse refundCompleteResponse = client.execute(requestParam, taobaoToken.getAccessToken());
            log.info("淘宝小时达同城零售逆向订单状态完成接口requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(refundCompleteResponse));
            if (refundCompleteResponse.isSuccess()) {
                return R.success(refundCompleteResponse.getErrMsg());
            } else {
                return R.error(refundCompleteResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达同城零售逆向订单状态完成接口异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error("淘宝小时达同城零售逆向订单状态完成接口异常");
    }

    /**
     * 配送轨迹回传
     *
     * @param requestParam
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> logisticsTraceCallback(AlibabaAelophyOrderLogisticsTraceCallbackRequest.LogisticsTraceCallbackRequest requestParam, TaobaoToken taobaoToken) {

        try {
            //查询订单详情
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyOrderLogisticsTraceCallbackRequest request = new AlibabaAelophyOrderLogisticsTraceCallbackRequest();
            request.setLogisticsTraceCallbackRequest(requestParam);
            AlibabaAelophyOrderLogisticsTraceCallbackResponse traceCallbackResponse = client.execute(request, taobaoToken.getAccessToken());
            log.info("淘宝小时达配送轨迹回传接口requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(traceCallbackResponse));
            if (traceCallbackResponse.isSuccess()) {
                AlibabaAelophyOrderLogisticsTraceCallbackResponse.TopBaseResult apiResult = traceCallbackResponse.getApiResult();
                if (apiResult == null) {
                    return R.error("淘宝小时达配送轨迹回传接口返回值为空");
                }
                if (apiResult.getSuccess()) {
                    return R.success(apiResult.getErrMsg());
                } else {
                    return R.error(apiResult.getErrMsg());
                }
            } else {
                return R.error(traceCallbackResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达配送轨迹回传接口异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error("淘宝小时达配送轨迹回传接口异常");
    }

    /**
     * 配送员信息变更
     *
     * @param requestParam
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> delivererChange(AlibabaAelophyOrderDelivererChangeRequest.DelivererChangeRequest requestParam, TaobaoToken taobaoToken) {

        try {
            //查询订单详情
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyOrderDelivererChangeRequest req = new AlibabaAelophyOrderDelivererChangeRequest();
            req.setDelivererChangeRequest(requestParam);
            AlibabaAelophyOrderDelivererChangeResponse delivererChangeResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达配送员信息变更接口requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(delivererChangeResponse));
            if (delivererChangeResponse.isSuccess()) {
                AlibabaAelophyOrderDelivererChangeResponse.TopBaseResult apiResult = delivererChangeResponse.getApiResult();
                if (apiResult == null) {
                    return R.error("淘宝小时达配送员信息变更接口返回值为空");
                }
                if (apiResult.getSuccess()) {
                    return R.success(apiResult.getErrMsg());
                } else {
                    return R.error(apiResult.getErrMsg());
                }
            } else {
                return R.error(delivererChangeResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达配送员信息变更接口异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error("淘宝小时达配送员信息变更接口异常");
    }

    /**
     * 仓配作业结果回传接口
     *
     * @param requestParam
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> workCallback(AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest requestParam, TaobaoToken taobaoToken) {
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyOrderWorkCallbackRequest req = new AlibabaAelophyOrderWorkCallbackRequest();
            req.setWorkCallbackRequest(requestParam);
            AlibabaAelophyOrderWorkCallbackResponse workCallbackResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达仓配作业结果回传接口requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(workCallbackResponse));
            if (workCallbackResponse.isSuccess()) {
                AlibabaAelophyOrderWorkCallbackResponse.TopBaseResult apiResult = workCallbackResponse.getApiResult();
                if (apiResult == null) {
                    return R.error(ResultCodeEnum.RETURN_ERROR.getCode(),"淘宝小时达仓配作业结果回传接口返回值为空");
                }
                if (apiResult.getSuccess()) {
                    return R.success(apiResult.getErrMsg());
                } else {
                    return R.error(ResultCodeEnum.RETURN_ERROR.getCode(), apiResult.getErrMsg());
                }
            } else {
                return R.error(ResultCodeEnum.RETURN_ERROR.getCode(),workCallbackResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达仓配作业结果回传接口异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error(ResultCodeEnum.RETURN_ERROR.getCode(),"淘宝小时达仓配作业结果回传接口异常");
    }

    /**
     * 库存发布接口
     *
     * @param requestParam
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> stockPublish(AlibabaWdkStockPublishRequest.BatchStockPublishDto requestParam, TaobaoToken taobaoToken) {
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaWdkStockPublishRequest  req = new AlibabaWdkStockPublishRequest ();
            req.setBatchStockPublishDto(requestParam);
            AlibabaWdkStockPublishResponse stockPublishResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达库存同步接口requestParam={}，rsp={}", JsonUtils.toJson(requestParam), JsonUtils.toJson(stockPublishResponse));
            if (stockPublishResponse.isSuccess()) {
                return R.success(stockPublishResponse.getMessage());
            } else {
                return R.error(stockPublishResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("淘宝小时达库存同步接口异常requestParam={}", JsonUtils.toJson(requestParam), e);
        }
        return R.error("淘宝小时达库存同步接口异常");
    }

    /**
     * 验签
     * @param request
     * @return
     */
    @Override
    public CheckResult checkTaobaoSign(HttpServletRequest request, String appSecret) {
        try {
            //验签
            return SpiUtils.checkSign(request, appSecret);
        } catch (Exception e) {
            log.error("淘宝接口验签异常appSecret={}, checkSign error", appSecret, e);
        }
        return null;
    }

    /**
     * 商户同意用户逆向申请
     *
     * @param req
     * @param taobaoToken
     * @return
     */
    @Override
    public R<String> refundAgree(AlibabaTclsAelophyRefundAgreeRequest req, TaobaoToken taobaoToken) {
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaTclsAelophyRefundAgreeResponse refundAgreeResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达商户同意用户逆向申请接口requestParam={}，rsp={}", JsonUtils.toJson(req), JsonUtils.toJson(refundAgreeResponse));
            if (refundAgreeResponse.isSuccess()) {
                return R.success(refundAgreeResponse.getMessage());
            } else {
                return R.error(refundAgreeResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("淘宝小时达商户同意用户逆向申请接口异常requestParam={}", JsonUtils.toJson(req), e);
        }
        return R.error("淘宝小时达商户同意用户逆向申请接口异常");
    }

    /**
     * 退款查询
     *
     * @param req
     * @param taobaoToken
     * @return
     */
    @Override
    public R<AlibabaWdkOrderRefundGetResponse.OrderSyncRefundListResult> refundGet(AlibabaWdkOrderRefundGetRequest req, TaobaoToken taobaoToken) {
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaWdkOrderRefundGetResponse refundGetResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达订单退款查询接口requestParam={}，rsp={}", JsonUtils.toJson(req), JsonUtils.toJson(refundGetResponse));
            if (refundGetResponse.isSuccess()) {
                return R.success(refundGetResponse.getResult());
            } else {
                return R.error(refundGetResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达订单退款查询接口requestParam={}", JsonUtils.toJson(req), e);
        }
        return R.error("淘宝小时达订单退款查询接口异常");
    }


    /**
     * 更新渠道店基础信息
     * @param req
     * @param taobaoToken
     * @return
     */
    @Override
    public  R<String> updateInfo(AlibabaAelophyShopUpdateinfoRequest req, TaobaoToken taobaoToken){
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyShopUpdateinfoResponse shopUpdateinfoResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达更新渠道店基础信息接口requestParam={}，rsp={}", JsonUtils.toJson(req), JsonUtils.toJson(shopUpdateinfoResponse));
            if (shopUpdateinfoResponse.isSuccess()) {
                return R.success(shopUpdateinfoResponse.getApiResult().getErrMsg());
            } else {
                return R.error(shopUpdateinfoResponse.getApiResult().getErrMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达更新渠道店基础信息接口requestParam={}", JsonUtils.toJson(req), e);
        }
        return R.error("淘宝小时达更新渠道店基础信息接口异常");
    }

    /**
     * 更新渠道店营业状态
     * @param req
     * @param taobaoToken
     * @return
     */
    @Override
    public  R<String> updateStatus(AlibabaAelophyShopUpdatestatusRequest req, TaobaoToken taobaoToken){
        try {
            TaobaoClient client = new DefaultTaobaoClient(taoBaoApiUrl, taobaoToken.getAppKey(), taobaoToken.getAppSecret());
            AlibabaAelophyShopUpdatestatusResponse shopUpdatestatusResponse = client.execute(req, taobaoToken.getAccessToken());
            log.info("淘宝小时达更新渠道店营业状态接口requestParam={}，rsp={}", JsonUtils.toJson(req), JsonUtils.toJson(shopUpdatestatusResponse));
            if (shopUpdatestatusResponse.isSuccess()) {
                return R.success(shopUpdatestatusResponse.getApiResult().getErrMsg());
            } else {
                return R.error(shopUpdatestatusResponse.getApiResult().getErrMsg());
            }
        } catch (Exception e) {
            log.error("淘宝小时达更新渠道店营业状态接口requestParam={}", JsonUtils.toJson(req), e);
        }
        return R.error("淘宝小时达更新渠道店营业状态接口异常");
    }
}
