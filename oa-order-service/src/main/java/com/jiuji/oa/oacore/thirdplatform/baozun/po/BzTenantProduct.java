package com.jiuji.oa.oacore.thirdplatform.baozun.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date ${Date} 18:37
 * @Description 宝尊平台商品信息
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "宝尊平台商品信息")
@Data
@TableName(value = "baozun_tenant_product")
public class BzTenantProduct extends Model<BzTenantProduct> {
    private static final long serialVersionUID = 1478791407806704879L;
    /**
     * SPU code 产品唯一编码
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * SPU code 产品唯一编码
     */
    @TableField(value = "product_code")
    @ApiModelProperty(value = "SPU code 产品唯一编码")
    private String productCode;

    /**
     * SPU code 产品唯一编码
     */
    @TableField(value = "fk_tenant_id")
    @ApiModelProperty(value = "关联平台id")
    private Integer fkTenantId;

    /**
     * 类目
     */
    @TableField(value = "schema_code")
    @ApiModelProperty(value = "类目")
    private String schemaCode;

    /**
     * 商品货号
     */
    @TableField(value = "product_no")
    @ApiModelProperty(value = "商品货号")
    private String productNo;

    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 产品类目名称
     */
    @TableField(value = "pos_cate_name")
    @ApiModelProperty(value = "产品类目名称")
    private String posCateName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_del")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(exist = false)
    private Date baozunTenantProductRv;


}