package com.jiuji.oa.oacore.common.controller;

import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/7/21 14:06
 * @Description
 * @Version 1.0.0
 */
@RestController
@RequestMapping("/common")
public class CommonController {

    @Resource
    private UserInfoClient userInfoClient;

    /**
     * 获取oa用户手机号
     * @param userId
     * @return
     */
    @GetMapping("/userinfo/mobile/v1")
    public R getUserInfo(@RequestParam("userId")Integer userId){
        R<Ch999UserVo> ch999UserInfo = userInfoClient.getCh999UserInfo(userId);
        if(ch999UserInfo.getData()==null){
            return R.error("无此用户");
        }
        String mobile = ch999UserInfo.getData().getMobile();
        Map<String,Object> result=new HashMap<>(2);
        result.put("mobile",mobile);
        return R.success(result);
    }
}
