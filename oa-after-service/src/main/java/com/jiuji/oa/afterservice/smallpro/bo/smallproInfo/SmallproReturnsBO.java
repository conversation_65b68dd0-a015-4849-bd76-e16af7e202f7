package com.jiuji.oa.afterservice.smallpro.bo.smallproInfo;

import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;

/**
 * description: <Class introduction>
 * translation: <Method translation introduction>
 *
 * <AUTHOR>
 * @date 2020/1/19
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproReturnsBO implements Serializable {

    private static final long serialVersionUID = -8012161374195541763L;
    private SmallproReturnsConnBO conn;

    private Boolean isrs_t1eof;

    private Double price;

    private Double tuikuanM;

    private HashMap data;

    private Integer area;

    private Double baitiaoPrice;

    private Double kuBaitiaoPrice;

    private ShouhouTuihuan MShouHouTuiHuan;

    private Boolean isServiceRecord;

    private Integer islock;

    private Integer mainRole;

}
