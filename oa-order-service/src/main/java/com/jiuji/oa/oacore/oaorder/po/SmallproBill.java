package com.jiuji.oa.oacore.oaorder.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SmallproBill")
public class SmallproBill extends Model<SmallproBill> implements Serializable {

    private static final long serialVersionUID = 3524963140983350060L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 小件售后接单号
     */
    @TableField("smallproID")
    private Integer smallproID;

    /**
     * 订单详情Id
     */
    private Integer basketId;

    /**
     * SKU id
     */
    private Long ppriceid;

    /**
     * 小件售后接单内商品数量
     */
    private Integer count;

    /**
     * 库存进价？？？
     */
    private BigDecimal inprice;

    private Integer mobileExchangeFlag = 0;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
