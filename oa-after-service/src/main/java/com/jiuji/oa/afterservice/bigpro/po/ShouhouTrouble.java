package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_trouble")
public class ShouhouTrouble extends Model<ShouhouTrouble> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer Id;

    @TableField("ShouhouId")
    private Integer ShouhouId;

    @TableField("TroubleId")
    private Integer TroubleId;

    @TableField("IsValid")
    private Boolean IsValid;

    @Override
    protected Serializable pkVal() {
        return this.Id;
    }

}
