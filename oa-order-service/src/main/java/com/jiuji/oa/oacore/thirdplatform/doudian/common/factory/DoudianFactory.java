package com.jiuji.oa.oacore.thirdplatform.doudian.common.factory;

import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.msg.DoudianOpMsgRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2022/3/21 17:20
 */
public interface DoudianFactory {

    /**
     * 获取token,主动刷新token
     * @param shopId
     * @return
     */
    MyAccessToken getMyAccessToken(Long shopId);

    /**
     * 移除accessToken
     * @param shopId
     * @return
     */
    boolean removeAccessToken(Long shopId);

    /**
     * 没有设置配置信息
     * @param request
     * @return
     */
    DoudianOpMsgRequest getMsgRequest(HttpServletRequest request);

    /**
     * 执行抖音请求的封装
     * @param request
     * @param shopId
     * @param setParamCallback
     * @param <T>
     * @param <S>
     * @return
     */
    <T,S> S executeRequest(DoudianOpRequest<T> request, Long shopId, Consumer<T> setParamCallback);


}
