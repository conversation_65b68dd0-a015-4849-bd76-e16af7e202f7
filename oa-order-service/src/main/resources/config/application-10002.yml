consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://************:5083
  select:
    url: https://img.1teng.com.cn/
  upload:
    url: http://************:9333
instance-zone: 10002
jiuji:
  sys:
    moa: https://moa.1teng.com.cn
    pc: https://oa.1teng.com.cn
    web: https://www.1teng.com.cn
    xtenant: 10002
  xtenant: 2000
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10002:diDHhZnPteGwMkMt@************:27017,************:27017,************:27017/ch999oa__10002
  url1: *****************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10002
    password: 84YmPJZMdVDrvTnv
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10002
  oa_nc:
    dbname: oa_nc__10002
    password: s0iB30eyBMxqt4pW
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10002
office:
  sys:
    xtenant: 10002
rabbitmq:
  master:
    password: LUIVM
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10002
    vhost: oaAsync__10002
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: INhVR
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10002
    vhost: oa__10002
  oaAsync:
    password: LUIVM
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10002
    vhost: oaAsync__10002
  printer:
    password: AtsqX
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10002
    vhost: printer__10002
redis:
  oa:
    host: ************
    password: google99
    port: 6389
    url: google99@************:6389
sms:
  send:
    email:
      url: http://sms.1teng.com.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.1teng.com.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10002
sqlserver:
  after_write:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'Z*T4ZtxbL0SF'
    port: 1433
    username: ch999oanew__10002
  ch999oanew:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'Z*T4ZtxbL0SF'
    port: 1433
    username: ch999oanew__10002
  ch999oanewHis:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'Z*T4ZtxbL0SF'
    port: 1433
    username: ch999oanew__10002
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'aPhHDXLCV*ZB'
    port: 1433
    username: office__10002
  oanewWrite:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'Z*T4ZtxbL0SF'
    port: 1433
    username: ch999oanew__10002
  office:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'aPhHDXLCV*ZB'
    port: 1433
    username: office__10002
  officeWrite:
    dbname: office__10002
    host: oa.sqlserver.serv.iteng.com
    password: 'aPhHDXLCV*ZB'
    port: 1433
    username: office__10002
  smallpro_write:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  web999:
    dbname: web999__10002
    host: oa.sqlserver.serv.iteng.com
    password: sCf_CPlMCKpJ
    port: 1433
    username: web999__10002
  ch999oanewReport:
    dbname: ch999oanew__10002
    host: oa.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbL0SF"
    port: 1433
    username: ch999oanew__10002
  ershou:
    dbname: ershou__10002
    host: oa.sqlserver.serv.iteng.com
    password: "wHGw19yypDug"
    port: 1433
    username: ershou__10002
url:
  delImgUrl: http://************:5083
  oa-push-info: http://inwcf.1teng.com.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.1teng.com.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://************:9333
autoCalculateSalary:
  xtenant: 10002
