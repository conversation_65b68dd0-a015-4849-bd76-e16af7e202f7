package com.jiuji.oa.oacore.thirdplatform.doudian.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.doudian.open.api.order_logisticsAdd.data.OrderLogisticsAddData;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsEdit.data.OrderLogisticsEditData;
import com.doudian.open.api.order_logisticsEdit.param.OrderLogisticsEditParam;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.electronicTicket.enums.DeliveryEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.DouDianlogisticsCompanyEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.DouDianlogisticsExpressCompanyEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DouyinBizService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.OrderLogisticsBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdPlatformOrderLogisticsSync;
import com.jiuji.oa.oacore.thirdplatform.order.mapper.OrderMapper;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.thirdplatform.order.service.ThirdPlatformOrderLogisticsSyncService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class DouyinBizServiceImpl implements DouyinBizService {
    @Resource
    private DefaultDouDianService defaultDouDianService;
    @Resource
    private SmsService smsService;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private ThirdPlatformOrderLogisticsSyncService thirdPlatformOrderLogisticsSyncService;

    /**
     * 抖音发货
     * @param orderId
     * @return
     */
    @Override
    public R<String> logisticsAdd(String orderId) {
        OrderLogisticsBO orderLogistics = orderService.getLogisticsByOrderId(orderId, ThirdPlatformCommonConst.THIRD_PLAT_DY);
        if (Objects.isNull(orderLogistics)) {
            return R.error("查询订单物流信息为空，订单号:" + orderId);
        }
        //限制确认或者出库状态
        if (!Arrays.asList(SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode(),SubCheckEnum.SUB_CHECK_CONFIRMED.getCode()).contains(Optional.ofNullable(orderLogistics.getSubCheck()).orElse(0))) {
            log.info("订单不是确认或出库状态，订单号:{}", orderId);
            return R.error("订单不是确认或出库状态，订单号:" + orderId);
        }
        //查询同步记录
        ThirdPlatformOrderLogisticsSync orderLogisticsSync = thirdPlatformOrderLogisticsSyncService.getLogisticsSyncByKindAndOrderId(orderId, 0);
        Long orderLogisticsSyncId = null;
        if (Objects.nonNull(orderLogisticsSync)) {
            orderLogisticsSyncId = orderLogisticsSync.getId();
            //如果有记录，并且物流单号相同，则不进行同步
            if (Objects.equals(orderLogistics.getNu(), orderLogisticsSync.getLogisticsCode())) {
                return R.success("同步成功");
            }
        }

        //查询订单串号
        List<String> imeiList = orderMapper.getImeiListBySubId(orderLogistics.getSubId());
        MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(orderLogistics.getTenantCode(), 0L));
        OrderLogisticsAddParam param = new OrderLogisticsAddParam();
        if (CollectionUtils.isNotEmpty(imeiList)) {
            param.setSerialNumberList(imeiList);
        }
        param.setOrderId(orderId);
        //快递运输
        if (Objects.equals(DeliveryEnum.DELIVERY_FOUR.getCode(), orderLogistics.getDelivery())) {
            param.setCompanyCode(DouDianlogisticsExpressCompanyEnum.getCompanyCode(orderLogistics.getCom()));
        } else {
            param.setCompanyCode(DouDianlogisticsCompanyEnum.getCompanyCode(orderLogistics.getCom()));
        }
        //未匹配到快递公司
        if(StrUtil.isBlank(param.getCompanyCode())) {
            log.warn("未匹配到抖音快递公司，订单号:{}，orderLogistics={}", orderId, orderLogistics);
            return R.error("未匹配到抖音快递公司");
        }
        param.setLogisticsCode(StringUtils.isNotBlank(orderLogistics.getNu()) ? orderLogistics.getNu() : orderLogistics.getWuliuId());
        param.setStoreId(Convert.toLong(orderLogistics.getStoreId(), 0L));
        if (orderLogisticsSyncId != null) {
            OrderLogisticsEditParam editParam = new OrderLogisticsEditParam();
            editParam.setOrderId(orderId);
            editParam.setCompanyCode(param.getCompanyCode());
            editParam.setLogisticsCode(param.getLogisticsCode());
            editParam.setCompany(param.getCompany());
            editParam.setStoreId(param.getStoreId());
            //更新订单物流信息
            R<OrderLogisticsEditData> orderLogisticsEditDataR = defaultDouDianService.logisticsEdit(myAccessToken, editParam);
            if (orderLogisticsEditDataR.isSuccess()) {
                ThirdPlatformOrderLogisticsSync logisticsSync = ThirdPlatformOrderLogisticsSync.builder().id(orderLogisticsSyncId).orderId(orderId).subId(orderLogistics.getSubId())
                        .companyCode(orderLogistics.getCom()).logisticsCode(param.getLogisticsCode()).kind(0).updateTime(LocalDateTime.now()).build();
                thirdPlatformOrderLogisticsSyncService.updateThirdPlatformOrderLogisticsById(logisticsSync);
                return R.success("同步成功");
            }
            return R.error(orderLogisticsEditDataR.getUserMsg());
        }
        R<OrderLogisticsAddData> orderLogisticsAddDataR = defaultDouDianService.logisticsAdd(myAccessToken, param);
        if (orderLogisticsAddDataR.isSuccess()) {
            //保存同步单号信息
            ThirdPlatformOrderLogisticsSync logisticsSync = ThirdPlatformOrderLogisticsSync.builder().orderId(orderId).subId(orderLogistics.getSubId())
                    .companyCode(orderLogistics.getCom()).logisticsCode(param.getLogisticsCode()).kind(0).build();
            thirdPlatformOrderLogisticsSyncService.saveThirdPlatformOrderLogistics(logisticsSync);

            subLogsCloud.addLpSubLog(LambdaBuild.create(new SubLogsNewReq()).set(SubLogsNewReq::setComment, "回传发货信息成功：通知抖音小时达平台发货成功。")
                    .set(SubLogsNewReq::setSubId, Convert.toInt(orderLogistics.getSubId())).set(SubLogsNewReq::setType, 1)
                    .set(SubLogsNewReq::setShowType, false).set(SubLogsNewReq::setInUser, "系统")
                    .set(SubLogsNewReq::setDTime, LocalDateTime.now()).build());
            return R.success("同步成功");
        } else {
            String msg = StrUtil.format("回传发货信息失败：通知抖音小时达平台发货失败，失败原因：{}", orderLogisticsAddDataR.getUserMsg());
            subLogsCloud.addLpSubLog(LambdaBuild.create(new SubLogsNewReq()).set(SubLogsNewReq::setComment, msg)
                    .set(SubLogsNewReq::setSubId, Convert.toInt(orderLogistics.getSubId())).set(SubLogsNewReq::setType, 1)
                    .set(SubLogsNewReq::setShowType, false).set(SubLogsNewReq::setInUser, "系统")
                    .set(SubLogsNewReq::setDTime, LocalDateTime.now()).build());
            if (!orderLogisticsAddDataR.getUserMsg().contains("没有能发货的订单，请修改后重试")) {
                smsService.sendOaMsgTo9JiMan("抖音小时达订单发货失败，原因：{}", orderLogisticsAddDataR.getUserMsg());
            }
            return R.error(orderLogisticsAddDataR.getUserMsg());
        }
    }

    /**
     * 定时处理抖音订单物流发货
     *
     * @return
     */
    @Override
    public R<Boolean> handleDouyinLogistics() {
        List<String> orderIdList = thirdPlatformOrderLogisticsSyncService.getOrderIdNeedSync();
        if (CollUtil.isNotEmpty(orderIdList)) {
            for (String orderId : orderIdList) {
                logisticsAdd(orderId);
            }
        }
        return R.success("操作成功");
    }
}
