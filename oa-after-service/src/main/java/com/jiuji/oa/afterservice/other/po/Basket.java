package com.jiuji.oa.afterservice.other.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basket")
public class Basket extends Model<Basket> implements IBasket {

    private static final long serialVersionUID = 1L;

    @TableId(value = "basket_id", type = IdType.AUTO)
    private Integer basketId;

    private Integer basketCount;

    private LocalDateTime basketDate;

    private String productPeizhi;

    private String seller;

    private Boolean ismobile;

    private BigDecimal price;

    private Long subId;

    private BigDecimal price1;

    private Long ppriceid;

    private BigDecimal inprice;

    private Integer giftid;

    /**
     * @see c# oa999Common\Enum.cs::basketType
     */
    @ApiModelProperty("搭配商品类型: 6 拍卖 1 赠品")
    private Integer type;

    private Boolean isdel;

    private Boolean ischu;

    private BigDecimal price2;

    private Boolean iskc;

    @TableField("isOnShop")
    private Boolean isOnShop;

    private BigDecimal returnPrice;
    @TableField("youhuiPrice")
    private BigDecimal youhuiPrice;
    @TableField("jifenPrice")
    private BigDecimal jifenPrice;
    @TableField("giftPrice")
    private BigDecimal giftPrice;
    @TableField("price_shouhou")
    private BigDecimal priceShouhou;

    @Override
    protected Serializable pkVal() {
        return this.basketId;
    }

}
