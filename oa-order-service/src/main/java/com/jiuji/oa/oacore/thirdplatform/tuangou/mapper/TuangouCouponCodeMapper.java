package com.jiuji.oa.oacore.thirdplatform.tuangou.mapper;

import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.CouponCodeDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.UpdateCouponCodeDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.QueryCouponCodeReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.QueryCouponCodeRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TuangouCouponCodeMapper {
    /**
     * 订单查询团购券信息
     *
     * @param req
     * @return
     */
    List<QueryCouponCodeRes> selectCouponCodeListBySub(@Param("req") QueryCouponCodeReq req);

    /**
     * 查询团购券信息
     *
     * @param id
     * @return
     */
    CouponCodeDto selectCouponCodeById(@Param("id") Integer id);

    /**
     * 查询团购券信息
     *
     * @param req
     * @return
     */
    boolean updateCouponStatusById(@Param("req") UpdateCouponCodeDto req);
}
