package com.jiuji.oa.oacore.thirdplatform.order.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.util.EnumUtil;
import com.jiuji.oa.oacore.common.util.ExcelUtils;
import com.jiuji.oa.oacore.common.util.ExcelWriterUtil;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.thirdplatform.order.bo.OrderSearchBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.JdOrder;
import com.jiuji.oa.oacore.thirdplatform.order.entity.JdOrderItem;
import com.jiuji.oa.oacore.thirdplatform.order.mapper.JdOrderMapper;
import com.jiuji.oa.oacore.thirdplatform.order.service.JdOrderItemService;
import com.jiuji.oa.oacore.thirdplatform.order.service.JdOrderService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.OrderVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存管理接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class JdOrderServiceImpl extends ServiceImpl<JdOrderMapper, JdOrder> implements JdOrderService {

    @Autowired
    private JdOrderItemService jdOrderItemService;

    @Override
    public Page<OrderVO> listByPage(OrderSearchBO search) {
        Page<OrderVO> page = new Page<>(search.getCurrent(), search.getSize());
        page.setDesc("t.order_time");
        List<OrderVO> list = baseMapper.orderList(search, page);

        rebuildList(list);
        if (CollectionUtils.isNotEmpty(list)) {
            page.setRecords(list);
        }
        return page;
    }




    private void rebuildList(List<OrderVO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            //查询订单明细
            List<Integer> outIds = list.stream().map(OrderVO::getId).distinct().collect(Collectors.toList());
            List<JdOrderItem> allItemList = CommonUtils.bigDataInQuery(outIds,jdOrderItemService::listOrderItemByOutId);
            list.forEach(obj -> {
                StringBuffer buffer = new StringBuffer();
                List<JdOrderItem> itemList = allItemList.stream().filter(ai -> Objects.equals(ai.getOutjdid(), obj.getId())).collect(Collectors.toList());
                Integer qty = itemList.stream().mapToInt(JdOrderItem::getSkuCount).sum();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    itemList.forEach(item -> {
                        String productStr = String.format("[%s]%s %sx%d", item.getPpriceid(), item.getProductName(),
                                null == item.getProductColor() ? "" : item.getProductColor(), item.getSkuCount());
                        buffer.append(productStr);
                        buffer.append("\r\n");
                    });
                }
                obj.setProductName(StringUtils.removeEnd(buffer.toString(), "\r\n"));
                obj.setSkuCount(qty);
                obj.setSubCheckName(EnumUtil.getMessageByCode(SubCheckEnum.class, obj.getSubCheck()));
                obj.setGovernmentSubsidyFlagName(Objects.equals(1, obj.getGovernmentSubsidyFlag()) ? "是" : "否");
            });
        }
    }

    @Override
    public R exportExcel(OrderSearchBO req, HttpServletResponse response) {
        String fileName = "订单列表数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xlsx";
        if (ExcelWriterUtil.outCacheFile("jdExportExcel",req, Duration.ofMinutes(NumberConstant.TEN),fileName,response)){
            return R.success("导出成功");
        }
        List<OrderVO> list = baseMapper.orderList(req, null);
        rebuildList(list);
        ArrayList<String> titles = new ArrayList<>(Arrays.asList("平台下单时间", "商户号", "平台订单号", "平台门店", "本地门店", "\t商品\t", "数量",
                "实付金额", "平台承担", "本地订单号", "本地订单状态",
                "订单备注", "错误消息"));
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<List<Object>> dataList = new ArrayList<>();
        list.forEach(obj -> {
            List<Object> cells = new ArrayList<>();
            if (null == obj.getOrderTime()) {
                cells.add("");
            } else {
                cells.add(sdf.format(obj.getOrderTime()));
            }
            cells.add(String.format("%s(%s)", obj.getTenantCode(), obj.getAreaName()));
            cells.add(obj.getOrderId());
            cells.add(obj.getStoreCode());
            cells.add(obj.getAreaCode());
            cells.add(obj.getProductName());
            cells.add(obj.getSkuCount());
            cells.add(obj.getPayableMoney());
            cells.add(obj.getPlatMoney());
            cells.add(obj.getSubId());
            cells.add(obj.getSubCheckName());
            cells.add(obj.getBuyerRemark());
            cells.add(obj.getSubMessage());
            dataList.add(cells);
        });

        try {
            ExcelUtils.export(response, dataList, titles, null,
                    fileName, 1);
        } catch (IOException e) {
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功");
    }


}


