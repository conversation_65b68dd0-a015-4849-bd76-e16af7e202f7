package com.jiuji.oa.oacore.tousu.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.common.group.Default;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tousuZeRenRen")
public class TouSuZenRenRen extends Model<TouSuZenRenRen> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("tousuId")
    @ApiModelProperty(value = "投诉id")
    @NotNull(groups = Default.class,message = "投诉id不能为空")
    private Integer tousuId;

    @ApiModelProperty(value = "责任人id")
    @TableField("userId")
    @NotNull(groups = Default.class,message = "责任人id不能为空")
    private Integer userId;

    @TableField("userName")
    @ApiModelProperty(value = "责任人")
    @NotNull(groups = Default.class,message = "责任人不能为空")
    private String userName;

    @TableField("depart_id")
    @ApiModelProperty(value = "部门id")
    private Integer departId;

    @TableField("area1id")
    @ApiModelProperty(value = "门店Id")
    private Integer area1id;

    @TableField("tousuRank")
    @ApiModelProperty(value = "投诉等级")
    private Integer tousuRank;

    @TableField("tousuPoint")
    @ApiModelProperty(value = "投诉扣除积分")
    private Integer tousuPoint;

    @TableField("tousu_lose_point")
    @ApiModelProperty(value = "投诉扣分")
    private BigDecimal tousuLosePoint;

    @ApiModelProperty(value = "90天内被投诉的次数")
    @TableField(exist = false)
    private Integer complaintCount;
}
