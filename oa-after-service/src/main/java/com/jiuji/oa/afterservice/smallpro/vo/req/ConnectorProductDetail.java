package com.jiuji.oa.afterservice.smallpro.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConnectorProductDetail {
    /**
     * 接件ppid
     */
    private Integer connectorPpid;
    /**
     * 接件ppid 数量
     */
    private Integer connectorPpidCount;
    /**
     * (1-大件附件换货 0-正常商品)
     * 接件商品是否为大件附件换货
     */
    private Integer mobileExchangeFlag;

    private Integer basketId;
}
