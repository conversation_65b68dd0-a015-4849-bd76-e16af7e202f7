package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.service;


import com.ch999.common.util.vo.Result;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.entity.HuiJiBaoNotify;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.DeleteHuiJiBaoReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.req.RegisterQrcodeReq;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res.HuijbRecoverDto;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res.RegisterQrcodeRes;
import com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.vo.HuiJiInfo;

public interface HuiJiService {

    /**
     * 获取汇机保的token
     * @return
     */
    String getHuiJiAccessToken();


    /**
     * 获取汇机保注册连接
     * @param req
     * @return
     */
    RegisterQrcodeRes getRegisterQrcode(RegisterQrcodeReq req);

    /**
     * 域名获取
     * @return
     */
    String getHost(Integer code);

    HuiJiInfo createHuiJiInfo();

    /**
     * @Description 处理汇机保回调信息
     * <AUTHOR>
     * @Date   2023/3/22 19:23
     */
    String huijbNotifyHandle(HuiJiBaoNotify huiJiBaoNotify);



    Result<String> deleteHuiJiBao(DeleteHuiJiBaoReq deleteHuiJiBaoReq);


    /**
     * 参数解析
     * @param huiJiBaoNotify
     * @return
     */
    HuijbRecoverDto packageCallbackParamHuijb(HuiJiBaoNotify huiJiBaoNotify);
}
