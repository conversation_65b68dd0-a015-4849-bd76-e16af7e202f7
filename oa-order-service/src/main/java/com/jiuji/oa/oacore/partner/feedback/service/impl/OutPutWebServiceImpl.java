package com.jiuji.oa.oacore.partner.feedback.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.partner.feedback.entity.OutPutWeb;
import com.jiuji.oa.oacore.partner.feedback.mapper.OutPutWebMapper;
import com.jiuji.oa.oacore.partner.feedback.service.OutPutWebService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/13 11:37
 */
@DS("oanew")
@Service
public class OutPutWebServiceImpl extends ServiceImpl<OutPutWebMapper, OutPutWeb> implements OutPutWebService {

    @Resource
    private OutPutWebMapper outPutWebMapper;

    @Override
    public List<OutPutWeb> getOutPutWebList(List<Integer> xTenantList) {
        return this.list(new LambdaQueryWrapper<OutPutWeb>()
                .select(OutPutWeb::getXtenant, OutPutWeb::getWebName)
                .eq(OutPutWeb::getIsDelete, 0)
                .in(OutPutWeb::getXtenant, xTenantList));
    }

    @Override
    public List<OutPutWeb> getNeoList(List<Integer> xTenantList) {
        return this.list(new LambdaQueryWrapper<OutPutWeb>()
                .select(OutPutWeb::getNameSpaceId, OutPutWeb::getWebName)
                .eq(OutPutWeb::getIsDelete, 0)
                .in(OutPutWeb::getNameSpaceId, xTenantList));
    }

    @Override
    public OutPutWeb getOutPutWebOne(Integer xTenant) {
        return outPutWebMapper.selectOutPutWebOne(xTenant);
    }

    @Override
    public OutPutWeb getNeoOne(Integer xTenant) {
        return outPutWebMapper.selectNeoOne(xTenant);
    }
}
