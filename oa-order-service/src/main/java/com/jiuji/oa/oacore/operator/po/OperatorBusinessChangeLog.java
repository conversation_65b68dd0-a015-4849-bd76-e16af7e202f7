package com.jiuji.oa.oacore.operator.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date ${Date} 9:38
 * @Description
 */

/**
 * 运营商历史办理数据同步日志[责任小组:销售]
 */
@ApiModel(value = "运营商历史办理数据同步日志[责任小组:销售]")
@Data
@TableName(value = "operator_business_change_log")
public class OperatorBusinessChangeLog implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Integer id;
    /**
     * 业务id
     */
    @TableField(value = "business_id")
    @ApiModelProperty(value = "业务id")
    private Integer businessId;
    /**
     * 操作人工号
     */
    @TableField(value = "staff_id")
    @ApiModelProperty(value = "操作人工号")
    private Integer staffId;
    /**
     * 修改范围
     */
    @TableField(value = "change_range")
    @ApiModelProperty(value = "修改范围")
    private Integer changeRange;
    /**
     * 同步开始时间
     */
    @TableField(value = "begin_time")
    @ApiModelProperty(value = "同步开始时间")
    private LocalDateTime beginTime;
    /**
     * 同步结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "同步结束时间")
    private LocalDateTime endTime;
    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 版本号
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "版本号")
    private LocalDateTime operatorBusinessChangeLogRv;
}