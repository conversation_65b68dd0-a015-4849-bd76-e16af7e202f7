package com.jiuji.oa.oacore.subRecommended.vo.req;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class RecommendedPageReq {

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    private Integer size;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Integer current;

    /**
     * 查询key
     */
    private String selectKey;


    /**
     * type 类型
     * @see com.jiuji.oa.oacore.subRecommended.enums.MainConfigSelectTypeEnum
     * (1, "商品名称"),
     * (2, "商品id");
     */
    private Integer selectType;

    /**
     * 使用地区查询
     */
    private List<Integer> applyAreaIdList;


    /**
     * @see com.jiuji.oa.oacore.subRecommended.enums.MainConfigStateEnum
     */
    private Integer state;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 1-生效时间
     * 2-交易时间
     * 时间类型
     * @see com.jiuji.oa.oacore.subRecommended.enums.MainConfigTimeTypeEnum
     */
    private Integer timeType;

    /**
     * 1-启用
     * 0-禁用
     * 是否启用
     */
    private Integer isEnabled;
}
