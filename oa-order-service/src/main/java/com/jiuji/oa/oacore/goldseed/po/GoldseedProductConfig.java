package com.jiuji.oa.oacore.goldseed.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-27
 */
@ApiModel
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("goldseed_product_config")
public class GoldseedProductConfig extends Model<GoldseedProductConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 商品分类名称
     */
    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则名
     */
    @ApiModelProperty(value = "规则名",example = "华为手机")
    private String name;

    /**
     * 用途
     */
    @ApiModelProperty(value = "用途",example = "筛选需要上送的手机")
    private String useto;

    /**
     * 商品分类id
     */
    @ApiModelProperty(value = "商品分类id",example = "1")
    private Integer categoryId;

    /**
     * 商品分类名称
     */
    @ApiModelProperty(value = "商品分类名称",example = "手机")
    private String categoryName;

    /**
     * 是否删除
     */
    @JsonIgnore
    @TableLogic
    private Boolean isDel;

    /**
     * 租户(0九机，1YY,2华为，1000智乐方，2000华腾)
     */
    @JsonIgnore
    private Integer xtenant;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private LocalDateTime updateTime;

    @JsonIgnore
    private Integer areaCode;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
