package com.jiuji.oa.afterservice.cloud.fallbackfactory;

import com.jiuji.oa.afterservice.cloud.rollback.StockCloudRollback;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @since 2021/5/28
 */
@Component
@Slf4j
public class StockCloudFallbackFactory implements FallbackFactory<StockCloudRollback> {

    private final StockCloudRollback fallback;

    public StockCloudFallbackFactory(StockCloudRollback fallback) {
        this.fallback = fallback;
    }

    @Override
    public StockCloudRollback create(Throwable throwable) {
        log.error("cloud 调用失效：", throwable);
        return fallback;
    }
}
