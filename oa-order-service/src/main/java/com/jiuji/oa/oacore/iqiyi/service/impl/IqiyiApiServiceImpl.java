package com.jiuji.oa.oacore.iqiyi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.IqiyiConstant;
import com.jiuji.oa.oacore.common.source.UrlSource;
import com.jiuji.oa.oacore.common.util.EncodeUtils;
import com.jiuji.oa.oacore.common.util.HttpClientUtil;
import com.jiuji.oa.oacore.common.util.MapUtil;
import com.jiuji.oa.oacore.iqiyi.bo.IqiyiOrderRes;
import com.jiuji.oa.oacore.iqiyi.enums.*;
import com.jiuji.oa.oacore.iqiyi.po.IqiyiOrderLog;
import com.jiuji.oa.oacore.iqiyi.service.IqiyiApiService;
import com.jiuji.oa.oacore.iqiyi.service.IqiyiOrderLogService;
import com.jiuji.oa.oacore.iqiyi.vo.req.*;
import com.jiuji.oa.oacore.iqiyi.vo.res.IdetfificationCheckRes;
import com.jiuji.oa.oacore.iqiyi.vo.res.IqiyiResult;
import com.jiuji.oa.oacore.iqiyi.vo.res.OaQueueRes;
import com.jiuji.tc.common.vo.OaApiVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.OaVerifyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class IqiyiApiServiceImpl implements IqiyiApiService {

    @Autowired
    private UrlSource urlSource;
    @Autowired
    private IqiyiOrderLogService iqiyiOrderLogService;
    @Autowired
    private RedissonClient redisson;
    @Resource
    private AmqpTemplate amqpTemplate;
    /**
     * 商品编码
     */
    static Map<Integer,Integer> ppidCode = new HashMap<>();

    static {
        ppidCode.put(89616,344);
        ppidCode.put(89684,345);
        ppidCode.put(89617,346);
        ppidCode.put(89635,347);
        ppidCode.put(89638,107);
        ppidCode.put(98637,108);
        ppidCode.put(89636,110);
    }

    @Override
    public String identificationCheck(IdentificationCheckReq identificationCheckReq) {
        TreeMap<String, String> paramMap = buildParamMap(identificationCheckReq);
        String resultStr = HttpClientUtil.post(IqiyiConstant.API_URL.IDENTIFICATION_CHECK_URL,paramMap);
        IqiyiResult result = JSON.parseObject(resultStr, IqiyiResult.class);
        if(result.getCode().equals(IqiyiCodeEnum.SUCCESS.getCode())){
            IdetfificationCheckRes idetfificationCheckRes = (IdetfificationCheckRes)result.getData();
            return idetfificationCheckRes.getSignPage();
        }
        return null;
    }

    @Override
    public R<String> createOrder(OaApiVo<OaIqiyiOrderReq> param) {
        log.info("爱奇艺充值提交订单接口参数：{}", JSON.toJSONString(param));
        R<Boolean> signFlag = OaVerifyUtil.checkSign(JiujiOaSecretEnum.JIUJI_OA_SECRET_IQIYI.getMessage(),param);
        String orderId = "";
        if(param.getData() != null && StringUtils.isNotEmpty(param.getData().getOutTradeNo())){
            orderId = param.getData().getOutTradeNo();
        }
        param.getData().getOutTradeNo();
        if(ResultCode.SUCCESS != signFlag.getCode() || !signFlag.getData()){
            iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"数据签名失败",orderId);
            return R.error("数据签名失败!orderId:" + orderId);
        }

        OaIqiyiOrderReq oaIqiyiOrderReq = param.getData();
        //验签接收数据
        if(StringUtils.isEmpty(oaIqiyiOrderReq.getOutTradeNo())){
            iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"订单号不能为空",orderId);
            return R.error("订单号不能为空!orderId:" + orderId);
        }
        if(oaIqiyiOrderReq.getUserId() == null || oaIqiyiOrderReq.getUserId() == 0){
            iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"用户id不能为空",orderId);
            return R.error("用户id不能为空!orderId:" + orderId);
        }
        if(StringUtils.isEmpty(oaIqiyiOrderReq.getRechargeAccount())){
            iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"充值账号不能为空",orderId);
            return R.error("充值账号不能为空!orderId:" + orderId);
        }
        if(!ppidCode.containsKey(oaIqiyiOrderReq.getPpid())){
            iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"对应的ppid不存在",orderId);
            return R.error("对应的ppid不存在!orderId:" + orderId);
        }
        if(oaIqiyiOrderReq.getAccountType() == null){
            oaIqiyiOrderReq.setAccountType(1);
        }

        IqiyiOrderReq iqiyiOrderReq = new IqiyiOrderReq();
        BeanUtils.copyProperties(oaIqiyiOrderReq,iqiyiOrderReq);
        iqiyiOrderReq.setMerchantId(IqiyiConstant.MERCHANT_ID);
        iqiyiOrderReq.setProductId(ppidCode.get(oaIqiyiOrderReq.getPpid()));
        iqiyiOrderReq.setNumber(1);
        long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        iqiyiOrderReq.setTimeStamp((int)timeStamp);
        iqiyiOrderReq.setNotifyUrl(urlSource.getAqiyiOrdercallback());
        TreeMap<String, String> paramMap = buildParamMap(iqiyiOrderReq);
        String key = oaIqiyiOrderReq.getUserId()+oaIqiyiOrderReq.getOutTradeNo()+oaIqiyiOrderReq.getRechargeAccount();
        RLock fairLock =  redisson.getFairLock("oaorder:createIqiyiOrder:"+key);
        IqiyiOrderLog iqiyiOrderLog  = null;
        try {
            //上锁，避免重复下单
            boolean flag = fairLock.tryLock(5, 10, TimeUnit.SECONDS);
            if(flag){
                iqiyiOrderLog  = iqiyiOrderLogService.getIqiyiOrderLog(oaIqiyiOrderReq.getUserId(),oaIqiyiOrderReq.getOutTradeNo(),oaIqiyiOrderReq.getRechargeAccount());
                if(iqiyiOrderLog != null && OrderStatusEnum.SUCCESS.getCode().equals(iqiyiOrderLog.getOrderStatus())
                && PayStatusEnum.SUCCESS.getCode().equals(iqiyiOrderLog.getPayStatus())){
                    iqiyiSendMsg(OaIqiyiStatusEnum.PAY_SUCCESS.getCode(),"该订单已存在,并且已支付成功！",orderId);
                    return R.success("该订单已存在,并且已支付成功！orderId:" + orderId);
                }
                if(iqiyiOrderLog != null){
                    iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"该订单已存在",orderId);
                    return R.error("该订单已存在!orderId:" + orderId);
                }

                String result = sendAndCallback(IqiyiConstant.API_URL.ORDER_SYNC_URL,paramMap);
                //写日志
                iqiyiOrderLog = new IqiyiOrderLog();
                BeanUtils.copyProperties(iqiyiOrderReq,iqiyiOrderLog);
                iqiyiOrderLog.setUserId(oaIqiyiOrderReq.getUserId());
                iqiyiOrderLog.setCreateTime(LocalDateTime.now());
                iqiyiOrderLog.setSign(param.getSign());
                if(StringUtils.isNotEmpty(result)){
                    IqiyiOrderRes iqiyiOrderRes = JSON.parseObject(result, IqiyiOrderRes.class);
                    iqiyiOrderLog.setResultCode(iqiyiOrderRes.getCode());
                    iqiyiOrderLog.setResultMsg(iqiyiOrderRes.getMessage());
                    if(iqiyiOrderRes.getCode().equals(IqiyiCodeEnum.REQUEST_TIMEOUT.getCode())){
                        iqiyiOrderLog.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
                        if(iqiyiOrderLog.getId() == null || iqiyiOrderLog.getId() == 0){
                            iqiyiOrderLogService.saveIqiyiOrderLog(iqiyiOrderLog);
                        }
                        return R.success("下单成功!" + orderId);
                    }
                    iqiyiOrderLog.setOrderStatus(OrderStatusEnum.FAIL.getCode());
                    iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"创建订单失败："+iqiyiOrderRes.getMessage(),orderId);
                    if(iqiyiOrderLog.getId() == null || iqiyiOrderLog.getId() == 0){
                        iqiyiOrderLogService.saveIqiyiOrderLog(iqiyiOrderLog);
                    }
                    return R.success("创建订单失败!orderId:" + orderId + "," + iqiyiOrderRes.getMessage());
                }
            }
        }catch (Exception e){
            if(iqiyiOrderLog == null){
                iqiyiOrderLog = new IqiyiOrderLog();
            }
            iqiyiOrderLog.setResultData("爱奇艺充值异常:"+e.getMessage());
            iqiyiOrderLog.setOrderStatus(OrderStatusEnum.FAIL.getCode());
            log.error("爱奇艺充值异常：{}",e.getMessage());
        }finally {
            //解锁
            fairLock.unlock();
        }
        if(iqiyiOrderLog != null && (iqiyiOrderLog.getId() == null || iqiyiOrderLog.getId() == 0)){
            iqiyiOrderLogService.saveIqiyiOrderLog(iqiyiOrderLog);
        }
        iqiyiSendMsg(OaIqiyiStatusEnum.ORDER_FAIL.getCode(),"创建订单失败",orderId);
        return R.error("创建订单失败!orderId:" + orderId);
    }

    @Override
    public R<Integer> getOrderStatus(OrderStatusQueryReq orderStatusQueryReq) {
        TreeMap<String, String> paramMap = buildParamMap(orderStatusQueryReq);
        return R.error(ResultCode.RETURN_ERROR,"");
    }

    @Override
    public String rechargeOrderCallback(RechargeOrderCallbackReq rechargeOrderCallbackReq) {
        if(rechargeOrderCallbackReq != null){
            try {
                //验证签名
                TreeMap<String, String> paramMap = new TreeMap<>();
                paramMap.put("merchantId",rechargeOrderCallbackReq.getMerchantId());
                paramMap.put("outTradeNo",String.valueOf(rechargeOrderCallbackReq.getOutTradeNo()));
                paramMap.put("rechargeAccount",rechargeOrderCallbackReq.getRechargeAccount());
                paramMap.put("status",rechargeOrderCallbackReq.getStatus());
                String sign = EncodeUtils.getIqiyiMD5Sign(paramMap);
                //签名不成功，不接收该数据
                log.info("本地签名："+sign);
                log.info("远程签名："+rechargeOrderCallbackReq.getSign());
                log.info("参数："+JSON.toJSONString(rechargeOrderCallbackReq));
                if(!sign.equals(rechargeOrderCallbackReq.getSign())){
                    return "success";
                }
                //充值回调记录
                iqiyiOrderLogService.updatePayStatus(rechargeOrderCallbackReq);
                //队列消息推送
                if(PayStatusEnum.SUCCESS.getIqiyiPayStatus().equals(rechargeOrderCallbackReq.getStatus())){
                    iqiyiSendMsg(OaIqiyiStatusEnum.PAY_SUCCESS.getCode(),"支付成功",rechargeOrderCallbackReq.getOutTradeNo().toString());
                }else{
                    iqiyiSendMsg(OaIqiyiStatusEnum.PAY_FAIL.getCode(),"支付失败",rechargeOrderCallbackReq.getOutTradeNo().toString());
                }
            }catch (Exception e){
                log.error("mq消息推送异常:{}",e.getMessage());
            }
        }
        return "success";
    }

    /**
     * 爱奇艺oa队列消息推送
     */
    private void iqiyiSendMsg(Integer status,String msg,String orderId){
        try {
            OaQueueRes  oaQueueRes = new OaQueueRes();
            oaQueueRes.setAct("iqiyi");
            OaQueueRes.QueueData data = new OaQueueRes.QueueData();
            data.setMsg(msg);
            data.setStatus(status);
            data.setData(new OaQueueRes.YewuData(orderId));
            oaQueueRes.setData(data);
            String json = JSONObject.toJSONString(oaQueueRes);
            amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, JSONObject.toJSONString(oaQueueRes));
        }catch (Exception e){
            log.error("mq消息推送异常:{}",e.getMessage());
        }
    }

    private TreeMap<String, String> buildParamMap(Object o){
        TreeMap<String, String> paramMap = MapUtil.object2Map(o);
        String sign = EncodeUtils.getIqiyiMD5Sign(paramMap);
        paramMap.put("sign",sign);
        paramMap.put("version","1.0");
        return paramMap;
    }

    private String sendAndCallback(String url,TreeMap<String, String> paramMap){
        int time = 0;
        int maxRetry = 3;
        String resultStr = "";
        long sleepTime = 0;
        do {
            try {
                if(time == 1){
                    sleepTime = 1000;
                }else if(time == 2){
                    sleepTime = 1000 * 5L;
                }
                if(time > 0){
                    Thread.sleep(sleepTime);
                }
                time++;
                resultStr = HttpClientUtil.post(url,paramMap);
            }catch (Exception e){
                log.error("爱奇艺接口请求异常:{}",e.getMessage());
            }
        } while (StringUtils.isBlank(resultStr) && time < maxRetry);
        return resultStr;
    }
}
