package com.jiuji.oa.oacore.thirdplatform.yading.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * @Description 亚丁对接接口返回编码枚举
 * <AUTHOR>
 * @Date   2022/5/10 17:07
 */
@Getter
@RequiredArgsConstructor
public enum YadingResultEnum {
    /**
     * 丁对接接口返回编码枚举
     * 1为成功这个是亚丁定的，不要修改
     */
    SUCCESS(1, "成功"),
    SING_ERROR_1(1001, "加签异常"),
    SING_ERROR_2(1002, "验签未通过"),
    PARAMETER_FORMAT_ERROR(1003, "参数格式异常"),
    REQUEST_BODY_EMPTY(1004, "请求体为空"),
    UNKNOWN_METHOD(1005, "未知的业务通知类型"),
    UNKNOWN_SUBID(1006, "未知的业务单号"),
    UNKNOWN_CHECK_STATUS(1007, "未知的审核状态"),
    REPETITION_SUBMIT_REGISTER(1008, "审核已通过，服务生效中"),
    COMPANY_CODE_ERROR(1009, "公司编号错误"),
    ;

    private final Integer value;

    private final String msg;
}
