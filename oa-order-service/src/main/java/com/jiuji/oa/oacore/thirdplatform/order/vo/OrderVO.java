/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.order.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 第三方订单Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "第三方订单实体")
public class OrderVO extends Model<OrderVO> {
    private static final long serialVersionUID = 4L;

    private Integer id;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    private String platCode;

    /**
     * 平台订单id
     */
    private String orderId;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /**
     * 商户编码
     */
    private String tenantCode;

    /**
     * 本地门店id
     */
    private Integer areaId;

    /**
     * 本地门店编码
     */
    private String areaCode;

    /**
     * 本地门店名称
     */
    private String areaName;

    /**
     * 平台门店编码
     */
    private String storeCode;

    /**
     * 买家账号
     */
    private String buyerPin;

    /**
     * 收货人名称
     */
    private String buyerName;

    /**
     * 收货人地址
     */
    private String buyerAddress;

    /**
     * 收货人手机号
     */
    private String buyerMobile;

    /**
     * 收货人电话
     */
    private String buyerTel;

    /**
     * 成交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;

    /**
     * 订单商品销售总金额
     */
    private Double totalMoney;

    /**
     * 订单优惠金额
     */
    private Double discountMoney;

    /**
     * 订单货款总金额 [订单货款金额（订单总金额-商家优惠金额）]
     */
    private Double goodMoney;

    /**
     * 用户积分抵扣金额
     */
    private Double pointMoney;

    /**
     * 订单实际运费
     */
    private Double freightMoney;

    /**
     * 用户应付金额
     */
    private Double payableMoney;

    /**
     * 收货人市id
     */
    private String buyerCity;

    /**
     * 收货人市名称
     */
    private String buyerCityName;

    /**
     * 收货人区县id
     */
    private String buyerCountry;

    /**
     * 收货人区县名称
     */
    private String buyerCountryName;

    /**
     * 订单备注
     */
    private String buyerRemark;

    /**
     * 平台订单状态
     */
    private Integer orderStatus;

    /**
     * 本地订单id
     */
    private Long subId;

    /**
     * 订单生成消息
     */
    private String subMessage;

    /**
     * 支付状态，对OA订单是否已支付
     */
    private Integer payStatus;

    /**
     * 商家承担金额
     */
    private Double venderMoney;

    /**
     * 平台承担金额
     */
    private Double platMoney;

    /**
     * 本地订单状态
     */
    private Integer subCheck;

    /**
     * 订单取消状态 1-取消
     */
    private Integer cancelCheck;

    /**
     * 订单取消原因
     */
    private String cancelReason;

    private String subCheckName;
    private String productName;
    private Integer skuCount;
    private String tenantName;


    /**
     * 预计送达时间
     */
    private Date estimateArrivalTime;


    /**
     * 0--普通配送
     * 1--用户到店自取
     */
    private Integer pickType;

    /**
     * 国补订单标志
     */
    private Integer governmentSubsidyFlag;
    /**
     * 国补订单标志
     */
    private String governmentSubsidyFlagName;
}
