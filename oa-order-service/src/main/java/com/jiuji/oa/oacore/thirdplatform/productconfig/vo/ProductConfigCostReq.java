/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
/**
 * 商品关系配置展现层
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@ApiModel(value = "商品关系配置")
public class ProductConfigCostReq {

    private String platCode;

    private String tenantCode;

    private List< Integer> productConfigId;

}
