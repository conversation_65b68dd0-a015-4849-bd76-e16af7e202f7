package com.jiuji.oa.oacore.thirdplatform.yading.dto;

import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingAppleProTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * @Description 注册亚丁保险所需的字段参数
 * 注意：字段名不是小驼峰风格的，这个是亚丁定的，不要来问我问什么要用下划线分割
 * <AUTHOR>
 * @Date   2022/5/11 20:17
 */
@Data
public class YadingRegisterQrcodeDto {


    /**
     * 员工识别码
     * 员工识别码为：租户号+_+员工工号
     * 使用 YadingUtil#getMemberCode方法获取
     */
    @NotBlank(message = "员工识别码不能为空")
    private String member_code;

    /**
     * 九机业务单号
     * 这里传detailId
     */
    @NotBlank(message = "业务单号不能为空")
    private String sn;

    /**
     * 保险年限：1年、2年、次年
     * @see com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingYearTypeEnum
     */
    @NotBlank(message = "保险年限不能为空")
    private String years;

    /**
     * 手机机型：1苹果  2安卓
     * @see com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingPhoneTypeEnum
     */
    @NotBlank(message = "手机机型不能为空")
    private String type;

    /**
     * 安卓机价格（安卓才传）
     */
    @NotBlank(message = "安卓机价格不能为空")
    private String price;

    /**
     * 苹果pro版本（苹果才传）
     * @see YadingAppleProTypeEnum
     */
    @NotBlank(message = "苹果pro版本不能为空")
    private String is_pro;

    /**
     * 手机串号
     */
    @NotBlank(message = "手机串号不能为空")
    private String imei;

    /**
     * 九机保险名称id
     * 九讯延长保、九讯电池保、九讯care+、九讯盾....
     * @see com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingJiuxunNameMapEnum
     */
    @NotBlank(message = "九讯保险名称id不能为空")
    private String insure_jiu;

    /**
     * 亚丁保险编号
     */
    @NotBlank(message = "亚丁保险编号不能为空")
    private String insure;

    /**
     * 子保险编码串
     */
    private String son;

    /**
     * 会员姓名
     */
    private String name;

    /**
     * 会员电话
     */
    private String mobile;

    private String brand;
    private String model;

    private String ram_size;

}
