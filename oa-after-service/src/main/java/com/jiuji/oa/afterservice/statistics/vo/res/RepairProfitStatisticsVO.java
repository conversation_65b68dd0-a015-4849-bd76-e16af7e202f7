package com.jiuji.oa.afterservice.statistics.vo.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description: < 维修毛利分析VO >
 * translation: <  >
 * date : 2020-10-20 20:56
 *
 * <AUTHOR> leee41
 **/
@Data
@ApiModel(value = "维修毛利分析VO")
@Slf4j
@Accessors(chain = true)
public class RepairProfitStatisticsVO implements Serializable, Cloneable {
    private static final long serialVersionUID = -8797439569239740664L;

    @ApiModelProperty(value = "地区")
    private String area;
    @ApiModelProperty(value = "分类id")
    private String cid;
    @ApiModelProperty(value = "地区ID")
    private Integer areaId;
    @ApiModelProperty(value = "有毛利数量")
    private Integer hasProfitNum;
    @ApiModelProperty(value = "无毛利数量")
    private Integer noProfitNum;
    @ApiModelProperty(value = "毛利")
    private BigDecimal profit;
    private Integer departRank;
    private String areaLevel1Code;
    private String areaLevel2Code;

    private Integer bigAreaDeptId;

    private Integer smallAreaDept;

    private String bigArea;

    private String smallArea;


    @JsonIgnore
    private Boolean enable = Boolean.FALSE;

    @Override
    public RepairProfitStatisticsVO clone() {
        RepairProfitStatisticsVO clone = null;
        try {
            clone = (RepairProfitStatisticsVO) super.clone();

        } catch (CloneNotSupportedException e) {
            log.warn("clone fail");
        }
        return clone;
    }
}
