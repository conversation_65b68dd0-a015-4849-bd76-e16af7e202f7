package com.jiuji.oa.oacore.yearcardtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 年包转赠日志实体类
 */
@Data
@TableName("year_package_transfer_log")
public class YearPackageTransferLogPo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务ID
     */
    @TableField(value = "business_id", fill = FieldFill.INSERT)
    private Integer businessId;

    /**
     * 操作内容
     */
    @TableField("content")
    private String content;

    /**
     * 操作时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @TableField("in_user")
    private String inUser;
}
