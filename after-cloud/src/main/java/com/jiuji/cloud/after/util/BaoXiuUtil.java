package com.jiuji.cloud.after.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuParam;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuVo;
import com.jiuji.tc.utils.business.small.SmallproUtil;
import com.jiuji.tc.utils.common.CommonUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/10/30 16:21
 */
public class BaoXiuUtil {

    /**
     * 根据订单类型处理不同质保时间
     */
    private static Map<BaoXiuParam.OrderTypeEnum, Function<BaoXiuParam, BaoXiuVo>> handleBaoXiuMap = new ConcurrentHashMap<>();

    static {
        handleBaoXiuMap.put(BaoXiuParam.OrderTypeEnum.NEW_MACHINE, BaoXiuUtil::newMachine);
        handleBaoXiuMap.put(BaoXiuParam.OrderTypeEnum.EXCELLENT_PRODUCT, BaoXiuUtil::excellentProduct);
        handleBaoXiuMap.put(BaoXiuParam.OrderTypeEnum.GOOD_PRODUCT, BaoXiuUtil::goodProduct);
    }

    /**
     * 计算质保时间
     */
    public static BaoXiuVo calculateBaoXiu(BaoXiuParam param) {
        if (handleBaoXiuMap.get(param.getOrderTypeEnum()) == null){
            throw new RuntimeException("不支持该订单类型");
        }
        return handleBaoXiuMap.get(param.getOrderTypeEnum()).apply(param);
    }

    public static int getWuLiYouDays(Long xtenant){
        if(ObjectUtil.defaultIfNull(xtenant, 0L) < 1000){
            return 7;
        }
        return 15;
    }

    /**
     * 新机
     * @param param
     * @return
     */
    private static BaoXiuVo newMachine(BaoXiuParam param){
        BaoXiuVo result;
        BaoXiuVo.Context context = commonCheckAndSetDefault(param);
        if(Boolean.TRUE.equals(param.getIsMobile())){
            //大件
            result = commonsetResult(param, context, -1);
        }else{
            //小件
            result = smallProSetResult(param, context);
        }
        return result;
    }


    /**
     * 优品
     * @param param
     * @return
     */
    private static BaoXiuVo excellentProduct(BaoXiuParam param) {
        // 微调天数 大件-1 小件0
        if(Boolean.TRUE.equals(param.getIsMobile())){
            //大件
            param.setRefundDay(7);
            param.setReplaceDay(0);
            param.setRepairDay(365);
        }else{
            //小件
            param.setRefundDay(7);
            param.setReplaceDay(0);
            param.setRepairDay(0);
        }
        BaoXiuVo.Context context = commonCheckAndSetDefault(param);
        BaoXiuVo result = commonsetResult(param, context, 0);
        return result;
    }

    /**
     * 良品
     * @param param
     * @return
     */
    private static BaoXiuVo goodProduct(BaoXiuParam param) {
        param.setRefundDay(getWuLiYouDays(param.getXtenant()));
        if(Boolean.TRUE.equals(param.getIsMobile())){
            //大件
            param.setReplaceDay(0);
            param.setRepairDay(365);
        }else{
            //小件 不支持修, 不支持换
            param.setReplaceDay(0);
            param.setRepairDay(0);
        }
        BaoXiuVo.Context context = commonCheckAndSetDefault(param);
        BaoXiuVo result = commonsetResult(param, context, 0);
        return result;
    }



    private static BaoXiuVo commonsetResult(BaoXiuParam param, BaoXiuVo.Context context, int fineTuningDays) {
        BaoXiuVo result = new BaoXiuVo();
        LocalDateTime startTime = context.getStartTime();
        result.setExchangeTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(getEndTime(startTime, param.getReplaceDay(), fineTuningDays)).days(param.getReplaceDay()).build());
        result.setRepairTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(getEndTime(startTime, param.getRepairDay(), fineTuningDays)).days(param.getRepairDay()).build());
        result.setReturnTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(getEndTime(startTime, param.getRefundDay(), fineTuningDays)).days(param.getRefundDay()).build());
        result.setWarrantyTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(getEndTime(startTime, context.getWarrantyDay(), fineTuningDays)).days(context.getWarrantyDay()).build());
        // now 还在开始结束时间之间
        result.setIsExchange(isBaoXiu(context.getNow(), result.getExchangeTimeRange()));
        result.setIsRepair(isBaoXiu(context.getNow(), result.getRepairTimeRange()));
        result.setIsReturn(isBaoXiu(context.getNow(), result.getReturnTimeRange()));
        result.setIsWarranty(isBaoXiu(context.getNow(), result.getWarrantyTimeRange()));
        return result;
    }

    private static BaoXiuVo smallProSetResult(BaoXiuParam param, BaoXiuVo.Context context) {
        BaoXiuVo result = new BaoXiuVo();
        LocalDateTime startTime = context.getStartTime();
        result.setExchangeTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(SmallproUtil.getEndTime(startTime, param.getReplaceDay())).days(param.getReplaceDay()).build());
        result.setRepairTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(SmallproUtil.getEndTime(startTime, param.getRepairDay())).days(param.getRepairDay()).build());
        result.setReturnTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(SmallproUtil.getEndTime(startTime, param.getRefundDay())).days(param.getRefundDay()).build());
        result.setWarrantyTimeRange(BaoXiuVo.TimeRangeVo.builder().startTime(context.getStartTime())
                .endTime(SmallproUtil.getEndTime(startTime, context.getWarrantyDay())).days(context.getWarrantyDay()).build());
        // now 还在开始结束时间之间
        result.setIsExchange(isBaoXiu(context.getNow(), result.getExchangeTimeRange()));
        result.setIsRepair(isBaoXiu(context.getNow(), result.getRepairTimeRange()));
        result.setIsReturn(isBaoXiu(context.getNow(), result.getReturnTimeRange()));
        result.setIsWarranty(isBaoXiu(context.getNow(), result.getWarrantyTimeRange()));
        return result;
    }

    private static BaoXiuVo.Context commonCheckAndSetDefault(BaoXiuParam param) {
        if(param.getOrderTypeEnum() == null){
            throw new RuntimeException("订单类型不能为空");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.plusDays(-1);
        param.setReplaceDay(ObjectUtil.defaultIfNull(param.getReplaceDay(), 0));
        param.setRefundDay(ObjectUtil.defaultIfNull(param.getRefundDay(), 0));
        param.setRepairDay(ObjectUtil.defaultIfNull(param.getRepairDay(), 0));

        //质保时间
        Integer warrantyDay = NumberUtil.max(param.getReplaceDay(), param.getRefundDay(), param.getRepairDay());

        // 没有传入交易时间, 则开始时间默认为
        LocalDateTime startTime = param.getTradeCompleteTime();
        // 如果开始时间为null, 取出库时间
        if (ObjectUtil.isNull(startTime)) {
            startTime = param.getOutStockTime();
        }
        // 如果开始时间为null, 取下单时间
        if (ObjectUtil.isNull(startTime)) {
            startTime = param.getSubDateTime();
        }
        // 全部为空预设昨天为到期时间
        if (ObjectUtil.isNull(startTime)) {
            startTime = yesterday.plusDays(-warrantyDay);
        }
        return BaoXiuVo.Context.builder().yesterday(yesterday).now(now).warrantyDay(warrantyDay).startTime(startTime).build();
    }

    /**
     * 获取结束时间
     * @param startTime
     * @param days
     * @param fineTuningDays 微调天数
     * @return
     */
    private static LocalDateTime getEndTime(LocalDateTime startTime, Integer days, int fineTuningDays) {
        if(startTime == null || ObjectUtil.defaultIfNull(days,0) <=0){
            return startTime;
        }
        int dd = days + fineTuningDays;
        if(dd < 0){
            return startTime;
        }
        return CommonUtils.getEndOfDay(startTime).plusDays(dd);

    }

    private static boolean isBaoXiu(LocalDateTime now, BaoXiuVo.TimeRangeVo timeRangeVo){
        return !now.isBefore(timeRangeVo.getStartTime()) && !now.isAfter(timeRangeVo.getEndTime());
    }

    private BaoXiuUtil(){

    }
}
