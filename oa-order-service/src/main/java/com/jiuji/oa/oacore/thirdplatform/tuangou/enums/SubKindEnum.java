package com.jiuji.oa.oacore.thirdplatform.tuangou.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SubKindEnum implements CodeMessageEnumInterface {
    SALES_ORDER(1, "销售单"),
    GOOD_PRODUCT(2, "良品单"),
    REPAIR_ORDERS(3, "维修单"),

    ;
    private final Integer code;
    private final String message;
}
