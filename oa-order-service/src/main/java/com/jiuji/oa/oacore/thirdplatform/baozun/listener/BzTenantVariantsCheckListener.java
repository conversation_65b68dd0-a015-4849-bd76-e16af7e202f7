package com.jiuji.oa.oacore.thirdplatform.baozun.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantVariantsCheckData;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantVariants;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantVariantsService;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import jodd.typeconverter.Convert;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/16 10:28
 */
@Slf4j
@Data
public class BzTenantVariantsCheckListener extends AnalysisEventListener<LinkedHashMap> {

    private ProductinfoService productinfoService;
    private BzTenantVariantsService bzTenantVariantsService;

    public BzTenantVariantsCheckListener() {

        this.productinfoService = SpringUtil.getBean(ProductinfoService.class);
        this.bzTenantVariantsService= SpringUtil.getBean(BzTenantVariantsService.class);
    }

    List<BzTenantVariantsCheckData> checkList = new ArrayList<>();
    List<BzTenantVariantsCheckData> list = new ArrayList<>();
    List<BzTenantVariants> updateList = new ArrayList<>();

    private static final Integer BATCH_COUNT = 1500;
    private String errorMsg = "";

    @Override
    public void invoke(LinkedHashMap data, AnalysisContext analysisContext) {
        if (StringUtils.isNotBlank(errorMsg)) {
            return;
        }
        if (analysisContext.readRowHolder().getRowIndex()==1){
            return;
        }
        BzTenantVariantsCheckData bzTenantVariantsCheckData = new BzTenantVariantsCheckData();
        //行号
        bzTenantVariantsCheckData.setRowNum(analysisContext.readRowHolder().getRowIndex() + 1);
        bzTenantVariantsCheckData.setPpriceId(Optional.ofNullable(Convert.toString(analysisContext.readRowHolder().getCellMap().get(0))).orElse("").trim());
        bzTenantVariantsCheckData.setNppPrice(Optional.ofNullable(Convert.toString(analysisContext.readRowHolder().getCellMap().get(1))).orElse("").trim());
        bzTenantVariantsCheckData.setEcppPrice(Optional.ofNullable(Convert.toString(analysisContext.readRowHolder().getCellMap().get(2))).orElse("").trim());
        bzTenantVariantsCheckData.setUpc(Optional.ofNullable(Convert.toString(analysisContext.readRowHolder().getCellMap().get(3))).orElse("").trim());
        list.add(bzTenantVariantsCheckData);
        if (list.size() >= BATCH_COUNT) {
            checkList.addAll(checkExcelData(list));
            //清理 list
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        checkList.addAll(checkExcelData(list));
        //清理 list
        list.clear();
        log.info("excel数据校验完成！");
    }

    /**
     * 校验导入excel数据
     * @param list
     * @return
     */
    private List<BzTenantVariantsCheckData> checkExcelData(List<BzTenantVariantsCheckData> list) {
        if (CollectionUtils.isNotEmpty(list) && list.size() > 1000) {
            errorMsg = "导入数据最多为1000条数据";
            return list;
        }
        Map<String, String> checkMscMap = checkBack(list);
        list.forEach(v -> v.setCheckReason(checkMscMap.get(v.getUpc())));
        return list;
    }

    /**
     * 校验返利结算导入
     * @param list
     * @return
     */
    private Map<String, String> checkBack(List<BzTenantVariantsCheckData> list) {
        Map<String, String> resultMap = new HashMap<>();

        List<String> upcList = list.stream().map(BzTenantVariantsCheckData::getUpc).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, BzTenantVariants> bzTenantVariantsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(upcList)){
            bzTenantVariantsMap = bzTenantVariantsService.lambdaQuery()
                    .in(BzTenantVariants::getVariantCode, upcList).list()
                    .stream().collect(Collectors.toMap(BzTenantVariants::getVariantCode, Function.identity(), (v1, v2) -> v1));
        }

        for (BzTenantVariantsCheckData x : list) {
            String checkReason = "";
            BzTenantVariants bzTenantVariants = bzTenantVariantsMap.get(x.getUpc());
            if (Objects.isNull(bzTenantVariants)) {
                checkReason += "upc不存在，请确认upc是否正确;";
            }
            Integer ppriceId = null;
            try {
                if (StringUtils.isNotEmpty(x.getPpriceId())) {
                    ppriceId = Convert.toInteger(Convert.toDouble(x.getPpriceId()));
                    BzTenantVariants exist = bzTenantVariantsService.lambdaQuery()
                            .eq(BzTenantVariants::getPpriceid, ppriceId).list()
                            .stream().findFirst().orElse(null);
                    if (Objects.nonNull(exist)) {
                        checkReason += "SKU不允许重复录入;";
                    }
                    Productinfo productinfo = productinfoService.getProductinfoByPpid(Arrays.asList(ppriceId)).stream()
                            .findFirst().orElse(null);
                    if (Objects.isNull(productinfo)) {
                        checkReason += "找不到所配置的ppid;";
                    }
                }
            }catch (Exception e){
                checkReason += "SKU格式错误;";
            }
            String strReg = "^[0-9]+(\\.[0-9]{1,4})?$";
            BigDecimal ecppPrice = null;
            if (StringUtils.isNotEmpty(x.getEcppPrice())) {
                if (!x.getEcppPrice().matches(strReg)) {
                    checkReason += "ecpp金额格式不符合,请填写正数金额，最多保留4位小数;";
                } else {
                    ecppPrice = Convert.toBigDecimal(x.getEcppPrice());
                }
            }
            BigDecimal nppPrice = null;
            if (StringUtils.isNotEmpty(x.getNppPrice())) {
                if (!x.getNppPrice().matches(strReg)) {
                    checkReason += "npp金额格式不符合,请填写正数金额，最多保留4位小数;";
                } else {
                    nppPrice = Convert.toBigDecimal(x.getNppPrice());
                }
            }
            resultMap.put(x.getUpc(), checkReason);
            if (StringUtils.isEmpty(checkReason)) {
                LambdaBuild<BzTenantVariants> bzTenantVariantsLambdaBuild = LambdaBuild.create(BzTenantVariants.class);
                bzTenantVariantsLambdaBuild.set(BzTenantVariants::setId, bzTenantVariants.getId());
                if (Objects.nonNull(ppriceId)){
                    bzTenantVariantsLambdaBuild.set(BzTenantVariants::setPpriceid, ppriceId);
                }
                if (Objects.nonNull(ecppPrice)){
                    bzTenantVariantsLambdaBuild.set(BzTenantVariants::setEcppPrice, ecppPrice);
                }
                if (Objects.nonNull(nppPrice)){
                    bzTenantVariantsLambdaBuild.set(BzTenantVariants::setNppPrice, nppPrice);
                }
                bzTenantVariantsLambdaBuild.set(BzTenantVariants::setVariantCode, Convert.toString(x.getUpc()));
                bzTenantVariantsLambdaBuild.set(BzTenantVariants::setIsEnabled, true);
                BzTenantVariants temp = bzTenantVariantsLambdaBuild.build();
                updateList.add(temp);
            }
        }
        return resultMap;
    }
}
