package com.jiuji.oa.afterservice.smallpro.vo.req;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 11:50
 * @Description 小件换货配置查询参数
 */
@Data
@Accessors(chain = true)
@ApiModel("小件换货配置商品保存")
public class ExchangeGoodsConfigProductSaveReq implements Serializable {
    /**
     * 主配置id
     * sku，spu、品牌、分类
     * 品牌 + 分类：品牌信息
     */
    private List<Integer> mainIdList;
    /**
     * 副配置id
     * 品牌 + 分类：分类信息
     */
    private List<Integer> cidList;
}
