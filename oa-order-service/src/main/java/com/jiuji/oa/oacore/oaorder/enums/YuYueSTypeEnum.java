package com.jiuji.oa.oacore.oaorder.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @author: gengjiaping
 * @date: 2020/3/17
 */
@Getter
@AllArgsConstructor
public enum YuYueSTypeEnum implements CodeMessageEnumInterface {
    YYDD(1,"预约到店"),
    SMQJ(2,"上门取件"),
    YJSX(3,"邮寄送修"),
    SMKX(4,"上门快修"),
    DMYY(5,"店面预约"),
    SMAZ(6,"上门安装");
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;


    /**
     * 判断类型是不是为预约单延迟推送类型
     * @param type
     * @return
     */
    public static Boolean isAppointmentFormPushType(Integer type){
        AtomicReference<Boolean> flag= new AtomicReference<>(Boolean.FALSE);
        Optional.ofNullable(type).ifPresent(item->{
            List<Integer> list = Arrays.asList(YYDD.getCode(), SMQJ.getCode(), YJSX.getCode());
            if(list.contains(type)){
                 flag.set(Boolean.TRUE);
            }
        });
        return flag.get();
    }

}
