package com.jiuji.oa.oacore.thirdplatform.baozun.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantVariants;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.req.BzTenantVariantsSearchReq;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantVariantsRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date ${Date} 18:38
 * @Description
 */
@Mapper
public interface BzTenantVariantsMapper extends BaseMapper<BzTenantVariants> {
    /**
     * 商品配置信息查询
     *
     * @param bzPage
     * @param req
     * @return
     */
    Page<BzTenantVariantsRes> getQueryList(Page<BzTenantVariantsRes> bzPage, @Param("req") BzTenantVariantsSearchReq req);

    List<String> listMarketClassification();

}
