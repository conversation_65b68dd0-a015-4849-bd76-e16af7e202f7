package com.jiuji.oa.afterservice.other.vo.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> quan
 * @description: 售前退订时效
 * @date 2021/7/26 13:41
 */

@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel
public class PreSaleRefundTimeLinesRes {

    @ApiModelProperty(value = "退款方式")
    private String refundWay;

    @ApiModelProperty(value = "办理时效")
    private List<String> handleTimeLines;

    @ApiModelProperty(value = "到账时效")
    private List<String> accountTimeLines;
}
