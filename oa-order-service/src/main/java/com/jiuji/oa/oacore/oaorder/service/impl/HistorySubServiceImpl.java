package com.jiuji.oa.oacore.oaorder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.oaorder.bo.SubHistoryDataBo;
import com.jiuji.oa.oacore.oaorder.dao.HistorySubMapper;
import com.jiuji.oa.oacore.oaorder.po.HistorySubData;
import com.jiuji.oa.oacore.oaorder.service.HistorySubService;
import com.jiuji.oa.oacore.oaorder.vo.res.QueryTimeRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2020/4/7 9:38
 */

@Service
@Slf4j
public class HistorySubServiceImpl extends ServiceImpl<HistorySubMapper,HistorySubData> implements HistorySubService {




}
