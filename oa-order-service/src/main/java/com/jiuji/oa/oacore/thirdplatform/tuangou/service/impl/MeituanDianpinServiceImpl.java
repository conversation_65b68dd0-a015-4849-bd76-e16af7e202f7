package com.jiuji.oa.oacore.thirdplatform.tuangou.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.dianping.openapi.sdk.api.customerauth.session.CustomerShopIdMappingQuery;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerShopIdMappingRequest;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerShopIdMappingResponse;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerShopIdMappingResponseEntity;
import com.dianping.openapi.sdk.api.oauth.CustomerRefreshToken;
import com.dianping.openapi.sdk.api.oauth.DynamicToken;
import com.dianping.openapi.sdk.api.oauth.entity.CustomerRefreshTokenResponse;
import com.dianping.openapi.sdk.api.oauth.entity.DynamicTokenRequest;
import com.dianping.openapi.sdk.api.oauth.entity.DynamicTokenResponse;
import com.dianping.openapi.sdk.api.oauth.entity.RefreshTokenRequest;
import com.dianping.openapi.sdk.api.oauth.enums.GrantType;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptConsume;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptPrepare;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptReverseConsume;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptScanPrepare;
import com.dianping.openapi.sdk.api.tuangou.entity.*;
import com.dianping.openapi.sdk.httpclient.DefaultOpenAPIClient;
import com.jiuji.oa.oacore.apollo.ApolloEntity;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TuangouPlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.order.entity.MtPaymentLog;
import com.jiuji.oa.oacore.thirdplatform.order.service.MtPaymentLogService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tuangou.bo.OrderQueryInfo;
import com.jiuji.oa.oacore.thirdplatform.tuangou.bo.OrderQueryInfoRequest;
import com.jiuji.oa.oacore.thirdplatform.tuangou.bo.OrderQueryInfoResponse;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.service.MeituanDianpinService;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.MeituanTuangouToken;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.*;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 抖音生活服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class MeituanDianpinServiceImpl implements MeituanDianpinService {
    private static final Integer SUCCESS_CODE = 200;
    private static final DefaultOpenAPIClient OPEN_API_CLIENT = new DefaultOpenAPIClient();

    @Resource
    private TenantService tenantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MtPaymentLogService mtPaymentLogService;
    @Resource
    private ApolloEntity apolloEntity;


    @Override
    public boolean isMyPlatfrom(String platfrom) {
        return !Boolean.TRUE.equals(apolloEntity.getMeituanTuangouSwitch()) && TuangouPlatfromEnum.MTTG.getPlatfromCode().equals(platfrom);
    }

    @Override
    public String certificatePrepare (CertificatePrepareReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        String appKey = token.getAppKey();
        String appSecret = token.getAppSecret();
        String session = token.getAccessToken();
        String openShopUuid = token.getOpenShopUuid();

        CertificatePrepareData certificatePrepare = new CertificatePrepareData();
        certificatePrepare.setVerifyToken(UUID.randomUUID().toString());
        if (StringUtils.isNotBlank(req.getEncryptedData())) {
            TuangouReceiptScanPrepareRequest request = new TuangouReceiptScanPrepareRequest(appKey, appSecret, session, req.getEncryptedData(), null, openShopUuid);
            TuangouReceiptScanPrepare tuangouReceiptScanPrepare = new TuangouReceiptScanPrepare(request);
            TuangouReceiptScanPrepareReponse receiptScanPrepareReponse = OPEN_API_CLIENT.invoke(tuangouReceiptScanPrepare);
            Integer errorCode = Objects.equals(SUCCESS_CODE, receiptScanPrepareReponse.getCode()) ? 0 : receiptScanPrepareReponse.getCode();
            if (!SUCCESS_CODE.equals(receiptScanPrepareReponse.getCode())) {
                certificatePrepare.setErrorCode(errorCode);
                certificatePrepare.setDescription(receiptScanPrepareReponse.getMsg());
                return  JsonUtils.toJson(certificatePrepare);
            }
            List<TuangouReceiptScanPrepareReponseEntity> data = receiptScanPrepareReponse.getData();

            List<CertificateData> certificateDataList = new ArrayList<>();
            for (TuangouReceiptScanPrepareReponseEntity tuangouReceiptScanPrepareReponseEntity : data) {
                TuangouReceiptPrepareRequest prepareRequest = new TuangouReceiptPrepareRequest(appKey, appSecret, session, tuangouReceiptScanPrepareReponseEntity.getReceipt_code(), null, openShopUuid);
                TuangouReceiptPrepare tuangouReceiptPrepare = new TuangouReceiptPrepare(prepareRequest);
                TuangouReceiptPrepareResponse receiptPrepareResponse = OPEN_API_CLIENT.invoke(tuangouReceiptPrepare);
                CertificateData certificateData = toCertificateData(receiptPrepareResponse);
                if (Objects.nonNull(certificateData)) {
                    certificateDataList.add(certificateData);
                }
                certificatePrepare.setDescription(receiptPrepareResponse.getMsg());
            }
            certificatePrepare.setErrorCode(errorCode);
            certificatePrepare.setCertificates(certificateDataList);
        }

        TuangouReceiptPrepareRequest request = new TuangouReceiptPrepareRequest(appKey, appSecret, session, req.getCode(), null, openShopUuid);
        TuangouReceiptPrepare tuangouReceiptPrepare = new TuangouReceiptPrepare(request);
        TuangouReceiptPrepareResponse receiptPrepareResponse = OPEN_API_CLIENT.invoke(tuangouReceiptPrepare);

        log.info("调用美团团购券验券校验接口,request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(receiptPrepareResponse));
        certificatePrepare.setErrorCode(receiptPrepareResponse.getCode());
        certificatePrepare.setDescription(receiptPrepareResponse.getMsg());
        if (SUCCESS_CODE.equals(receiptPrepareResponse.getCode())) {
            certificatePrepare.setErrorCode(0);
            CertificateData certificateData = toCertificateData(receiptPrepareResponse);
            certificatePrepare.setCertificates(Collections.singletonList(certificateData));
        }
        TuangouDataRes<CertificatePrepareData> res = new TuangouDataRes<>();
        res.setData(certificatePrepare);
        return JsonUtils.toJson(res);
    }

    private CertificateData toCertificateData(TuangouReceiptPrepareResponse receiptPrepareResponse) {
        CertificateData certificateData = new CertificateData();
        TuangouReceiptPrepareResponseEntity data = receiptPrepareResponse.getData();
        if (Objects.isNull(data)) {
            return null;
        }
        certificateData.setCount(data.getCount());
        certificateData.setExpireTime(data.getReceiptEndDate().getTime());
        certificateData.setEncryptedCode(data.getReceipt_code());
        /**
         * 类型说明：
         * 2：抵用券
         * 5：积分
         * 6：立减
         * 8：商户抵用券
         * 10：C端美团支付
         * 12：优惠代码
         * 15：美团立减
         * 17：商家立减
         * 18：美团商家立减
         * 21：次卡
         * 22：打折卡
         * 23：B端美团支付
         * 24：全渠道会员券
         * 25：pos支付
         * 26：线下认款平台
         * 28：商家折上折
         * 29：美团分销支付
         */
        List<TuangouReceiptPreparePaymentDetail> paymentDetail = data.getPayment_detail();
        //8，17，18，22，24，28表示商家优惠；10，23，25，26，29表示用户支付；其余为平台优惠
        List<Long> merchantType = Arrays.asList(8L, 17L, 18L, 22L, 24L, 28L);
        List<Long> payType = Arrays.asList(10L,23L,25L,26L,29L);
        /*Long originalAmount = paymentDetail.stream()
                .map(v -> Convert.toLong(v.getAmount().subtract(new BigDecimal(NumberConstant.ONE_HUNDRED)).longValue()))
                .reduce(Long::sum).orElse(0L);*/
        Long merchantTicketAmount = paymentDetail.stream().filter(v -> merchantType.contains(v.getAmount_type()))
                .map(v -> Convert.toLong(v.getAmount().multiply(new BigDecimal(NumberConstant.ONE_HUNDRED)).longValue()))
                .reduce(Long::sum).orElse(0L);
        Long payAmount = BigDecimal.valueOf(ObjectUtil.defaultIfNull(data.getDeal_price(), 0D))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED)).longValue();
        CertificateAmount certificateAmount = new CertificateAmount();
        certificateAmount.setPayAmount(payAmount);
        certificateAmount.setMerchantTicketAmount(merchantTicketAmount);
        certificateAmount.setCouponPayAmount(payAmount);
        certificateData.setAmount(certificateAmount);

        CertificateSku certificateSku = new CertificateSku();
        certificateSku.setSkuId(Convert.toStr(data.getDealgroup_id()));
        certificateSku.setAccountId(data.getMobile());
        certificateSku.setTitle(data.getDeal_title());
        certificateSku.setMarketPrice(Convert.toLong(data.getDeal_marketprice() * NumberConstant.ONE_HUNDRED));
        certificateData.setSku(certificateSku);
        return certificateData;
    }

    /**
     * 保存支付明细
     * @param receiptConsumeResponse
     */
    @Override
    public void savePayment (TuangouReceiptConsumeResponse receiptConsumeResponse) {
        try {
            List<TuangouReceiptConsumeResponseEntity> data = receiptConsumeResponse.getData();
            //只有九机触发
            if(CollectionUtils.isNotEmpty(data) && XtenantEnum.isJiujiXtenant()){
                List<MtPaymentLog> MtPaymentLogList = new ArrayList<>();
                for (TuangouReceiptConsumeResponseEntity datum : data) {
                    List<TuangouReceiptPreparePaymentDetail> paymentDetail = datum.getPayment_detail();
                    if(CollectionUtils.isNotEmpty(paymentDetail)){
                        paymentDetail.forEach(v -> {
                            MtPaymentLog mtPaymentLog = new MtPaymentLog();
                            mtPaymentLog.setOrderId(datum.getOrder_id());
                            mtPaymentLog.setPaymentDetailId(v.getPayment_detail_id());
                            mtPaymentLog.setAmount(v.getAmount());
                            mtPaymentLog.setReceiptCode(datum.getReceipt_code());
                            mtPaymentLog.setAmountType(v.getAmount_type());
                            MtPaymentLogList.add(mtPaymentLog);
                        });
                    }
                }
                mtPaymentLogService.saveBatchByOrderId(MtPaymentLogList);
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("美团支付订单记录明细", receiptConsumeResponse, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    @Override
    public String certificateVerify (CertificateVerifyReq req) {
        if (CollectionUtils.isEmpty(req.getEncryptedCodes())) {
            throw new CustomizeException("核销券码不能为空");
        }
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        String appKey = token.getAppKey();
        String appSecret = token.getAppSecret();
        String session = token.getAccessToken();
        String openShopUuid = token.getOpenShopUuid();

        TuangouReceiptConsumeRequest request = new TuangouReceiptConsumeRequest(appKey,
                appSecret, session, req.getVerifyToken(), req.getEncryptedCodes().get(0),
                Optional.ofNullable(req.getCount()).orElse(1), null,
                req.getAppShopAccount(), req.getAppShopAccountname(), openShopUuid);
        TuangouReceiptConsume tuangouReceiptConsume = new TuangouReceiptConsume(request);
        TuangouReceiptConsumeResponse receiptConsumeResponse = OPEN_API_CLIENT.invoke(tuangouReceiptConsume);

        log.info("调用美团团购券核销接口,request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(receiptConsumeResponse));
        //记录支付记录
        savePayment(receiptConsumeResponse);
        CertificateVerifyData certificateVerifyData = new CertificateVerifyData();
        certificateVerifyData.setErrorCode(receiptConsumeResponse.getCode());
        certificateVerifyData.setDescription(receiptConsumeResponse.getMsg());
        if (SUCCESS_CODE.equals(receiptConsumeResponse.getCode()) && CollectionUtils.isNotEmpty(receiptConsumeResponse.getData())) {
            certificateVerifyData.setErrorCode(0);
            List<CertificateVerifyResultData> verifyResults = receiptConsumeResponse.getData().stream()
                    .map(v -> LambdaBuild.create(CertificateVerifyResultData.class)
                            .set(CertificateVerifyResultData::setResult, 0)
                            .set(CertificateVerifyResultData::setOrderId, v.getOrder_id())
                            .set(CertificateVerifyResultData::setOriginCode, v.getReceipt_code())
                            .set(CertificateVerifyResultData::setCertificateId, v.getReceipt_code())
                            .set(CertificateVerifyResultData::setVerifyId, Convert.toStr(v.getDeal_id()))
                            .build()).collect(Collectors.toList());
            certificateVerifyData.setVerifyResults(verifyResults);
        }
        TuangouDataRes<CertificateVerifyData> res = new TuangouDataRes<>();
        res.setData(certificateVerifyData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateCancel (CertificateCancelReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        String appKey = token.getAppKey();
        String appSecret = token.getAppSecret();
        String session = token.getAccessToken();
        String openShopUuid = token.getOpenShopUuid();
        TuangouReceiptReverseConsumeRequest request = new TuangouReceiptReverseConsumeRequest(appKey,appSecret, session,
                req.getVerifyId(), req.getCertificateId(), null, req.getAppShopAccount(), req.getAppShopAccountname(), openShopUuid);
        TuangouReceiptReverseConsume tuangouReceiptReverseConsume = new TuangouReceiptReverseConsume(request);
        TuangouReceiptReverseConsumeResponse receiptReverseConsumeResponse = OPEN_API_CLIENT.invoke(tuangouReceiptReverseConsume);
        log.info("调用美团团购撤销验券接口,request={}，response={}", request.toString(), JsonUtils.toJson(receiptReverseConsumeResponse));
        CertificateCancelData certificateCancelData = new CertificateCancelData();
        certificateCancelData.setErrorCode(receiptReverseConsumeResponse.getCode());
        if (SUCCESS_CODE.equals(receiptReverseConsumeResponse.getCode()) && CollectionUtils.isNotEmpty(receiptReverseConsumeResponse.getData())) {
            Integer errorCode = Objects.equals(SUCCESS_CODE, receiptReverseConsumeResponse.getCode()) ? 0 : receiptReverseConsumeResponse.getCode();
            certificateCancelData.setErrorCode(errorCode);
        }
        certificateCancelData.setDescription(receiptReverseConsumeResponse.getMsg());
        TuangouDataRes<CertificateCancelData> res = new TuangouDataRes<>();
        res.setData(certificateCancelData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateGet (CertificateGetReq req) {
        return null;
    }

    @Override
    public String certificateQuery (CertificateQueryReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        String appKey = token.getAppKey();
        String appSecret = token.getAppSecret();
        String session = token.getAccessToken();

        OrderQueryInfoRequest request = new OrderQueryInfoRequest(appKey,appSecret, session,
                null, req.getOrderId(), Arrays.asList(1,2));
        OrderQueryInfo orderQueryInfo = new OrderQueryInfo(request);
        OrderQueryInfoResponse queryInfoResponse = OPEN_API_CLIENT.invoke(orderQueryInfo);
        log.info("调用美团团购订单及券码状态查询接口,request={}，response={}", request.toString(), JsonUtils.toJson(queryInfoResponse));
        CertificateQueryData certificateQueryData = new CertificateQueryData();
        //只可撤销当天核销且未超过10分钟的团购券
        certificateQueryData.setCancelLimitTime(NumberConstant.TEN);
        certificateQueryData.setErrorCode(queryInfoResponse.getCode());
        certificateQueryData.setDescription(queryInfoResponse.getMsg());
        if (Objects.nonNull(queryInfoResponse.getData())) {
            certificateQueryData.setErrorCode(0);
            List<CertificateInfoData> certificates = queryInfoResponse.getData().getReceiptList().stream().map(v -> LambdaBuild.create(CertificateInfoData.class)
                    .set(CertificateInfoData::setCode, v.getReceiptCode())
                    .set(CertificateInfoData::setEncryptedCode, v.getReceiptCode())
                    .set(CertificateInfoData::setStatus, toTuangouStatus(v.getStatus()))
                    .build()).collect(Collectors.toList());
            certificateQueryData.setCertificates(certificates);
        }
        TuangouDataRes<CertificateQueryData> res = new TuangouDataRes<>();
        res.setData(certificateQueryData);
        return JsonUtils.toJson(res);
    }

    private Integer toTuangouStatus(Integer status) {
        Integer res = status;
        switch (status) {
            case 3:
                res = 7;
                break;
            case 4:
            case 6:
                res = 4;
                break;
            default:
                break;
        }
        return res;
    }

    /**
     * 授权后查询缓存token
     * @param appKey
     * @param authCode
     * @param redirectUrl
     * @return
     */
    @Override
    public String getToken (String appKey, String authCode, String redirectUrl) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        DynamicTokenRequest request = new DynamicTokenRequest(tenant.getAppKey(),
                tenant.getAppSecret(),
                GrantType.AUTHORIZATION_CODE.getValue(),
                authCode,
                redirectUrl);
        DynamicToken dynamicToken = new DynamicToken(request);
        DynamicTokenResponse response = OPEN_API_CLIENT.invoke(dynamicToken);

        MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
        tuangouToken.setAppKey(tenant.getAppKey());
        tuangouToken.setAppSecret(tenant.getAppSecret());

        tuangouToken.setAccessToken(response.getAccess_token());
        tuangouToken.setRefreshToken(response.getRefresh_token());
        tuangouToken.setBid(response.getBid());
        tuangouToken.setExpiresIn(response.getExpires_in());
        tuangouToken.setScope(response.getScope());
        tuangouToken.setRemainRefreshCount(response.getRemain_refresh_count());
        tuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(tuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));

        String tokenKey = RedisKeyConstant.MEITUAN_DIANPIN_TOKEN + appKey;
        stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(tuangouToken), NumberConstant.ONE_HUNDRED, TimeUnit.DAYS);

        return response.getAccess_token();
    }

    /**
     * 从缓存查询token
     * @return
     */
    public MeituanTuangouToken getTokenByCache (Integer areaId) {
        TenantStoreDto tenantStore = tenantService.getTenantStoreByPlatCodeAndAreaId(PlatfromEnum.MTTG.name(), areaId);
        if (Objects.isNull(tenantStore)) {
            throw new CustomizeException(StrUtil.format("商户信息或门店{}配置不存在或未启用", areaId));
        }
        String appKey = tenantStore.getAppKey();

        String tokenKey = RedisKeyConstant.MEITUAN_DIANPIN_TOKEN + appKey;
        String tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
        if (StringUtils.isBlank(tokenStr)) {
            throw new CustomizeException("美团点评授权已过期，请到美团团购配置页面重新授权！");
        }
        MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
        if (Objects.nonNull(meituanTuangouToken.getExpiresTime()) && LocalDateTime.now().isAfter(meituanTuangouToken.getExpiresTime())) {
            meituanTuangouToken = refreshToken(meituanTuangouToken);
            //设置过期时间
            meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
            stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.ONE_HUNDRED, TimeUnit.DAYS);
        }
        String openShopUuid = getOpenShopUuid(meituanTuangouToken, tenantStore);
        meituanTuangouToken.setOpenShopUuid(openShopUuid);
        return meituanTuangouToken;
    }

    private String getOpenShopUuid(MeituanTuangouToken meituanTuangouToken,TenantStoreDto tenantStore) {
        String storeCode = tenantStore.getStoreCode();
        if (StringUtils.isBlank(storeCode)) {
            throw new CustomizeException("门店"+tenantStore.getArea()+"未绑定美团点评门店");
        }
        String tokenKey = RedisKeyConstant.MEITUAN_DIANPIN_OPENSHOPUUID + meituanTuangouToken.getAppKey();
        Object obj = stringRedisTemplate.opsForHash().get(tokenKey, tenantStore.getStoreCode());
        if (obj != null) {
            return Convert.toStr(obj);
        }
        CustomerShopIdMappingRequest request = new CustomerShopIdMappingRequest(meituanTuangouToken.getAppKey(),
                meituanTuangouToken.getAppSecret(),
                meituanTuangouToken.getAccessToken(), meituanTuangouToken.getBid(), storeCode);
        CustomerShopIdMappingQuery customerShopIdMappingQuery = new CustomerShopIdMappingQuery(request);
        CustomerShopIdMappingResponse customerShopIdMappingResponse = OPEN_API_CLIENT.invoke(customerShopIdMappingQuery);
        if (!SUCCESS_CODE.equals(customerShopIdMappingResponse.getCode()) || CollectionUtils.isEmpty(customerShopIdMappingResponse.getData())) {
            throw new CustomizeException("获取美团openshopuuid失败：" + customerShopIdMappingResponse.getMsg());
        }
        for (CustomerShopIdMappingResponseEntity customerShopIdMappingResponseEntity : customerShopIdMappingResponse.getData()) {
            if (storeCode.equals(customerShopIdMappingResponseEntity.getShop_id())) {
                String openShopUuid = customerShopIdMappingResponseEntity.getOpen_shop_uuid();
                if (StringUtils.isBlank(openShopUuid)) {
                    throw new CustomizeException("获取美团openshopuuid失败：" + customerShopIdMappingResponseEntity.getErr_msg());
                }
                stringRedisTemplate.opsForHash().put(tokenKey, storeCode, openShopUuid);
                return openShopUuid;
            }
        }
        return "";
    }

    /**
     * 刷新token
     * @param meituanTuangouToken
     * @return
     */
    public MeituanTuangouToken refreshToken (MeituanTuangouToken meituanTuangouToken) {
        RefreshTokenRequest request = new RefreshTokenRequest(meituanTuangouToken.getAppKey(), meituanTuangouToken.getAppSecret(), meituanTuangouToken.getRefreshToken());
        CustomerRefreshToken refreshToken = new CustomerRefreshToken(request);
        CustomerRefreshTokenResponse response = OPEN_API_CLIENT.invoke(refreshToken);
        if (SUCCESS_CODE.equals(response.getCode())) {
            MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
            tuangouToken.setAppKey(meituanTuangouToken.getAppKey());
            tuangouToken.setAppSecret(meituanTuangouToken.getAppSecret());

            tuangouToken.setAccessToken(response.getAccess_token());
            tuangouToken.setRefreshToken(response.getRefresh_token());
            tuangouToken.setBid(response.getBid());
            tuangouToken.setExpiresIn(response.getExpires_in());
            tuangouToken.setScope(response.getScope());
            tuangouToken.setRemainRefreshCount(response.getRemain_refresh_count());
            return tuangouToken;
        } else {
            throw new CustomizeException("appkey:"+meituanTuangouToken.getAppKey()+"刷新美团点评token失败，原因" + response.getMsg());
        }
    }
}
