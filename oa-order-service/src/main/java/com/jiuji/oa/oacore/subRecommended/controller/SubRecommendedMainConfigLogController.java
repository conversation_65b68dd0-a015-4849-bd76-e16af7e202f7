package com.jiuji.oa.oacore.subRecommended.controller;

import com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedMainConfigLog;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedMainConfigLogService;
import com.jiuji.oa.oacore.subRecommended.vo.req.SaveLogReq;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/SubRecommendedMainConfigLog")
public class SubRecommendedMainConfigLogController {

    @Resource
    private SubRecommendedMainConfigLogService logService;


    @PostMapping("/saveLog/v1")
    public R<String> saveLog(@RequestBody @Valid SaveLogReq req) {
        logService.saveLog(req);
        return R.success("保存成功");
    }


    /**
     * 根据id进行日志查询
     * @param id
     * @return
     */
    @GetMapping("/getLogById/v1")
    public R<List<SubRecommendedMainConfigLog>> getLogById(@RequestParam("id")  Integer id) {
        return R.success(logService.getLogById(id));
    }
}
