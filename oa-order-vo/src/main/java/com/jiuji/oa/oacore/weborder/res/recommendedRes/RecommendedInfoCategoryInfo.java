package com.jiuji.oa.oacore.weborder.res.recommendedRes;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain =true)
public class RecommendedInfoCategoryInfo {

    /**
     * 自增长
     */
    private Integer id;

    private String uuid;

    /**
     * 关联套餐(sub_recommended_package_config)id
     */
    private Integer fkPackageId;


    /**
     * RecommendedInfoPackageInfo的uuid
     */
    private String fkPackageUuid;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer seqNum;


    private LocalDateTime createTime;

    /**
     * 前端所需要的数据
     */
    private List<RecommendedInfoProductInfo> productInfoList;

    /**
     * oa和app所需要的数据
     */
    private List<ProductAggregation> productAggregationList;

}
