package com.jiuji.oa.oacore.tousu.vo.res;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.oa.oacore.common.util.conver.LocalDateTimeConverter;
import com.jiuji.oa.oacore.common.util.conver.TagEnumConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TouSuModelExportJiuji{

    @ApiModelProperty("投诉ID")
    @ExcelProperty(index = 0, value = "投诉ID")
    private Integer tsId;

    @ApiModelProperty(value = "反馈类型：1投诉，2表扬,3建议")
    @ExcelProperty(index = 1, value = "投诉性质",converter = TagEnumConverter.class)
    private Integer tag;

    @ApiModelProperty("会员Id")
    @ExcelProperty(index = 2, value = "会员Id")
    private Integer userId = 0;

    @ApiModelProperty(value = "会员投诉次数")
    @ExcelIgnore
    private Integer tcount = 0;

    @ApiModelProperty(value = "投诉主体")
    @ExcelIgnore
    private Integer xtenant;

    @ApiModelProperty("会员等级")
    @ExcelIgnore
    private Integer userClass = -1;

    @ApiModelProperty("会员等级名称")
    @ExcelProperty(index = 3, value = "会员等级")
    private String userClassName;

    @ApiModelProperty(value = "投诉时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(index = 4, value = "投诉时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime addTime;

    @ApiModelProperty(value = "处理超时")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private LocalDateTime dealTimeout;

    @ApiModelProperty(value = "是否处理超时处理超时")
    @ExcelIgnore
    private Boolean timeOut;

    @ApiModelProperty(value = "投诉内容")
    @ExcelProperty(index = 5, value = "投诉内容")
    private String content;

    /**
     * 投诉来源 1PC、2OA录入、3M版、4小程序、7APP、6门店二维码
     */
    @ExcelProperty(index = 6, value = "投诉来源")
    private String tsTypeStr;

    @JSONField(serialize = false)
    @ExcelIgnore
    private Integer tsType;

    @ApiModelProperty(value = "责任门店、门店")
    @ExcelIgnore
    private List<TouSuDepartRes> zenRenAreasAndDeparts;

    @JsonIgnore
    @ExcelProperty(index = 7, value = "责任归属门店")
    private String dutyArea;

    @JsonIgnore
    @ExcelProperty(index = 8, value = "责任归属区域")
    private String dutyDepart;

    @ApiModelProperty(value = "定性分类 1、有效投诉 2、无效投诉 3、占额投诉")
    @ExcelProperty(index = 9, value = "定性分类")
    private String catName;

    @ExcelProperty(index = 10,value = "扣分")
    private BigDecimal score = BigDecimal.ZERO;

    @ExcelProperty(index = 11,value = "投诉原因")
    private String tousuReason;

    @ExcelProperty(index = 12, value = "处理状态")
    private String statusName;

    @ApiModelProperty(value = "处理进程")
    @ExcelIgnore
    private Integer states;

    @ApiModelProperty(value = "奖励")
    @ExcelIgnore
    private BigDecimal bonusMoney = new BigDecimal(0);

    @ApiModelProperty(value = "罚款")
    @ExcelIgnore
    private BigDecimal fineMoney = new BigDecimal(0);

    @ApiModelProperty(value = "门店id")
    @ExcelIgnore
    private Integer areaId ;

    @ApiModelProperty(value = "门店编码")
    @ExcelIgnore
    private String area ;

    @ExcelProperty(index = 13,value = "跟进人")
    @JSONField(serialize = false)
    private String processUser;

    @JSONField(serialize = false)
    @ExcelIgnore
    private String dealUserIds;

    @ExcelProperty(index = 14,value = "处理人")
    @JSONField(serialize = false)
    private String dealUsers;

    @ApiModelProperty(value = "完成时间")
    @ExcelIgnore
    private LocalDateTime finishTime;
    @ExcelProperty(index = 15, value = "完成时间")
    private String finishTime2;

    @ExcelProperty(index = 16,value = "投诉责任人")
    @JSONField(serialize = false)
    private String dutyUser;

    /**
     * 补偿发放
     */
    @ExcelProperty(index = 17,value = "补偿发放")
    private String rewards;
}
