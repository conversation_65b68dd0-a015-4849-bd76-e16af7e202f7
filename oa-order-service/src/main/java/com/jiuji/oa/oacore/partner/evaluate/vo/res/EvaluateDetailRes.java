package com.jiuji.oa.oacore.partner.evaluate.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021/7/20
 */
@Data
@ApiModel(value = "服务评价详情返回实体")
public class EvaluateDetailRes {

    @ApiModelProperty(value = "评价id")
    private String id;

    @ApiModelProperty(value = "评价时间")
    private String dtime;

    @ApiModelProperty(value = "反馈人姓名")
    private String employeeName;

    @ApiModelProperty(value = "合作伙伴名称")
    private String xtenantName;

    @ApiModelProperty(value = "反馈人联系电话")
    private String employeeMobile;

    @ApiModelProperty(value = "评价状态码")
    private Integer evaluateStatusCode;

    @ApiModelProperty(value = "评价状态")
    private String evaluateStatus;

    @ApiModelProperty(value = "运营经理姓名")
    private String relateCh999Name;

    @ApiModelProperty(value = "运营经理工号")
    private String ch999Id;

    @ApiModelProperty(value = "运营经理职务")
    private String zhiwu;

    @ApiModelProperty(value = "运营经理职级")
    private String zhiji;

    @ApiModelProperty(value = "运营经理职级id")
    private Integer zhijiId;

    @ApiModelProperty(value = "运营经理职级Szhiji_id")
    private Integer szhijiId;

    @ApiModelProperty(value = "运营经理头像")
    private String relateCh999PicUri;

    @JSONField(serialize = false)
    private String relateCh999Pic;

    @ApiModelProperty(value = "服务分")
    private String score;

    @ApiModelProperty(value = "专业分")
    private String score2;

    @ApiModelProperty(value = "响应速度")
    private String speedScore;

    @ApiModelProperty(value = "改进建议")
    private String improveSuggest;

    @ApiModelProperty(value = "支撑建议")
    private String supportSuggest;

    @ApiModelProperty(value = "跟进人")
    private String processUser;

    private String process;

    /**
     * 处理进程
     */
    private List<ProcessLog> processLogs;

    /**
     * xtenant
     */
    private Integer xtenant;

    /**
     * 数据来源（null = 九机，1=九讯云，2=九讯云Neo）
     */
    private Integer source;

    @Data
    public static class ProcessLog implements Serializable {
        private static final long serialVersionUID = 3675496603242671394L;
        private String logTime;
        private String inuser;
        private String content;
    }
}
