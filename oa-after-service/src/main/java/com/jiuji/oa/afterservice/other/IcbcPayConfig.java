package com.jiuji.oa.afterservice.other;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Transient;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@TableName("IcbcPayConfig")
public class IcbcPayConfig {

    private Long id;

    @TableField("mch_no")
    private String mchNo;
    @TableField("xtenant")
    private Integer xtenant;

    @TableField("auth_id")
    private Integer authId;
    @TableField("areaids")
    private String areaids;

    @Transient
    @TableField(exist = false)
    private List<Integer> areaIdArray;
    @TableField("public_key")
    private String publicKey;
    @TableField("mch_private_key")
    private String mchPrivateKey;
    @TableField("channel_config_id")
    private String channelConfigId;
    @TableField("pay_name")
    private String payName;
    @TableField("nick_name")
    private String nickName;
    @TableField("subject")
    private String subject;
    @TableField("is_del")
    private Boolean isDel;

    public List<Integer> getAreaIdArray() {
        return areaids == null ? null : Arrays.asList(areaids.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
    }

}
