<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.label.dao.ProductLabelPushMsgMapper">

    <select id="listLteExpire" resultType="com.jiuji.oa.oacore.label.bo.ProductNotificationBo">
        select
        p.id productId,
        p.name productName,
        ph.label productLabel,
        p.cid
        from
        dbo.product p with(nolock)
        left join product_label_his ph with(nolock) on p.id = ph.product_id and ph.sold is null
        where (p.ismobile = 0 or p.cid = 597) and p.display = 1
        and ph.xTenant = 0
        AND ph.end_time &lt;= DATEADD(DAY, #{days}, CONVERT(DATE, GETDATE()))
        <foreach collection="cids" open="and p.cid in (" close=")" separator="," item="cid">
            #{cid}
        </foreach>
    </select>
</mapper>
