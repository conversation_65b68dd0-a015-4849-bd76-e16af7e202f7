package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.other.bo.OtherShouYinBo;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/4/30
 */
@ApiModel
@Data
public class ShouhouTuihuanRes {

    private ShouhouRes shouhou;
    @ApiModelProperty(value = "非库分期金额")
    private BigDecimal nkuBaiTiaoPrice;
    @ApiModelProperty(value = "从订单里来的退货金额")
    private BigDecimal buyPriceM;
    @ApiModelProperty(value = "维修单号")
    private Integer shouhouId;
    @ApiModelProperty(value = "当前所在地区")
    private String currArea;
    @ApiModelProperty(value = "授权隔离id")
    private Integer authorizeId;
    @ApiModelProperty(value = "权限")
    private List<String> myRank;
    @ApiModelProperty(value = "从订单里来的退货金额（九机币）")
    private BigDecimal coinM;
    @ApiModelProperty(value = "赠品金额")
    private BigDecimal giftPrice;
    @ApiModelProperty(value = "白条金额")
    private BigDecimal baitiaoPrice;
    @ApiModelProperty(value = "库分期金额")
    private BigDecimal kuBaiTiaoPrice;
    @ApiModelProperty(value = "ovg 返现金额")
    private BigDecimal ovgSaveMoney;
    @ApiModelProperty(value = "当前用户余额")
    private BigDecimal userSaveMoney;
    @ApiModelProperty(value = "交易日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeDate;
    @ApiModelProperty(value = "交易天数")
    private Integer tradeDay;
    @ApiModelProperty(value = "交易类别")
    private Integer tradeType;
    @ApiModelProperty(value = "退换配置")
    private List<TuihuanConfigRes> tuihunConfig;

    @ApiModelProperty(value = "选项列表（前端使用）")
    private List<String> faultOptionList;

    @ApiModelProperty(value = "商品附件")
    private String peizhi;
    @ApiModelProperty(value = "发票类别")
    private String piaoType;

    @ApiModelProperty(value = "发票类别名称")
    private String piaoTypeText;

    @ApiModelProperty(value = "中移金服余额")
    private BigDecimal zhongyiMoney;
    @ApiModelProperty(value = "微信红包金额")
    private BigDecimal wxhongbao;
    @ApiModelProperty(value = "捷信0利息")
    private OtherShouYinBo jiexin;
    @ApiModelProperty(value = "普惠分期")
    private OtherShouYinBo puhui;
    @ApiModelProperty(value = "是否绑定微信")
    private Boolean isMemberBindingWeixin;
    @ApiModelProperty(value = "微信qrCode")
    private String bindingWeixinQrCode;
    private MemberWXAuthRes wxAuthInfo;
    @ApiModelProperty(value = "退换信息")
    private ShouhouTuihuan huan;


    @Data
    public static class ShouhouRes extends Shouhou{
        private Integer ck_area;
        private Integer d1;
    }

    @ApiModelProperty(value = "是否允许发送短信")
    private Boolean smsSendFlag;













}
