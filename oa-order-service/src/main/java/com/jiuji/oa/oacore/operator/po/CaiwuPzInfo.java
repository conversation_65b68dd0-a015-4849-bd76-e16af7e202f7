package com.jiuji.oa.oacore.operator.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 财务凭证业务中间表[责任小组:财务]
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CaiwuPzInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 账套
     */
    private Integer accountSetId;

    /**
     * 账套名字
     */
    private String accountSetName;

    /**
     * 科目编码(对应账套)
     */
    private String accountCode;

    /**
     * 科目编码名字(对应账套)
     */
    private String accountName;

    /**
     * 核算编码
     */
    private Integer auxiliary;

    /**
     * 部门辅助核算
     */
    private Integer deptId;

    /**
     * 门店辅助核算
     */
    private Integer areaId;

    /**
     * 个人辅助核算(一般是操作者的工号)
     */
    private Integer operatorId;

    /**
     * 项目辅助大类编码
     */
    private String projectRootCode;

    /**
     * 项目辅助次类编码
     */
    private String projectMinorCode;

    /**
     * 项目辅助次类名称
     */
    private String projectFzhsName;

    /**
     * 往来单位大类编码
     */
    private String partnerRootCode;

    /**
     * 往来单位次级编码
     */
    private String partnerMinorCode;

    /**
     * 往来单位次级名称
     */
    private String partnerName;

    /**
     * 客户辅助大类编码
     */
    private String customerRootCode;

    /**
     * 客户辅助小类编码
     */
    private String customerMinorCode;

    /**
     * 客户辅助小类名称
     */
    private String custName;

    /**
     * 逻辑删除字段(0未删除,1已删除)
     */
    private Integer isDel;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据最后修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Integer xtenant;


}
