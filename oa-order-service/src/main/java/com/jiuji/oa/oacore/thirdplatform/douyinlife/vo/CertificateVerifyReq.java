package com.jiuji.oa.oacore.thirdplatform.douyinlife.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.beans.Transient;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:46
 */
@Data
public class CertificateVerifyReq {
    private Integer areaId;

    @JSONField(name = "verify_token")
    @JsonProperty("verify_token")
    private String verifyToken;
    @JSONField(name = "poi_id")
    @JsonProperty("poi_id")
    private String poiId;
    @JSONField(name = "encrypted_codes")
    @JsonProperty("encrypted_codes")
    private List<String> encryptedCodes;
    private List<String> codes;
    @JSONField(name = "order_id")
    @JsonProperty("order_id")
    private String orderId;
    @JSONField(name = "code_with_time_list")
    @JsonProperty("code_with_time_list")
    private List<ThirdCode> codeWithTimeList;
    @JSONField(name = "voucher")
    @JsonProperty("voucher")
    private List<VoucherCode> voucher;
    @JSONField(name = "verify_extra")
    @JsonProperty("verify_extra")
    private List<VerifyExtra> verifyExtra;

    @Data
    static class ThirdCode {
        private String code;
        @JSONField(name = "verify_time")
        @JsonProperty("verify_time")
        private Long verifyTime;
        @JSONField(name = "verify_sign_list")
        @JsonProperty("verify_sign_list")
        private List<String> verifySignList;
    }

    @Data
    static class VoucherCode {
        @JSONField(name = "project_id")
        @JsonProperty("project_id")
        private String projectId;
        @JSONField(name = "id_card_list")
        @JsonProperty("id_card_list")
        private List<String> idCardList;
        @JSONField(name = "qrcode_list")
        @JsonProperty("qrcode_list")
        private List<String> qrcodeList;
        @JSONField(name = "certificate_no_list")
        @JsonProperty("certificate_no_list")
        private List<String> certificateNoList;
        @JSONField(name = "verify_time")
        @JsonProperty("verify_time")
        private List<String> verifyTime;
    }

    @Data
    static class VerifyExtra {
        @JSONField(name = "dynamic_coupon_info")
        @JsonProperty("dynamic_coupon_info")
        private DynamicCouponInfo dynamicCouponInfo;
    }

    @Data
    static class DynamicCouponInfo {
        @JSONField(name = "biz_time")
        @JsonProperty("biz_time")
        private Long bizTime;
        @JSONField(name = "actual_deduction_amount")
        @JsonProperty("actual_deduction_amount")
        private Long actualDeductionAmount;
    }
}
