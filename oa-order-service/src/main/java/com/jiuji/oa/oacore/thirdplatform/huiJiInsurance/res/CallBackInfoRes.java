package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CallBackInfoRes {

    private String action;
    private BigDecimal amount;
    private String orderNo;
    private String outerId;
    private String brandName;
    private String modelName;
    private String productionNo;
    private String sign;
}
