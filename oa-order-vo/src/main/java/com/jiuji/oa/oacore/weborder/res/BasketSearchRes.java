package com.jiuji.oa.oacore.weborder.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/11/12/14:43
 * @Description:
 */
@Data
public class BasketSearchRes implements Serializable {
    private static final long serialVersionUID = -495518632827360844L;


    private long subId;

    private int basketId;

    private int ppriceid;

    private int giftid;

    private BigDecimal price;

    private BigDecimal price1;

    private int basketCount;

    private String productName;

    private String productColor;

    private String productImg;

    private String basket_date;

    //套餐相关
    private String PlanId;

    private String type;

    private String Mobile;

    private String PackageType; //购机类型 1 购机送费 2 单卡 3 零元购

    private String isptype; //1 联通套餐 2 电信套餐

    private String ContractPeroid; //合约期 24个月、36个月

    private List<MyOrderNewRes.SubMkcInfo> mkcInfo;

    /// <summary>
    /// 靓号
    /// </summary>
    private String haoma;
    private boolean ismobile;
    /// <summary>
    /// 配置
    /// </summary>
    private String peizhi;

    /// <summary>
    /// 串号
    /// </summary>
    private String imei;

    /// <summary>
    /// 是否支持复购
    /// </summary>
    private boolean isHalfBuy;
    /// <summary>
    /// 剩余可复购次数
    /// </summary>
    private int halfBuyCount;


}
