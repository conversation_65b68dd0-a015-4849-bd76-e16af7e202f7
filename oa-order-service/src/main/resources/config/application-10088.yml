consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://weedfs.xn.saas.ch999.cn:5083
  select:
    url: https://img.sxzrtx.com/
  upload:
    url: http://weedfs.xn.saas.ch999.cn:9333
instance-zone: 10088
jiuji:
  sys:
    moa: https://moa.sxzrtx.com
    pc: https://oa.sxzrtx.com
    web: https://www.sxzrtx.com
    inWcf: https://inwcf.sxzrtx.com
    oaWcf: https://inwcf2.sxzrtx.com
    xtenant: 10088
  xtenant: 88000
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10088:9fhhCsCugShs@***********:27017,***********:27017,***********:27017/ch999oa__10088
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10088
    password: fbZMMIjEolvV
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10088
  oa_nc:
    dbname: oa_nc__10088
    password: pijd#I4Gipq8
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10088
  oa_log:
    dbname: oa_log__10088
    password: 'nCqnZ8DAd9y1'
    url: tidb.serv.hb.saas.ch999.cn:9383
    username: oa_log__10088
office:
  sys:
    xtenant: 10088
rabbitmq:
  master:
    password: emLBF
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10088
    vhost: oaAsync__10088
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: IKUIu
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10088
    vhost: oa__10088
  oaAsync:
    password: emLBF
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10088
    vhost: oaAsync__10088
  printer:
    password: juQdA
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10088
    vhost: printer__10088
redis:
  oa:
    host: ***********
    password: google99
    port: 6383
    url: google99@***********:6383
sms:
  send:
    email:
      url: http://sms.sxzrtx.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.sxzrtx.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10088
sqlserver:
  after_write:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '#TJZnXAxUlXP'
    port: 1433
    username: ch999oanew__10088
  ch999oanew:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '#TJZnXAxUlXP'
    port: 1433
    username: ch999oanew__10088
  ch999oanewHis:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '#TJZnXAxUlXP'
    port: 1433
    username: ch999oanew__10088
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'dKuf5#uVmpN6'
    port: 1433
    username: office__10088
  oanewWrite:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '#TJZnXAxUlXP'
    port: 1433
    username: ch999oanew__10088
  office:
    dbname: office__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'dKuf5#uVmpN6'
    port: 1433
    username: office__10088
  officeWrite:
    dbname: office__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'dKuf5#uVmpN6'
    port: 1433
    username: office__10088
  smallpro_write:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "#TJZnXAxUlXP"
    port: 1433
    username: ch999oanew__10088
  web999:
    dbname: web999__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: j2nJStEWQBu6
    port: 1433
    username: web999__10088
  ch999oanewReport:
    dbname: ch999oanew__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "#TJZnXAxUlXP"
    port: 1433
    username: ch999oanew__10088
  ershou:
    dbname: ershou__10088
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "MR1dJtK^GRlp"
    port: 1433
    username: ershou__10088
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.sxzrtx.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.sxzrtx.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
autoCalculateSalary:
  xtenant: 10088

# 亚丁保险对接地址url参数等
yading:
  # 同步员工信息地址
  sync-user-url: https://mapi.cdydkj.cn/jiuji/User/synchronizaMember
  # 注册保险的地址
  insurance-register-url: http://jiuji.cdydkj.cn/RightApply/addApply
  # 接口加签的签名key
  sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa


apollo:
  file: application-order.yml