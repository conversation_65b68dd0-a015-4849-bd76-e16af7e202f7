## host
data1=************
data2=************
data3=************
data4=************
data5=************
web1=************
web2=************
linux1=************
linux2=************
vm1=************

## \u6570\u636E\uFFFD? sqlserver
sqlserver.data2.host=oa.sqlserver.serv.iteng.com
sqlserver.data2.port=1433

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data2.host}
sqlserver.ch999oanew.port=${sqlserver.data2.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=huateng_ch999oanew
sqlserver.ch999oanew.password=cpPE2Zv5JNId#EQ
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data2.host}
sqlserver.ch999oanewReport.port=${sqlserver.data2.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=huateng_ch999oanew
sqlserver.ch999oanewReport.password=cpPE2Zv5JNId#EQ

## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data2.host}
sqlserver.smallpro_write.port=${sqlserver.data2.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=huateng_ch999oanew
sqlserver.smallpro_write.password=cpPE2Zv5JNId#EQ

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=${sqlserver.data2.host}
sqlserver.ch999oanewHis.port=${sqlserver.data2.port}
sqlserver.ch999oanewHis.dbname=ch999oanew
sqlserver.ch999oanewHis.username=huateng_ch999oanew
sqlserver.ch999oanewHis.password=cpPE2Zv5JNId#EQ

## sqlserver:office
sqlserver.office.host=${sqlserver.data2.host}
sqlserver.office.port=${sqlserver.data2.port}
sqlserver.office.dbname=office
sqlserver.office.username=huateng_office
sqlserver.office.password=noYJ*szhN^9a%w9

# data1
sqlserver.data1.host=web.sqlserver.serv.iteng.com
sqlserver.data1.port=1433

## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=huateng_web999
sqlserver.web999.password=P6$y9e3o^CrCIjf

## sqlserver:oaOffice
sqlserver.officeWrite.host=${sqlserver.data2.host}
sqlserver.officeWrite.port=${sqlserver.data2.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=oaOffice
sqlserver.officeWrite.password=o#!@$DMas

## mysql
mysql.url=master.mysql.serv.iteng.com:3306

## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=OaCore2019!@#


consul.host=${data4}
consul.port=8500
redis.oa.host=oa.redis.serv.iteng.com
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
spring.redis.password=${redis.oa.password}

logging.config.path=classpath:log/log4j2-iteng.xml
## image
image.upload.url=http://master.weedfs.serv.iteng.com:9333
image.del.url=http://volume.weedfs.serv.iteng.com:5083
image.select.url=https://img.iteng.com/
## rabbitMq
rabbitmq.master.url=master.rabbitmq.serv.iteng.com
rabbitmq.master.port=5672
rabbitmq.master.username=oaAsync
rabbitmq.master.password=oaAsyncpwd
rabbitmq.master.vhost=oaAsync

spring.cloud.consul.discovery.instance-zone=iteng
instance-zone=iteng

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100

sms.url=https://sms.iteng.com/?test=
sms.send.email.url=https://sms.iteng.com/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx

mongodb.url1=mongodb://ch999oa:google@${data3}:27017/ch999oa

jiuji.sys.moa=https://moa.iteng.com
jiuji.sys.pc=https://oa.iteng.com

jiuji.seata.enabled=false
jiuji.seata.config.consul.address=xxx
jiuji.seata.config.consul.prefix=xxx
jiuji.seata.registry.consul.address=xxx
jiuji.seata.registry.consul.cluster=xxx

meituan.shangou.appId=6322
meituan.shangou.appSecret=8d5697553c53e7d1f26dff66dce21fec

autoCalculateSalary.xtenant=10002


# ï¿½Ç¶ï¿½ï¿½ï¿½ï¿½Õ¶Ô½Óµï¿½Ö·urlï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
# Í¬ï¿½ï¿½Ô±ï¿½ï¿½ï¿½ï¿½Ï¢ï¿½ï¿½Ö·
yading.sync-user-url: https://mapi.cdydkj.cn/jiuji/User/synchronizaMember
# ×¢ï¿½á±£ï¿½ÕµÄµï¿½Ö·
yading.insurance-register-url: http://jiuji.cdydkj.cn/RightApply/addApply
# ï¿½Ó¿Ú¼ï¿½Ç©ï¿½ï¿½Ç©ï¿½ï¿½key
yading.sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa

apollo.url=http://************:8081,http://************:8081,http://************:8081
apollo.file=application-order.yml

