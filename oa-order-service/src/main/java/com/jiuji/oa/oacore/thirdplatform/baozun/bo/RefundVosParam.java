package com.jiuji.oa.oacore.thirdplatform.baozun.bo;

import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款VO参数封装对象
 * <AUTHOR>
 * @since 2024/07/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RefundVosParam {
    /**
     * 三方原路径退款列表
     */
    private List<ThirdOriginRefundVo> originRefundVos;
    
    /**
     * 退款金额
     */
    private BigDecimal refundPrice;
    
    /**
     * 刷卡原路径退款列表
     */
    private List<CardOriginRefundVo> cardOriginRefundVos;
    
    /**
     * 其他退款列表
     */
    private List<OtherRefundVo> otherRefundVos;
}
