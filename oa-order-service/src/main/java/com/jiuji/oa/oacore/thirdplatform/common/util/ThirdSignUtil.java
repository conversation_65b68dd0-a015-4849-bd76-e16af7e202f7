package com.jiuji.oa.oacore.thirdplatform.common.util;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.util.SignUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 三方平台前面工具类
 * <AUTHOR>
 * @since 2024/5/21 9:53
 */
@Slf4j
public class ThirdSignUtil {

    /**
     * 美团签名校验
     * @param appSecret
     * @return
     */
    public static boolean meituanCheck(String appSecret){
        HttpServletRequest request = SpringContextUtil.getRequest().orElseThrow(() -> new CustomizeException("美团签名校验, 请求对象不能为空"));
        Map<String, String[]> parameterMap = request.getParameterMap();
        String sigName = "sig";
        String urlQuery = parameterMap.entrySet().stream()
                .filter(entry -> !sigName.equals(entry.getKey()))
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> StrUtil.format("{}={}", entry.getKey(),
                        URLDecoder.decode(ObjectUtil.defaultIfNull(ArrayUtil.get(entry.getValue(), 0), StringPool.EMPTY), StandardCharsets.UTF_8)))
                .collect(Collectors.joining(StringPool.AMPERSAND));
        String moaUrl = CommonUtils.getResultData(SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.MOA_URL),
                userMsg -> {
                    throw new CustomizeException(StrUtil.format("获取moa地址异常, 原因: {}", userMsg));
                });
        String urlForGenSig = StrUtil.format("{}/cloudapi_nc{}?{}{}", moaUrl,request.getRequestURI(), urlQuery, appSecret);
        try {
            String sig = SignUtil.genSig(urlForGenSig);
            String reqSign = request.getParameter(sigName);
            log.warn("签名字符串: {} , 生成sign: {}, 请求sign: {}", urlForGenSig, sig, reqSign);
            return Objects.equals(sig, reqSign);
        } catch (IOException|SgOpenException e) {
            throw new CustomizeException("构建美团签名异常", e);
        }
    }
}
