## host

data1=************ 
data2=************
data3=************ 
data4=************ 
data5=************
web1=************ 
web2=************
linux1=************ 
linux2=************
vm1=************ 
logserver=************ 

## \u6570\u636E\u5E93 sqlserver
sqlserver.data2.host=oa.sqlserver.serv.zlf.co
sqlserver.data2.port=1433

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data2.host}
sqlserver.ch999oanew.port=${sqlserver.data2.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=jiahai_ch999oanew
sqlserver.ch999oanew.password=ZYEny7J!QtU4DGjw
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data2.host}
sqlserver.ch999oanewReport.port=${sqlserver.data2.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=jiahai_ch999oanew
sqlserver.ch999oanewReport.password=ZYEny7J!QtU4DGjw

## sqlserver:ershou
sqlserver.ershou.host=web.sqlserver.serv.zlf.co
sqlserver.ershou.port=1433
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=jiahai_ershou
sqlserver.ershou.password=4D1zvo%^Z0VChFM5
## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data2.host}
sqlserver.smallpro_write.port=${sqlserver.data2.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=jiahai_ch999oanew
sqlserver.smallpro_write.password=ZYEny7J!QtU4DGjw

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=${sqlserver.data2.host}
sqlserver.ch999oanewHis.port=${sqlserver.data2.port}
sqlserver.ch999oanewHis.dbname=ch999oanew
sqlserver.ch999oanewHis.username=jiahai_ch999oanew
sqlserver.ch999oanewHis.password=ZYEny7J!QtU4DGjw

## sqlserver:office
sqlserver.office.host=${sqlserver.data2.host}
sqlserver.office.port=${sqlserver.data2.port}
sqlserver.office.dbname=office
sqlserver.office.username=jiahai_office
sqlserver.office.password=bwhG1BFc@u2#Yq2t

# data1
sqlserver.data1.host=web.sqlserver.serv.zlf.co
sqlserver.data1.port=1433

## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=jiahai_web999
sqlserver.web999.password=FL2nBkultoNdkcWQ

## sqlserver:oaOffice
sqlserver.officeWrite.host=${sqlserver.data2.host}
sqlserver.officeWrite.port=${sqlserver.data2.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=jiahai_office
sqlserver.officeWrite.password=bwhG1BFc@u2#Yq2t

## mysql
mysql.url=master.mysql.serv.zlf.co:3306

## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=OaCore2019#$%
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=OaNc2019!@#

## mysql:oa_log
mysql.oa_log.url=main.tidb.serv.zlf.co:8381
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=B5MpMel3MPA

redis.oa.host=oa.redis.serv.zlf.co
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
spring.redis.password=${redis.oa.password}

logging.config.path=classpath:log/log4j2-zlf.xml
## mongodb
mongodb.url1=mongodb://ch999oa:google@${data3}:27017/ch999oa
mongodb.url2=mongodb://ch999oa:google@${data4}:27017/ch999oa
mongodb.url3=mongodb://ch999oa:google@${data5}:27017/ch999oa
## image
image.upload.url=http://master.weedfs.serv.zlf.co:9333
image.del.url=http://volume.weedfs.serv.zlf.co:5083
image.select.url=https://img.zlf.co/

rabbitmq.master.url=master.rabbitmq.serv.zlf.co
rabbitmq.master.port=5672
rabbitmq.master.username=oaAsync
rabbitmq.master.password=oaAsyncpwd
rabbitmq.master.vhost=oaAsync

# oa
rabbitmq.oa.url=master.rabbitmq.serv.zlf.co
rabbitmq.oa.port=5672
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999
rabbitmq.oa.vhost=oa

consul.host=${spring.cloud.client.hostname}
consul.port=8500
spring.cloud.consul.discovery.instance-zone=zlf
instance-zone=zlf

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100


sms.url=http://sms.zlf.co/?test=
sms.send.email.url=http://sms.zlf.co/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx

jiuji.sys.moa=https://moa.zlf.co
jiuji.sys.pc=https://oa.zlf.co
jiuji.sys.inWcf=http://inwcf.zlf.co
jiuji.sys.oaWcf=http://inwcf2.zlf.co

jiuji.seata.enabled=false
jiuji.seata.config.consul.address=xxx
jiuji.seata.config.consul.prefix=xxx
jiuji.seata.registry.consul.address=xxx
jiuji.seata.registry.consul.cluster=xxx

autoCalculateSalary.xtenant=10001

# ï¿½Ç¶ï¿½ï¿½ï¿½ï¿½Õ¶Ô½Óµï¿½Ö·urlï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
# Í¬ï¿½ï¿½Ô±ï¿½ï¿½ï¿½ï¿½Ï¢ï¿½ï¿½Ö·
yading.sync-user-url: https://mapi.cdydkj.cn/jiuji/User/synchronizaMember
# ×¢ï¿½á±£ï¿½ÕµÄµï¿½Ö·
yading.insurance-register-url: http://jiuji.cdydkj.cn/RightApply/addApply
# ï¿½Ó¿Ú¼ï¿½Ç©ï¿½ï¿½Ç©ï¿½ï¿½key
yading.sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa

apollo.url=http://************:8081,http://************:8081,http://************:8081
apollo.file=application-order.yml

