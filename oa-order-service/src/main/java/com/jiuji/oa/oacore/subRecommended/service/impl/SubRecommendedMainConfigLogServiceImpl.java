package com.jiuji.oa.oacore.subRecommended.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedMainConfigLog;
import com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedMainConfigLogMapper;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedMainConfigLogService;
import com.jiuji.oa.oacore.subRecommended.vo.req.SaveLogReq;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.foundation.db.annotation.DS;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@DS(DataSourceConstants.OA_LOG)
public class SubRecommendedMainConfigLogServiceImpl extends ServiceImpl<SubRecommendedMainConfigLogMapper, SubRecommendedMainConfigLog> implements SubRecommendedMainConfigLogService {
    @Resource
    private SmsService smsService;

    @Resource
    private AbstractCurrentRequestComponent component;
    @Override
    public void insertLog(Integer mainConfigId, String content,String inUser) {
        if(StringUtil.isNotBlank(content) && StringUtil.isNotBlank(inUser) && ObjectUtil.isNotNull(mainConfigId)){
            try {
                SubRecommendedMainConfigLog log = new SubRecommendedMainConfigLog();
                log.setMainConfigId(mainConfigId)
                        .setDTime(LocalDateTime.now())
                        .setUpdateTime(LocalDateTime.now())
                        .setContent(content)
                        .setInUser(inUser);
                this.save(log);
            } catch (Exception e){
                String comment = String.format("mainConfigId:%s,content:%s,inUser:%s", mainConfigId, content, inUser);
                RRExceptionHandler.logError("套餐推荐日志保存异常",comment , e, smsService::sendOaMsgTo9JiMan);
            }
        }
    }

    @Override
    public void saveLog(SaveLogReq req) {
        OaUserBO userBO = Optional.ofNullable(component.getCurrentStaffId()).orElseThrow(() -> new RuntimeException("未获取到当前用户信息"));
        insertLog(req.getMainConfigId(),req.getComment(),userBO.getUserName());
    }



    @Override
    public List<SubRecommendedMainConfigLog> getLogById(Integer id) {
        return this.lambdaQuery().eq(SubRecommendedMainConfigLog::getMainConfigId,id).orderByAsc(SubRecommendedMainConfigLog::getDTime).list();
    }
}
