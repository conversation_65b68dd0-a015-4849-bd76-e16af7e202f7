package com.jiuji.oa.oacore.subRecommended.vo.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ProductBaseInfo {

    private Integer cid;

    private Integer productId;

    private Integer ppid;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品规格
     */
    private String productColor;
    /**
     * 库存(全区)
     */
    private Integer productCount;

    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 是否年包
     */
    private Boolean isAnnualPackage;

    /**
     * 是否为壳膜商品
     */
    private Boolean isCaseOrFilm;

    private Boolean isMobile;

}
