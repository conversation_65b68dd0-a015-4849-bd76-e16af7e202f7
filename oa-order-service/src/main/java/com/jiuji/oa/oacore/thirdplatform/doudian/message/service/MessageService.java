package com.jiuji.oa.oacore.thirdplatform.doudian.message.service;

import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.gson.JsonElement;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.DoudianTagEnum;
import com.jiuji.tc.common.vo.R;

/**
 * 抖音消息处理的主接口
 * <AUTHOR>
 * @since 2022/3/20 20:55
 */
public interface MessageService {
    /**
     * 处理接收到的消息内容
     *
     * @param json
     * @param msgParamRecord
     * @return
     */
    R handleMessage(String json, DoudianOpMsgParamRecord<JsonElement> msgParamRecord);

    /**
     * 接受的消息种类
     * @return
     */
    DoudianTagEnum acceptTag();
}
