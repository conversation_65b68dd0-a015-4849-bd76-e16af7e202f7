package com.jiuji.oa.afterservice.cloud.vo;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class YuYueDetailVO {

    /**
     * 处理情况
     */
    private String status;

    /**
     * 处理情况代码
     */
    private Integer statusNum;

    /**
     * 是否需要填写快递单
     */
    private Boolean showCourier;

    /**
     * 对处理情况的描述，有两句话，有换行
     */
    private List<String> statusInfo;

    /**
     * 订单进度条
     */
    private OrderProgressVO progress;

    private Integer wxType;

    /**
     * 处理进度日志
     */
    private List<Map<String,Object>> logs;

    /**
     * 订单信息
     */
    private OrderInfo orderInfo;

    /**
     * 维修详情
     */
    private WxInfo wxInfo;

    /**
     * 服务方式
     */
    private ServiceType serviceType;

    /**
     * 门店信息
     */
    private StoreInfo storeInfo;

    /**
     * 联系人信息
     */
    private UserInfo userInfo;

    /**
     * 寄回地址
     */
    private UserInfo sendBackAddress;

    /**
     * 邮寄送修地址
     */
    private UserInfo repairAddress;

    /**
     * 温馨提示
     */
    private String tips;

    /**
     * 接件人
     */
    private List<RepairOaUser> oaUser;


    @Getter
    @Setter
    public static class OrderInfo {

        /**
         * 售后单号
         */
        private Integer afterServiceId;

        /**
         * 预约单号
         */
        private Integer appointmentId;

        /**
         * 关联新机的订单
         */
        private Integer newOrderId;

        /**
         * 关联良品的订单
         */
        private Integer secondOrderId;

        /**
         * 下单时间
         */
        private String time;
    }

    @Getter
    @Setter
    public static class WxInfo{
        /**
         * 商品图片
         */
        private String image;

        /**
         * 机型
         */
        private String phoneModel;

        /**
         * 故障，不是只有一条的话拼接换行符
         */
        private List<Map<String,Object>> fault;

        /**
         * 优惠
         */
        private Map<String, Object> benefit;
        /**
         *维修总价
         */
        private String wxPrice;

        /**
         * 优惠券价格
         */
        private String discountCouponPrice;

        /**
         * 优惠码价格
         */
        private String promotionCodePrice;

        /**
         * 优惠码优惠费用
         */
        private BigDecimal discountAmount;


        public void setWxPrice(String wxPrice) {
            if (StringUtils.isBlank(wxPrice)){
                return;
            }
            Double totalPrice = Double.parseDouble(wxPrice);
            DecimalFormat df = new DecimalFormat("#0.00");
            this.wxPrice = df.format(totalPrice);
        }

        /**
         * 备注
         */
        private String remarks;
    }

    /**
     * 门店信息
     */
    @Getter
    @Setter
    public static class StoreInfo{

        private String selectStore;

        private String storeTel;

        private String storeAddress;

        private String workTime;

        /**
         * 门店详情地址
         */
        private String storeUrl;
    }

    @Setter
    @Getter
    public static class ServiceType{

        /**
         * 处理方式
         */
        private String handleType;

        /**
         * 服务方式
         */
        private String serviceType;

        /**
         * 上门时间
         */
        private String goHomeTime;

        /**
         * 到店时间
         */
        private String goStoreTime;

        /**
         * 上门地址
         */
        private String homeAddress;
    }

    @Getter
    @Setter
    public static class UserInfo{

        private String userName;

        private String userMobile;

        private String address;

    }

    @Data
    public static class RepairOaUser {

        private Integer userId;
        private String headImg;
        private String userName;
        private String linkUrl;
        /**
         * 职位名称
         */
        private String jobName;

    }
}
