package com.jiuji.oa.oacore.oaorder.bo;

import lombok.Data;

import java.time.LocalDateTime;


/**
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class EvaluateOrderBO {

    /**
     * 订单号
     */
    private Integer subId;

    /**
     * 订单类型
     */
    private String subType;

    /**
     * 评价类型
     */
    private Integer evaluateType;

    /**
     * 交易时间
     */
    private LocalDateTime tradeDate;
}
