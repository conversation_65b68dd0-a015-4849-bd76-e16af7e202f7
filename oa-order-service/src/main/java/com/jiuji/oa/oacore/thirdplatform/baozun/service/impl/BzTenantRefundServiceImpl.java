package com.jiuji.oa.oacore.thirdplatform.baozun.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.cloud.after.service.OaAfterClient;
import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.enums.TuihuanKindEnum;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.cloud.AfterCloud;
import com.jiuji.oa.oacore.cloud.bo.ShouhouLogReq;
import com.jiuji.oa.oacore.cloud.bo.SmallproAddLogReq;
import com.jiuji.oa.oacore.cloud.bo.SmallproLogRes;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.mapstruct.CommonStructMapper;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.service.RetryService;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.ChildMerchantNameEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantRefundMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.SecretEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantRefundService;
import com.jiuji.oa.oacore.thirdplatform.order.mapper.OrderMapper;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.OaVerifyUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpCookie;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 宝尊退货退款处理
 *
 * <AUTHOR>
 * @since 2021/10/8 13:49
 */
@Service
@Slf4j
@DS(DataSourceConstants.SMALLPRO_WRITE)
public class BzTenantRefundServiceImpl implements BzTenantRefundService {
    @Resource
    @Lazy
    private BzTenantRefundService bzTenantRefundService;
    @Resource
    private BzTenantRefundMapper bzTenantRefundMapper;
    /**
     * 每次最大处理记录数
     */
    private static final int MAX_REFUND_ORDER_NUM = 100;
    private static final Integer GUANG_ZHOU_ITENG = 10003;
    @Autowired
    private UserInfoClient userInfoClient;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private MemberClient memberClient;
    @Resource
    private SmsService smsService;

    @Resource
    private OrderMapper orderMapper;
    @Autowired
    private AfterCloud afterCloud;
    @Resource
    private OaAfterClient afterClient;
    @Resource
    private RetryService retryService;

    @Resource(name = "oaAsyncRabbitTempe")
    private RabbitTemplate oaAsyncRabbitTempe;
    private static final String TOW_KEY_FORMAT = "{}_{}";
    private static final String THREE_KEY_FORMAT = "{}_{}_{}";
    private static final String TUIKUAN_SUCCESS = "退款办理成功";
    private static final String REFUND_WAY_ORIGIN = "返回";
    private static final String XIAN_JIN = "现金";
    /**
     * 库存操作分布式锁
     */
    private static final String LOCK_SECRET_INSERT = "OA:JAVA:ORDER:LOCK_SECRET_INSERT:{}";
    /**
     * 模拟用户的token
     */
    private static final ConcurrentMap<Integer, String> USER_TOKEN_MAP = new ConcurrentHashMap<>();
    /**
     * 延时执行线程池
     */
    private static final ScheduledExecutorService delayTimeExecutorService = Executors.newScheduledThreadPool(2,
            ThreadFactoryBuilder.create().setNamePrefix("baozun_refund_").build());

    /**
     * 扫描退款订单信息 生成售后单并自动退款
     *
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public R<String> scannerRefund() {
        List<BzRefundBO> bzRefunds = listBzRefundOrderByAreaId(null);
        if (bzRefunds.isEmpty()) {
            return R.success("没有退货订单");
        }

        Integer currAreaId = null;
        for (BzRefundBO bzRefund : bzRefunds) {
            try {
                if (baseValidData(bzRefund)) {
                    //基础验证通过
                    BzTenantSalesOrder salesOrder = bzRefund.getSalesOrder();
                    AreaInfo areaInfo = bzRefund.getAreaInfo();
                    if (!Objects.equals(currAreaId, salesOrder.getAreaId())) {
                        //门店信息已变更
                        currAreaId = salesOrder.getAreaId();
                        simulateUser(areaInfo,bzRefund);
                    }
                    //获取三方收银信息
                    //获取三方收银明细
                    List<ThirdOriginRefundVo> originRefundVos = CommonUtils.getResultData(retryService
                                    .retryByFeignRetryableException(() -> afterClient.listAllThirdShouYing(bzRefund.getSub().getSubId(), 3)),
                            errMsg -> {
                                throw new CustomizeException(StrUtil.format("获取三方收银记录发生[{}]异常", errMsg));
                            });
                    bzRefund.setThirdOriginRefundVos(originRefundVos);
                    //暂时开放给广州易腾使用
                    List<Integer> OpenXtenantList = Arrays.asList(10050, GUANG_ZHOU_ITENG);
                    if(OpenXtenantList.contains(XtenantEnum.getXtenant())){
                        //获取现金收银金额
                        List<OtherRefundVo> otherRefundVos = CommonUtils.getResultData(retryService
                                        .retryByFeignRetryableException(() -> afterClient.listAllOtherRefund(bzRefund.getSub().getSubId(), TuihuanKindEnum.TK.getCode())),
                                errMsg -> {
                                    throw new CustomizeException(StrUtil.format("获取刷卡支付信息发生[{}]异常", errMsg));
                                });
                        if(CollUtil.isNotEmpty(otherRefundVos)){
                            List<OtherRefundVo> xianJinList = otherRefundVos.stream().filter(item -> XIAN_JIN.equals(item.getReturnWayName())).collect(Collectors.toList());
                            bzRefund.setOtherRefundVos(xianJinList);
                        }
                        //获取刷卡支付信息
                        List<CardOriginRefundVo> cardOriginRefundVos = CommonUtils.getResultData(retryService
                                        .retryByFeignRetryableException(() -> afterClient.getCardPayInfo(bzRefund.getSub().getSubId(), TuihuanKindEnum.TK.getCode())),
                                errMsg -> {
                                    throw new CustomizeException(StrUtil.format("获取刷卡支付信息发生[{}]异常", errMsg));
                                });
                        bzRefund.setCardOriginRefundVos(cardOriginRefundVos);
                    }
                    List<BzTenantSalesDetailBO> smallSalesDetails = new LinkedList<>();
                    for (BzTenantSalesDetailBO salesDetail : bzRefund.getSalesDetails()) {
                        if (validDetail(bzRefund, salesDetail)) {
                            //basket 肯定会存在,前面已经校验了basket是否存在
                            //按大件小件进行分类
                            if (Boolean.TRUE.equals(salesDetail.getBasket().getIsmobile())) {
                                mobileShouhouTuihuan(bzRefund, salesDetail);
                            } else {
                                //小件商品接件
                                smallSalesDetails.add(salesDetail);
                            }
                        } else {
                            //商品更新为处理失败
                            updateOrderDealResultAndCommit(salesDetail, false);
                        }
                    }
                    //小件单退款处理
                    if (!smallSalesDetails.isEmpty()) {
                        smallTuihuan(bzRefund, smallSalesDetails);
                    }
                    if(isOnlyCreateShouhou(bzRefund)){
                        // 线下订单只生成售后接件单
                        continue;
                    }
                    //推送宝尊处理完的广播
                    oaAsyncRabbitTempe.convertAndSend(RabbitMqConfig.BAOZUN_AUTO_REFUND_TOPIC,"",bzRefund.getSalesOrder().getTransactionNumber());
                } else {
                    //所有商品处理为更新失败
                    bzRefund.getSalesDetails().stream().forEach(sd -> updateOrderDealResultAndCommit(sd, false));
                }
            }catch(Exception e){
                RRExceptionHandler.logError("宝尊退款",bzRefund,e,bzRefund.getMessage()::add);
            }finally {
                //记录日志
                if (bzRefund.getMessage().length() > 0) {
                    appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(), bzRefund.getMessage().toString());
                }
            }

        }
        return R.success(StrUtil.format("处理了以下单号:{}", bzRefunds.stream().map(BzRefundBO::getSalesOrder)
                .map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.joining(SignConstant.COMMA))));
    }


    /**
     * 扫描退款订单信息 生成售后单并自动退款
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public R<String> scannerRefundForXXL() {
        List<BzRefundBO> bzRefunds = listBzRefundOrderByAreaIdForXXL(null);
        if (bzRefunds.isEmpty()) {
            return R.success("没有退货订单");
        }

        Integer currAreaId = null;
        for (BzRefundBO bzRefund : bzRefunds) {
            try {
                if (baseValidData(bzRefund)) {
                    //基础验证通过
                    BzTenantSalesOrder salesOrder = bzRefund.getSalesOrder();
                    AreaInfo areaInfo = bzRefund.getAreaInfo();
                    if (!Objects.equals(currAreaId, salesOrder.getAreaId())) {
                        //门店信息已变更
                        currAreaId = salesOrder.getAreaId();
                        simulateUser(areaInfo,bzRefund);
                    }
                    List<BzTenantSalesDetailBO> smallSalesDetails = new LinkedList<>();
                    for (BzTenantSalesDetailBO salesDetail : bzRefund.getSalesDetails()) {
                        if (validDetail(bzRefund, salesDetail)) {
                            //basket 肯定会存在,前面已经校验了basket是否存在
                            //按大件小件进行分类
                            if (Boolean.TRUE.equals(salesDetail.getBasket().getIsmobile())) {
                                mobileShouhouTuihuan(bzRefund, salesDetail);
                            } else {
                                //小件商品接件
                                smallSalesDetails.add(salesDetail);
                            }
                        } else {
                            //商品更新为处理失败
                            updateOrderDealResultAndCommit(salesDetail, false);
                        }
                    }
                    //小件单退款处理
                    if (!smallSalesDetails.isEmpty()) {
                        smallTuihuan(bzRefund, smallSalesDetails);
                    }
                } else {
                    //所有商品处理为更新失败
                    bzRefund.getSalesDetails().stream().forEach(sd -> updateOrderDealResultAndCommit(sd, false));
                }
            }catch(Exception e){
                RRExceptionHandler.logError("宝尊退款",bzRefund,e,bzRefund.getMessage()::add);
            }finally {
                //记录日志
                if (bzRefund.getMessage().length() > 0) {
                    appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(), bzRefund.getMessage().toString());
                }
            }

        }
        return R.success(StrUtil.format("处理了以下单号:{}", bzRefunds.stream().map(BzRefundBO::getSalesOrder)
                .map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.joining(SignConstant.COMMA))));
    }

    private void smallTuihuan(BzRefundBO bzRefund, List<BzTenantSalesDetailBO> smallSalesDetails) {
        //不存在小件单或存在有效的小件单且为系统处理的 才可以自动接件
        Smallpro smallPro = bzTenantRefundMapper.getSmallPro(bzRefund);
        String transactionNumber = bzRefund.getSalesOrder().getTransactionNumber();
        if(smallPro != null && (!Objects.equals("系统",smallPro.getInuser()) || StrUtil.contains(smallPro.getProblem(),transactionNumber))){
            new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
                bzTenantRefundMapper.appendOrderMsg(transactionNumber,StrUtil.format("已经手动接件,小件id:{}",smallPro.getId()));
                //更新处理结果为成功
                smallSalesDetails.stream().forEach(salesDetail->bzTenantRefundMapper.updateOrderDetailDealResult(salesDetail.getId(), true));
            }).commit();
            return;
        }
        //小简单接件
        addSmallProTuihuan(bzRefund, smallSalesDetails);

        if(isOnlyCreateShouhou(bzRefund)){
            return;
        }

        //退款审核
        CompletableFuture.runAsync(() -> smallHttpTuikuan(bzRefund, transactionNumber,() -> asyncBatchIntoStock(bzRefund,transactionNumber, smallSalesDetails)))
                .exceptionally(e -> {
                    RRExceptionHandler.logError("宝尊小件退款", bzRefund, e, bzRefund.getMessage()::add);
                    return null;
                }).join();
    }

    private static boolean isOnlyCreateShouhou(BzRefundBO bzRefund) {
        return ChildMerchantNameEnum.HDPOS.getCode().equals(bzRefund.getSalesOrder().getPlatformSource());
    }

    @Override
    public void asyncBatchIntoStock(BzRefundBO bzRefund, String transactionNumber, List<BzTenantSalesDetailBO> smallSalesDetails) {
        delayTimeExecutorService.schedule(() -> {
                    try {
                        //小件批量转现
                        String batchStockUrl = StrUtil.format("{}/cloudapi_nc/afterservice/api/smallpro/returnFactory/batchIntoStock", bzRefund.getMOaUrl());
                        SmallProBatchStockBo smallProBatchStockBo = new SmallProBatchStockBo().setPpidList(smallSalesDetails.stream()
                                .map(BzTenantSalesDetailBO::getPpid).collect(Collectors.toList())).setSmallproId(bzRefund.getSmallProId());
                        HttpRequest request = HttpUtil.createPost(batchStockUrl).header(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN, USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()))
                                .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, bzRefund.getAreaInfo().getXtenant() + "")
                                .header("xservicename", "oa-afterservice");
                        String inputJson = JSON.toJSONString(smallProBatchStockBo);
                        Optional.ofNullable(request.body(inputJson).execute())
                                .ifPresent(sResult -> handleSmallProTransactResult(bzRefund, batchStockUrl, inputJson, sResult, null, "小件批量转现"));
                    } catch (Exception e) {
                        //异常记录日志
                        RRExceptionHandler.logError("宝尊小件批量转现", bzRefund, e, msg -> appendOrderMsgAndCommit(transactionNumber, msg));
                    }
                }, 1, TimeUnit.SECONDS);
    }

    @Override
    public void smallHttpTuikuan(BzRefundBO bzRefund, String transactionNumber, Runnable successCallback) {
        AreaInfo areaInfo = bzRefund.getAreaInfo();
        String url = StrUtil.format("{}/oaApi.svc/rest/Tuikuan_Linqi", bzRefund.getInWcfUrl());
        AfterSmallTuikuanBO data = new AfterSmallTuikuanBO().setId(bzRefund.getSmallProTuihuanId())
                .setAreaid(areaInfo.getId()).setUser("系统").setRank(Arrays.asList("777"));
        String inputJson = JSON.toJSONString(getInput(bzRefund.getSecret(), data));
        try {
            HttpRequest request = HttpUtil.createPost(url).header(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN, USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()))
                    .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, areaInfo.getXtenant() + "");
            Optional.ofNullable(request.body(inputJson).execute())
                    .ifPresent(sResult -> handleSmallProTransactResult(bzRefund,url, inputJson, sResult,successCallback, "小件退款"));
        } catch (RuntimeException e) {
            RRExceptionHandler.logError("宝尊小件退款办理",inputJson,e,msg->appendOrderMsgAndCommit(transactionNumber,msg));
        }
    }

    /**
     * 保存三方收银记录
     * @param tuihuanId
     * @param userName
     * @param myTuiWayDetails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveThirdOrigin(Integer tuihuanId, String userName, List<ThirdOriginRefundVo> myTuiWayDetails) {
        for (ThirdOriginRefundVo myTuiWayDetail : myTuiWayDetails) {
            if(!Boolean.TRUE.equals(myTuiWayDetail.getIsDel())){
                //非删除才需要增加返回
                myTuiWayDetail.setReturnWayName(StrUtil.addSuffixIfNot(myTuiWayDetail.getReturnWayName(), "返回"));
            }
        }
        //插入明细信息
        int detailInsertNum = bzTenantRefundMapper.batchInsertThirdOrigin(tuihuanId, userName, myTuiWayDetails);
        Assert.isTrue(detailInsertNum == myTuiWayDetails.size(),"三方支付原路退款插入明细失败");
        //更新shouying_other的金额
        Map<Integer,ThirdOriginRefundVo> updateOtherMap = myTuiWayDetails.stream()
                .collect(Collectors.toMap(ThirdOriginRefundVo::getOtherRecordId,
                        mtd -> new ThirdOriginRefundVo().setId(mtd.getId())
                                .setRefundPrice(ObjectUtil.defaultIfNull(mtd.getRefundPrice(), BigDecimal.ZERO))
                                .setOtherRecordId(mtd.getOtherRecordId()).setGroupCode(mtd.getGroupCode()),
                        (v1,v2)->v1.setRefundPrice(v1.getRefundPrice().add(v2.getRefundPrice()))));
        int refundPriceUpdateNum = bzTenantRefundMapper.batchUpdateThirdOriginShouyingOther(updateOtherMap.values());
        Assert.isTrue(refundPriceUpdateNum == updateOtherMap.size(),"三方支付原路退款更新退款金额失败");
    }


    /**
     * 保存刷卡支付信息
     * @param tuihuanId
     * @param userName
     * @param myTuiWayDetails
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCardPayOriginWay(Integer tuihuanId, String userName, List<CardOriginRefundVo> myTuiWayDetails) {
        for (CardOriginRefundVo myTuiWayDetail : myTuiWayDetails) {
            if(!Boolean.TRUE.equals(myTuiWayDetail.getIsDel())){
                //非删除才需要增加返回
                myTuiWayDetail.setReturnWayName(StrUtil.addSuffixIfNot(myTuiWayDetail.getReturnWayName(), REFUND_WAY_ORIGIN));
            }
        }
        bzTenantRefundMapper.batchInsertCardPayOriginWay(tuihuanId, userName,myTuiWayDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOtherRefundVo(Integer tuihuanId, String userName, List<OtherRefundVo> myTuiWayDetails) {
        bzTenantRefundMapper.batchInsertOtherRefundVo(tuihuanId, userName, myTuiWayDetails);
    }


    private void addSmallProTuihuan(BzRefundBO bzRefund, List<BzTenantSalesDetailBO> smallSalesDetails) {
        String name = smallSalesDetails.stream().map(BzTenantSalesDetailBO::getProductInfo).collect(Collectors.groupingBy(Productinfo::getProductName))
                .entrySet().stream().map(entry -> StrUtil.join(SignConstant.BLANK, entry.getKey(), entry.getValue().stream()
                        .map(Productinfo::getProductColor).filter(StrUtil::isNotBlank).collect(Collectors.toList())))
                .collect(Collectors.joining(SignConstant.BLANK));
        //退款金额计算
        BigDecimal tuikuanM = smallSalesDetails.stream()
                .map(ssd -> Optional.ofNullable(ssd).map(BzTenantSalesDetailBO::getBasket).map(Basket::getPrice2)
                        .map(Convert::toBigDecimal).map(price -> price.multiply(new BigDecimal(ssd.getQuantity()))).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal::add)
                .orElseThrow(()->new CustomizeException("退款金额异常,不可能发生,前面已经做了校验")).setScale(NumberConstant.FOUR, RoundingMode.HALF_DOWN);
        BigDecimal inPrice = smallSalesDetails.stream().filter(ssd->Objects.nonNull(ssd.getBasket().getInprice()))
                .map(ssd->new BigDecimal(ssd.getBasket().getInprice()).multiply(new BigDecimal(ssd.getQuantity())))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(3,RoundingMode.HALF_UP);
        new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
            //插入小件接件信息
            bzTenantRefundMapper.insertSmallPro(bzRefund,name);
            //插入小件接件商品信息
            bzTenantRefundMapper.batchInsertSmallProBill(bzRefund.getSmallProId(), smallSalesDetails);

            if(!isOnlyCreateShouhou(bzRefund)){
                List<ThirdOriginRefundVo> originRefundVos = ObjectUtil.defaultIfNull(bzRefund.getThirdOriginRefundVos(), Collections.emptyList());
                List<CardOriginRefundVo> cardOriginRefundVos = ObjectUtil.defaultIfNull(bzRefund.getCardOriginRefundVos(), Collections.emptyList());
                List<OtherRefundVo> otherRefundVos = ObjectUtil.defaultIfNull(bzRefund.getOtherRefundVos(), Collections.emptyList());

                RefundVosParam param = new RefundVosParam(originRefundVos, tuikuanM, cardOriginRefundVos, otherRefundVos);
                RefundVosResult result = getCurrThirdOriginRefundVos(param);
                List<ThirdOriginRefundVo> currOriginRefundVos = result.getThirdOriginRefundVos();

                //插入退款信息
                bzTenantRefundMapper.insertSmallProTuihuan(bzRefund,tuikuanM,inPrice,currOriginRefundVos);
                if (!currOriginRefundVos.isEmpty()) {
                    bzTenantRefundService.saveThirdOrigin(bzRefund.getSmallProTuihuanId(), "系统", currOriginRefundVos);
                }
                //插入退款商品详情
                bzTenantRefundMapper.batchInsertSmallProTuihuanBill(bzRefund.getSmallProTuihuanId(), smallSalesDetails);
            }

            //记录接件消息
            bzTenantRefundMapper.appendOrderMsg(bzRefund.getSalesOrder().getTransactionNumber(), StrUtil.format("接件成功,小件id为:{}", bzRefund.getSmallProId()));
            asyncAddSubLog(bzRefund, StrUtil.format("系统自动接件成功, 小件单号: <a href=\"/staticpc/#/small-refund/{}\"  target=\"_blank\">{}</a>",
                    bzRefund.getSmallProId(), bzRefund.getSmallProId()));
            //更新处理结果为成功
            smallSalesDetails.stream().forEach(salesDetail->bzTenantRefundMapper.updateOrderDetailDealResult(salesDetail.getId(), true));
        }).commit();
        //异步添加进程
        asyncAddSmallLog(bzRefund, name, StrFormatter.format("宝尊订单退款,【BJ】【{}】{}", bzRefund.getSalesOrder().getPlatformSource(), bzRefund.getSalesOrder().getSlipCode()));
    }

    private void asyncAddSmallLog(BzRefundBO bzRefund, String name, String comment) {
        CompletableFuture.runAsync(() -> {
            R<SmallproLogRes> sr = afterCloud.addSmallproLog(new SmallproAddLogReq().setSmallproId(bzRefund.getSmallProId())
                    .setComment(comment)
                    .setProductName(name).setUserName("系统").setShowType(0).setToEmail(0).setToSms(0).setToWeixin(0),
                    USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()), bzRefund.getAreaInfo().getXtenant());
            if (sr.getCode() != ResultCode.SUCCESS) {
                appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(), StrUtil.format("小件添加进程异常,原因:{}", StrUtil.blankToDefault(sr.getUserMsg(), sr.getMsg())));
            }
        }).exceptionally(e -> {
            RRExceptionHandler.logError("宝尊小件添加进程",bzRefund,e,bzRefund.getMessage()::add);
            return null;
        });
    }



    private void asyncAddSubLog(BzRefundBO bzRefund, String comment) {
        if(bzRefund.getSub() == null){
            return;
        }
        CompletableFuture.runAsync(() -> {
            R<Boolean> sr = SpringUtil.getBean(SubLogsCloud.class).addSubLog(LambdaBuild.create(new SubLogsNewReq())
                            .set(SubLogsNewReq::setSubId, bzRefund.getSub().getSubId())
                            .set(SubLogsNewReq::setComment, comment).set(SubLogsNewReq::setDTime, LocalDateTime.now())
                            .set(SubLogsNewReq::setInUser, "系统").set(SubLogsNewReq::setShowType, Boolean.FALSE)
                            .set(SubLogsNewReq::setType, 1)
                            .build());
            if (sr.getCode() != ResultCode.SUCCESS) {
                appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(), StrUtil.format("添加订单进程异常,原因:{}", StrUtil.blankToDefault(sr.getUserMsg(), sr.getMsg())));
            }
        }).exceptionally(e -> {
            RRExceptionHandler.logError("宝尊添加订单进程",bzRefund,e,bzRefund.getMessage()::add);
            return null;
        });
    }

    private void mobileShouhouTuihuan(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail) {
        boolean dealResult = true;
        for (BzTenantSalesDetailSninfo sn : salesDetail.getSalesDetailSninfos()) {
            //校验串号是否在库存中真实的存在
            if (validDetailSn(sn, salesDetail)) {
                //如果串号已经存在生效的售后接件,不再进行自动接件,只更新售后id
                Integer shouhouId = bzTenantRefundMapper.getShouhouId(bzRefund.getSub().getSubId(), sn.getSn());
                if(shouhouId != null){
                    new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
                        bzTenantRefundMapper.appendOrderMsg(salesDetail.getFkTransactionNumber(),StrUtil.format("串号:{}已经手动接件,售后id:{}", sn.getSn(),shouhouId));
                        bzTenantRefundMapper.updateSnShouhouId(sn.getId(),shouhouId);
                    }).commit();
                    continue;
                }
                //接件 退款
                new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE
                        , () -> addShouhouAndTuiHuan(bzRefund, salesDetail, sn)).commit();
                //异步添加进程
                ShouhouLogReq.ShouHouLogTypeEnum logType = ShouhouLogReq.ShouHouLogTypeEnum.WXFA;
                asyncAddMobileShouhouLog(sn.getShouhouId(), bzRefund, sn, logType, StrFormatter.format("宝尊订单退款,【BJ】【{}】{}", bzRefund.getSalesOrder().getPlatformSource(), bzRefund.getSalesOrder().getSlipCode()));
                if(isOnlyCreateShouhou(bzRefund)){
                    continue;
                }
                //退款操作,等c#接口,调用接口异常写入日志
                CompletableFuture.runAsync(() -> mobileHttpTuikuan(bzRefund, salesDetail, sn))
                        .exceptionally(e -> {
                            RRExceptionHandler.logError("宝尊大件退款", bzRefund, e, bzRefund.getMessage()::add);
                            return null;
                        }).join();
                //退款操作,发送消息
                CompletableFuture.runAsync(() -> {
                    if (NumberConstant.ONE.equals(sn.getInvStatus())) {
                        return;
                    }
                    AreaInfo areaInfo = bzRefund.getAreaInfo();
                    //发送消息给门店人员
                    String msg = "宝尊线上订单" + bzRefund.getSalesOrder().getSlipCode() + "退回非新机状态机器"
                            + sn.getSn() + "，请及时手工转为正确状态，并在操作备注中添加指定标识";
                    List<Integer> userIdList = new ArrayList<>();
                    List<Integer> personneList = orderMapper.getAllPersonneList(areaInfo.getId());

                    String inuser = bzRefund.getSub().getInuser();
                    if (StrUtil.isEmpty(inuser)
                            || "系统".equals(inuser)) {
                        userIdList.addAll(personneList);
                            msg = msg + "（此订单互道上操作员未与OA关联，自动通知给本店所有员工，请及时处理，如因未及时处理引起库存差异将处罚本店所有员工）";
                        } else {
                            Ch999UserVo ch999UserVo = Optional.ofNullable(userInfoClient.getCh999UserByUserName(inuser))
                                    .filter(R::isSuccess).map(R::getData).orElse(new Ch999UserVo());
                            if (CommonUtil.isNotNullZero(ch999UserVo.getCh999Id())) {
                                userIdList.add(ch999UserVo.getCh999Id());
                            } else {
                                userIdList.addAll(personneList);
                                msg = msg + "（此订单互道上操作员未与OA关联，自动通知给本店所有员工，请及时处理，如因未及时处理引起库存差异将处罚本店所有员工）";
                            }
                        }
                        if (CollUtil.isNotEmpty(userIdList)) {
                            smsService.sendOaMsg(msg, "", userIdList.stream()
                                    .map(String::valueOf).collect(Collectors.joining(",")), String.valueOf(OaMesTypeEnum.SYSTEM.getCode()));
                        }

                });

            } else {
                dealResult = false;
            }
        }
        updateOrderDealResultAndCommit(salesDetail, dealResult);
    }

    private void asyncAddMobileShouhouLog(Integer shouhouId, BzRefundBO bzRefund, BzTenantSalesDetailSninfo sn, ShouhouLogReq.ShouHouLogTypeEnum logType, String content) {
        String transactionNumber = bzRefund.getSalesOrder().getTransactionNumber();
        CompletableFuture.runAsync(()->{
            R<Boolean> rs = afterCloud.addShouhouLog(new ShouhouLogReq().setWxId(shouhouId)
                    .setNotice(new ShouhouLogReq.ShouhouLogNoticeBo().setNeedNotice(Boolean.FALSE).setToEmail(0).setToSms(0).setToWeixin(0))
                    .setType(logType.getCode()).setIsSendZY(0).setContent(content), USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()), bzRefund.getAreaInfo().getXtenant());
            if(rs.getCode() != ResultCode.SUCCESS){
                appendOrderMsgAndCommit(transactionNumber, StrUtil.format("大件添加进程异常,原因:{}", StrUtil.blankToDefault(rs.getUserMsg(),rs.getMsg())));
            }
        }).exceptionally(e->{
            RRExceptionHandler.logError("宝尊大件添加进程",bzRefund,e,msg->appendOrderMsgAndCommit(transactionNumber,msg));
            return null;
        });
    }

    private void mobileHttpTuikuan(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn) {
        AreaInfo areaInfo = bzRefund.getAreaInfo();
        String url = StrUtil.format("{}/oaApi.svc/rest/AftersaleTransact", bzRefund.getInWcfUrl());
        MobileTuihuanTransactBO data = new MobileTuihuanTransactBO().setAreaid(areaInfo.getId()).setAreaKind1(areaInfo.getKind1())
                .setTuikuanId(sn.getShouhouTuihuanId()).setRank(Arrays.asList("777")).setUser("系统")
                .setBasketId(salesDetail.getBasket().getBasketId());

        String inputJson = JSON.toJSONString(getInput(bzRefund.getSecret(), data));
        try {
            HttpRequest request = HttpUtil.createPost(url).header(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN, USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()))
                    .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, areaInfo.getXtenant() + "");
            Optional.ofNullable(request.body(inputJson).execute())
                    .ifPresent(sResult -> handleMobileHttpResult("退款办理",salesDetail, sn, inputJson, sResult,()-> {
                        Integer shouhouId = sn.getShouhouId();
                        asyncAddMobileShouhouLog(shouhouId, bzRefund,sn, ShouhouLogReq.ShouHouLogTypeEnum.WXJD,TUIKUAN_SUCCESS);
                        //自动取机
                        updateToYiXiuhao(shouhouId);
                        mobileQuji(shouhouId,bzRefund,salesDetail,sn,() -> asyncMobileXianhuoHttp(bzRefund, salesDetail, sn));
                    }));
        } catch (RuntimeException e) {
            RRExceptionHandler.logError("宝尊大件退款办理",inputJson,e,msg-> appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),msg));
        }
    }

    private static Dict getInput(String secret,Object data) {
        long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        return Dict.create().set("item",Dict.create().set("sign",OaVerifyUtil.getOaSign(secret, data,timeStamp))
                .set("timestamp",timeStamp).set("Data", data));
    }

    private int updateOrderDealResultAndCommit(BzTenantSalesDetailBO salesDetail, boolean dealResult) {
        //更新处理结果
        int[] result = new int[]{0};
        new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE, () -> result[0] = bzTenantRefundMapper.updateOrderDetailDealResult(salesDetail.getId(), dealResult)).commit();
        return result[0];
    }

    private void handleMobileHttpResult(String name,BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn,
                                        String inputJson, HttpResponse sResult, Runnable successCallBack) {
        log.debug("大件{}输入参数:{}",name,inputJson);
        if (sResult.isOk()) {
            log.debug("大件{}返回结果:{}",name,sResult.body());
            R r = JSON.parseObject(StrUtil.replace(StrUtil.strip(sResult.body(),"\""),"\\\"","\""),R.class);
            if (r.getCode() == ResultCode.SUCCESS) {
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrUtil.format("串号:{}{}成功", sn.getSn(),name));
                if(successCallBack != null){
                    successCallBack.run();
                }
            } else {
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrFormatter.format("串号:{}{}失败,返回[{}]异常,参数:{}", sn.getSn(),name, StrUtil.blankToDefault(r.getMsg(),r.getUserMsg()), inputJson));
            }
        } else {
            //接口发生系统异常
            appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrFormatter.format("调用{}接口系统[{}]异常,参数:{}",name, sResult.getStatus(), inputJson));
        }
    }

    @Override
    public void mobileQuji(Integer shouhouId, BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn, Runnable successCallback) {
        String inputJson = JSON.toJSONString(getInput(bzRefund.getSecret(),new AftersaleQujiInputBO().setId(shouhouId).setStaffId(0)
                .setAreaid(bzRefund.getAreaInfo().getId()).setRank(Arrays.asList("777","1c7")).setUser("系统")));
        String url = StrUtil.format("{}/oaApi.svc/rest/quji", bzRefund.getInWcfUrl());
        try {
            HttpRequest request = HttpUtil.createPost(url).header(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN, USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()))
                    .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, bzRefund.getAreaInfo().getXtenant() + "");
            Optional.ofNullable(request.body(inputJson).execute())
                    .ifPresent(sResult -> handleMobileHttpResult("取机", salesDetail, sn, inputJson, sResult,successCallback));
        } catch (RuntimeException e) {
            RRExceptionHandler.logError("宝尊大件取机",inputJson,e,msg-> appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),msg));
        }
    }

    @Override
    public void asyncMobileXianhuoHttp(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn) {
        //取机成功后新机单转入库存,获取现货单id,修改现货单必须的数据
        CompletableFuture.runAsync(()-> {
            //添加维修方案
            String comment = StrFormatter.format("宝尊订单退款,【BJ】【{}】{}", bzRefund.getSalesOrder().getPlatformSource(),
                    bzRefund.getSalesOrder().getSlipCode());
            //获取现货维修单号
            AfterXianhuoBo xianhuo = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantRefundMapper.getXianHuoByFromId(sn.getShouhouId()));
            Integer shouhouId = xianhuo.getShouhouId();

            if(Objects.isNull(xianhuo) || Objects.isNull(xianhuo.getMkcId())){
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),"获取不到现货单号或库存单号");
                return;
            }

            //售后转出 转新机
            AfterTransferBo input = new AfterTransferBo().setActionName("save").setKinds("h2").setMkcId(xianhuo.getMkcId())
                    .setPrice1(xianhuo.getInBeihuoPrice()).setPrice2(BigDecimal.ZERO).setInpriceNow(xianhuo.getInBeihuoPrice())
                    .setComment(comment);
            String mkdUrl = StrUtil.format("{}/commonApi/MkcDelOperate", bzRefund.getMOaUrl());
            HttpRequest request = HttpUtil.createPost(mkdUrl).auth(USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()))
                    .header(AbstractCurrentRequestComponent.REQUEST_HEADER_XTENANT, bzRefund.getAreaInfo().getXtenant() + "",true)
                    .header(Header.USER_AGENT,AbstractCurrentRequestComponent.REQUEST_HEADER_USER_AGENT_IPHONE,true)
                    .cookie(new HttpCookie("isApp", "1"));
            //提交申请
            JSONObject inputMap = (JSONObject)JSON.toJSON(input);
            HttpResponse httpResponse = request.form(inputMap).execute();
            Optional<R<JSONObject>> rOpt = Optional.ofNullable(JSON.parseObject(httpResponse.body(), R.class));
            Integer mkdId = rOpt.filter(R::isSuccess).map(R::getData).map(d->d.getInteger("id")).orElse(null);
            if(Objects.isNull(mkdId)){
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrUtil.format("地址:{},参数:{},结果:{}",
                        mkdUrl,JSON.toJSONString(inputMap),rOpt.map(r -> ObjectUtil.defaultIfBlank(r.getUserMsg(),r.getMsg()))
                                .orElse(httpResponse.body())));
                return;
            }
            //审核1
            inputMap = (JSONObject)JSON.toJSON(input.setId(mkdId).setActionName("acept1"));
            httpResponse = request.form(inputMap).execute();
            String msg = Optional.ofNullable(JSON.parseObject(httpResponse.body(), R.class)).filter(r->!r.isSuccess())
                    .map(r -> ObjectUtil.defaultIfBlank(r.getUserMsg(),r.getMsg())).orElse(null);
            if(StrUtil.isNotBlank(msg)){
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrUtil.format("现货审核1失败: 地址:{},参数:{},结果:{}",
                        mkdUrl,JSON.toJSONString(inputMap),msg));
                return;
            }
            //审核2
            inputMap = (JSONObject)JSON.toJSON(input.setId(mkdId).setActionName("acept2"));
            //更新为已修好
            updateToYiXiuhao(shouhouId);
            httpResponse = request.form(inputMap).execute();
            msg = Optional.ofNullable(JSON.parseObject(httpResponse.body(), R.class)).filter(r->!r.isSuccess())
                    .map(r -> ObjectUtil.defaultIfBlank(r.getUserMsg(),r.getMsg())).orElse(null);
            if(StrUtil.isNotBlank(msg)){
                appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrUtil.format("现货审核2失败: 地址:{},参数:{},结果:{}",
                        mkdUrl,JSON.toJSONString(inputMap),msg));
                return;
            }
        }).exceptionally(e->{
            RRExceptionHandler.logError("宝尊大件现货",bzRefund,e,msg->appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),msg));
            return null;
        }).join();
    }

    private void updateToYiXiuhao(Integer shouhouId) {
        new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE, () -> bzTenantRefundMapper.updateShouhouQujiData(shouhouId)).commit();
    }

    private void handleSmallProTransactResult(BzRefundBO bzRefund, String url, String inputJson, HttpResponse sResult, Runnable successCallback, String workName) {
        String transactionNumber = bzRefund.getSalesOrder().getTransactionNumber();
        log.debug("{}输入参数:{}",workName,inputJson);
        if (sResult.isOk()) {
            log.debug("{}返回结果:{}",workName,sResult.body());
            R r = JSON.parseObject(StrUtil.replace(StrUtil.strip(sResult.body(),"\""),"\\\"","\""),R.class);
            if (r.getCode() == ResultCode.SUCCESS) {
                Optional.ofNullable(successCallback).ifPresent(Runnable::run);
                appendOrderMsgAndCommit(transactionNumber,workName+"成功");
                asyncAddSmallLog(bzRefund,"",workName+"成功");

            } else {
                appendOrderMsgAndCommit(transactionNumber,StrFormatter.format("{}失败,返回[{}]异常,地址:{},参数:{}",workName, StrUtil.blankToDefault(r.getUserMsg(),r.getMsg()),url, inputJson));
            }
        } else{
            int errorCode = CommonUtils.getRandom4Code();
            log.error("{}返回结果:{},错误编号:{}",workName,sResult.body(), errorCode);
            //接口发生系统异常
            appendOrderMsgAndCommit(transactionNumber,StrFormatter.format("调用{}接口系统[{}]异常,错误编号:{},地址:{},参数:{}",workName, sResult.getStatus(),errorCode,url, inputJson));
        }
    }

    /**
     * 添加售后及退换信息
     * @param bzRefund
     * @param salesDetail
     * @param sn
     */
    private void addShouhouAndTuiHuan(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail, BzTenantSalesDetailSninfo sn) {
        R<String> soiR = afterCloud.getShouhouOrderId(USER_TOKEN_MAP.get(bzRefund.getAreaInfo().getId()), bzRefund.getAreaInfo().getXtenant());
        if(soiR.getCode() != ResultCode.SUCCESS){
            bzTenantRefundMapper.appendOrderMsg(salesDetail.getFkTransactionNumber(),StrUtil.format("串号:{}接件失败,原因:{}", sn.getSn(),soiR.getUserMsg()));
            return;
        }
        bzTenantRefundMapper.insertShouhou(bzRefund, salesDetail, sn,soiR.getData());
        Integer shouhouId = sn.getShouhouId();
        //售后接件时间
        bzTenantRefundMapper.insertShouhouTimePoint(shouhouId);
        //更新sn的售后id
        bzTenantRefundMapper.updateSnShouhouId(sn.getId(),shouhouId);
        if(!isOnlyCreateShouhou(bzRefund)){
            //插入退货表信息
            ProductMkc productMkc = salesDetail.getProductMkcs().stream().filter(pm -> Objects.equals(sn.getSn(), pm.getImei()))
                    .findFirst().orElseThrow(()->new CustomizeException(StrUtil.format("串号[{}]库存不存在,不可能发生,前面已经做了数据校验!", sn.getSn())));
            bzTenantRefundMapper.insertReturnCb(salesDetail, sn, productMkc);

            List<ThirdOriginRefundVo> originRefundVos = ObjectUtil.defaultIfNull(bzRefund.getThirdOriginRefundVos(), Collections.emptyList());
            List<CardOriginRefundVo> cardOriginRefundVos = ObjectUtil.defaultIfNull(bzRefund.getCardOriginRefundVos(), Collections.emptyList());
            List<OtherRefundVo> otherRefundVos = ObjectUtil.defaultIfNull(bzRefund.getOtherRefundVos(), Collections.emptyList());

            RefundVosParam param = new RefundVosParam(originRefundVos, BigDecimal.valueOf(salesDetail.getBasket().getPrice2()), cardOriginRefundVos, otherRefundVos);
            RefundVosResult result = getCurrThirdOriginRefundVos(param);
            List<ThirdOriginRefundVo> currOriginRefundVos = result.getThirdOriginRefundVos();

            sn.setThirdOriginRefundVos(currOriginRefundVos);
            //插入退款信息
            bzTenantRefundMapper.insertShouhouTuihuan(bzRefund, salesDetail, sn,productMkc);

            if (!currOriginRefundVos.isEmpty()) {
                bzTenantRefundService.saveThirdOrigin(sn.getShouhouTuihuanId(), "系统", currOriginRefundVos);
            }
        }

        bzTenantRefundMapper.appendOrderMsg(salesDetail.getFkTransactionNumber(),StrUtil.format("串号:{}已经申请退款,售后id:{}", sn.getSn(), sn.getShouhouId()));
        asyncAddSubLog(bzRefund, StrUtil.format("串号:{} 系统自动接件成功, 售后单号: <a href=\"/staticpc/#/after-service/order/edit/{}\"  target=\"_blank\">{}</a>",
                sn.getSn(), sn.getShouhouId(), sn.getShouhouId()));
    }



    /**
     * 获取当前退款的三方收银记录
     * @param param 退款参数封装对象
     * @return 退款结果封装对象
     */
    private RefundVosResult getCurrThirdOriginRefundVos(RefundVosParam param) {
        CommonStructMapper commonStructMapper = SpringUtil.getBean(CommonStructMapper.class);
        List<ThirdOriginRefundVo> currOriginRefundVos = new LinkedList<>();
        List<ThirdOriginRefundVo> originRefundVos = ObjectUtil.defaultIfNull(param.getOriginRefundVos(), Collections.emptyList());
        BigDecimal refundPrice = ObjectUtil.defaultIfNull(param.getRefundPrice(), BigDecimal.ZERO);

        for (int i = 0; i < originRefundVos.size(); i++) {
            ThirdOriginRefundVo originRefundVo = originRefundVos.get(i);
            if (refundPrice.compareTo(BigDecimal.ZERO) <= 0 && i > 0) {
                //退款小于0元,只有第一条需要生成记录
                break;
            }
            if (refundPrice.compareTo(BigDecimal.ZERO) > 0 && originRefundVo.getRefundPrice().compareTo(BigDecimal.ZERO) <= 0) {
                //退款大于0且没有可退金额, 跳过
                continue;
            }
            //处理退款方式的收银记录
            ThirdOriginRefundVo currThirdVo = commonStructMapper.copyThirdOriginRefund(originRefundVo)
                    .setRefundPrice(NumberUtil.min(originRefundVo.getRefundPrice(), refundPrice));
            //当前三方原路径扣减已退金额
            refundPrice = refundPrice.subtract(currThirdVo.getRefundPrice());
            originRefundVo.setRefundedPrice(originRefundVo.getRefundedPrice().add(currThirdVo.getRefundPrice()));
            originRefundVo.setRefundPrice(originRefundVo.getRefundPrice().subtract(currThirdVo.getRefundPrice()));
            currOriginRefundVos.add(currThirdVo);
        }

        return new RefundVosResult(
                currOriginRefundVos,
                ObjectUtil.defaultIfNull(param.getCardOriginRefundVos(), Collections.emptyList()),
                ObjectUtil.defaultIfNull(param.getOtherRefundVos(), Collections.emptyList()));
    }

    /**
     * 获取当前退款的三方收银记录（保持向后兼容的重载方法）
     * @param originRefundVos 三方原路径退款列表
     * @param refundPrice 退款金额
     * @return 当前三方原路径退款列表
     */
    private List<ThirdOriginRefundVo> getCurrThirdOriginRefundVos(List<ThirdOriginRefundVo> originRefundVos, BigDecimal refundPrice) {
        RefundVosParam param = new RefundVosParam(originRefundVos, refundPrice, Collections.emptyList(), Collections.emptyList());
        return getCurrThirdOriginRefundVos(param).getThirdOriginRefundVos();
    }

    private boolean validDetailSn(BzTenantSalesDetailSninfo sn, BzTenantSalesDetailBO salesDetail) {
        if (salesDetail.getProductMkcs().stream().noneMatch(pm->Objects.equals(pm.getImei(),sn.getSn()))){
            appendOrderMsgAndCommit(salesDetail.getFkTransactionNumber(),StrUtil.format("串号:{}无法接件,串号错误",sn.getSn()));
            return false;
        }
        return true;
    }

    private boolean validDetail(BzRefundBO bzRefund, BzTenantSalesDetailBO salesDetail) {
        if(salesDetail.getPpid() == null){
            appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,没有对应的本地ppid", salesDetail.getUpc()));
            return false;
        }
        if(Objects.isNull(salesDetail.getLineTotal())){
            appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,实付金额不能为空", salesDetail.getUpc()));
        }
        if(Objects.isNull(salesDetail.getQuantity()) || salesDetail.getQuantity().compareTo(NumberConstant.ONE)<0){
            appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,数量不能小于1", salesDetail.getUpc()));
        }
        if(salesDetail.getBasket() == null){
            appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,没有对应的basket", salesDetail.getUpc()));
            return false;
        }
        if (Boolean.TRUE.equals(salesDetail.getBasket().getIsmobile())) {
            //串号信息不能为空
            if (CollUtil.isEmpty(salesDetail.getSalesDetailSninfos())) {
                appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,串号信息不能为空", salesDetail.getUpc()));
                return false;
            }
            //本地串号信息不能为空
            if (CollUtil.isEmpty(salesDetail.getProductMkcs())) {
                appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,没查询到本地串号信息", salesDetail.getUpc()));
                return false;
            }
        }
        if(salesDetail.getProductInfo() == null){
            appendOrderMsgAndCommit(bzRefund.getSalesOrder().getTransactionNumber(),StrUtil.format("upc[{}]无法退货,没有对应的产品信息", salesDetail.getUpc()));
            return false;
        }
        return true;
    }

    @Override
    public List<BzRefundBO> listBzRefundOrderByAreaId(List<String> transactionNumbers) {
        //查询未处理的退款订单信息
        List<BzTenantSalesOrder>  salesOrders = bzTenantRefundMapper.queryRefundOrder(MAX_REFUND_ORDER_NUM,transactionNumbers);
        if (salesOrders.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> orderIds = salesOrders.stream().map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.toList());
        //查询未处理的退款订单详情信息
        boolean isNotTest = CollUtil.isEmpty(transactionNumbers);
        List<BzTenantSalesDetailBO> salesDetails = bzTenantRefundMapper.queryRefundOrderDetail(orderIds, isNotTest);
        //查询串号
        List<BzTenantSalesDetailSninfo> salesDetailSninfos = bzTenantRefundMapper.queryRefundSninfo(orderIds,isNotTest);
        //查询退款金额
        List<BzTenantSalesTender> salesTenders = Collections.emptyList();//bzTenantRefundMapper.querySalesTenders(orderIds);
        //查询秘钥信息
        String secret = getShouHouThjSecret();
        //查询退款信息
        List<BzTenantApp> bzTenantApps = Optional.ofNullable(salesOrders.stream()
                .filter(so->Objects.nonNull(so.getFkTenantId()) && Objects.nonNull(so.getPlatformSource())).collect(Collectors.toList()))
        .filter(CollUtil::isNotEmpty).map(bzTenantRefundMapper::listApp).orElse(Collections.emptyList());
        //连接订单和订单详情
        List<BzRefundBO> bzRefunds = concat(salesOrders,salesDetails,salesTenders,bzTenantApps);
        concatLocalInfo(salesDetailSninfos, bzRefunds,secret);
        //设置in_wcf 地址
        setInWcfHost(bzRefunds);
        return bzRefunds;
    }


    @Override
    public List<BzRefundBO> listBzRefundOrderByAreaIdForXXL(List<String> transactionNumbers) {
        //查询未处理的退款订单信息
        List<BzTenantSalesOrder>  salesOrders = bzTenantRefundMapper.queryRefundOrder(MAX_REFUND_ORDER_NUM,transactionNumbers);
        if (salesOrders.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> orderIds = salesOrders.stream().map(BzTenantSalesOrder::getTransactionNumber).collect(Collectors.toList());
        //查询未处理的退款订单详情信息
        boolean isNotTest = CollUtil.isEmpty(transactionNumbers);
        List<BzTenantSalesDetailBO> salesDetails = bzTenantRefundMapper.queryRefundOrderDetailForXXL(orderIds, isNotTest);
        //查询串号
        List<BzTenantSalesDetailSninfo> salesDetailSninfos = bzTenantRefundMapper.queryRefundSninfo(orderIds,isNotTest);
        //查询退款金额
        List<BzTenantSalesTender> salesTenders = Collections.emptyList();//bzTenantRefundMapper.querySalesTenders(orderIds);
        //查询秘钥信息
        String secret = getShouHouThjSecret();
        //查询退款信息
        List<BzTenantApp> bzTenantApps = Optional.ofNullable(salesOrders.stream()
                .filter(so->Objects.nonNull(so.getFkTenantId()) && Objects.nonNull(so.getPlatformSource())).collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty).map(bzTenantRefundMapper::listApp).orElse(Collections.emptyList());
        //连接订单和订单详情
        List<BzRefundBO> bzRefunds = concat(salesOrders,salesDetails,salesTenders,bzTenantApps);
        concatLocalInfo(salesDetailSninfos, bzRefunds,secret);
        //设置in_wcf 地址
        setInWcfHost(bzRefunds);
        return bzRefunds;
    }

    @Override
    public String getShouHouThjSecret() {
        String secret = bzTenantRefundMapper.getSecretByCode(SecretEnum.SH_THJ.getCode());
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        if(StrUtil.isBlank(secret)){
            RLock lock = redissonClient.getLock(StrUtil.format(LOCK_SECRET_INSERT, SecretEnum.SH_THJ.getCode()));
            try {
                lock.lock();
                secret = bzTenantRefundMapper.getSecretByCode(SecretEnum.SH_THJ.getCode());
                if(StrUtil.isBlank(secret)){
                    secret = IdUtil.fastSimpleUUID();
                    bzTenantRefundMapper.insertSecret(SecretEnum.SH_THJ.getCode(),secret);
                }
            } finally {
                lock.unlock();
            }

        }
        return secret;
    }

    private static void setInWcfHost(List<BzRefundBO> bzRefunds) {
        //设置内网地址
        SysConfigClient sysConfigClient = SpringUtil.getBean(SysConfigClient.class);
        Dict inWcfUrlDict = bzRefunds.stream().map(BzRefundBO::getAreaInfo).filter(Objects::nonNull).map(AreaInfo::getXtenant).filter(Objects::nonNull).distinct()
                .reduce(Dict.create(), (dict, xtenant) -> dict.set(xtenant + "inWcf", Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                        .filter(r -> r.getCode() == ResultCode.SUCCESS).map(R::getData).orElse(null))
                        .set(xtenant + "mOaUrl", Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                        .filter(r -> r.getCode() == ResultCode.SUCCESS).map(R::getData).orElse(null)), ((dict1, dict2) -> dict1));
        bzRefunds.stream().filter(refund -> Objects.nonNull(refund.getAreaInfo()))
                .forEach(refund -> refund.setInWcfUrl(inWcfUrlDict.getStr(refund.getAreaInfo().getXtenant() + "inWcf"))
                        .setMOaUrl(inWcfUrlDict.getStr(refund.getAreaInfo().getXtenant() + "mOaUrl")));
    }

    private void concatLocalInfo(List<BzTenantSalesDetailSninfo> salesDetailSninfos, List<BzRefundBO> bzRefunds, String secret) {
        //获取所有订单信息
        Optional<List<Sub>> subsOpt = Optional.ofNullable(bzRefunds).map(bzs -> bzs.stream().map(BzRefundBO::getSalesOrder).map(BzTenantSalesOrder::getSubId)
                .filter(Objects::nonNull).collect(Collectors.toList())).filter(CollUtil::isNotEmpty).map(bzTenantRefundMapper::listSub);
        Map<Integer,Sub> subMap = subsOpt.map(ss -> CollUtil.toMap(ss,new HashMap<>(ss.size()),Sub::getSubId))
                .orElseGet(Collections::emptyMap);
        //获取用户信息
        Map<Integer, MemberBasicRes> memberMap = subsOpt.map(subs -> subs.stream().map(Sub::getUserid).filter(Objects::nonNull).distinct()
                .map(Convert::toInt).collect(Collectors.toList())).filter(CollUtil::isNotEmpty).map(memberClient::getMemberBasicList)
                .filter(r -> r.getCode() == ResultCode.SUCCESS).map(R::getData)
                .map(ms -> CollUtil.toMap(ms, new HashMap<>(ms.size()), MemberBasicRes::getId)).orElseGet(Collections::emptyMap);
        //查询所有的productinfo信息
        Map<Integer, Productinfo> productInfoMap = Optional.of(bzRefunds.stream().map(BzRefundBO::getSalesDetails).filter(CollUtil::isNotEmpty).flatMap(List::stream)
                .map(BzTenantSalesDetail::getPpid).filter(Objects::nonNull).distinct().collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty)
                .map(bzTenantRefundMapper::listProductInfoByPpid)
                .map(pps -> CollUtil.toMap(pps, new HashMap<>(pps.size()), Productinfo::getPpriceid)).orElseGet(Collections::emptyMap);
        //根据订单id与ppid获取basket信息
        Map<String,Basket> siGroup = Optional.ofNullable(bzRefunds)
                //过滤有效的订单数据,订单id不能为空,订单详情的ppid不全为空
                .map(brs->brs.stream().filter(br->Optional.ofNullable(br).map(BzRefundBO::getSalesOrder).map(BzTenantSalesOrder::getSubId).isPresent()
                        && Optional.ofNullable(br).map(BzRefundBO::getSalesDetails).map(sds->sds.stream().anyMatch(sd->sd.getPpid() != null)).isPresent())
                        .collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty)
                .map(bzTenantRefundMapper::queryBaskets)
                .map(bs->{
                    Map<String, Basket> map = CollUtil.newHashMap(bs.size()*2);
                    for (Basket b : bs) {
                        map.put(StrUtil.format(THREE_KEY_FORMAT,b.getSubId(),b.getPpriceid(), null), b);
                        map.put(StrUtil.format(THREE_KEY_FORMAT,b.getSubId(),b.getPpriceid(), b.getBasketId()), b);
                    }
                    return map;
                })
                .orElseGet(Collections::emptyMap);
        //查询所有的门店信息
        List<Integer> areaIds = bzRefunds.stream().map(BzRefundBO::getSalesOrder).map(BzTenantSalesOrder::getAreaId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, AreaInfo> areaInfoMap = Optional.ofNullable(areaIds).filter(CollUtil::isNotEmpty).map(aIds -> areaInfoClient.listAreaInfo(aIds))
                .map(R::getData).filter(CollUtil::isNotEmpty).map(areaInfos -> CollUtil.toMap(areaInfos,new HashMap<>(areaInfos.size()),AreaInfo::getId))
                .orElseGet(Collections::emptyMap);
        Map<String, List<BzTenantSalesDetailSninfo>> tnGroupSninfo = salesDetailSninfos.stream().filter(sn-> StrUtil.isNotBlank(sn.getSn()) && StrUtil.isNotBlank(sn.getUpc()))
                .collect(Collectors.groupingBy(sn->StrUtil.format(TOW_KEY_FORMAT,sn.getFkTransactionNumber(),sn.getUpc())));
        for (BzRefundBO bzRefund : bzRefunds) {
            //设置秘钥信息
            bzRefund.setSecret(secret);
            //连接订单和basket的信息
            Optional<Integer> subIdOpt = Optional.ofNullable(bzRefund.getSalesOrder()).map(BzTenantSalesOrder::getSubId);
            subIdOpt.map(subMap::get).ifPresent(bzRefund::setSub);
            Optional.ofNullable(bzRefund.getSub()).map(Sub::getUserid).map(Convert::toInt).map(memberMap::get).ifPresent(bzRefund::setMember);
            for (BzTenantSalesDetailBO salesDetail : bzRefund.getSalesDetails()) {
                //连接串号和商品
                Optional.ofNullable(tnGroupSninfo.getOrDefault(StrUtil.format(TOW_KEY_FORMAT,salesDetail.getFkTransactionNumber(),salesDetail.getUpc()),Collections.emptyList()))
                        .ifPresent(salesDetail::setSalesDetailSninfos);
                if(subIdOpt.isPresent() && Objects.nonNull(salesDetail.getPpid())){
                    salesDetail.setProductInfo(productInfoMap.get(salesDetail.getPpid()));
                    subIdOpt.map(subId->StrUtil.format(THREE_KEY_FORMAT,subId,salesDetail.getPpid(), salesDetail.getBasketId())).map(siGroup::get)
                            .ifPresent(salesDetail::setBasket);

                }
            }
            Optional.ofNullable(bzRefund.getSalesOrder()).map(BzTenantSalesOrder::getAreaId).map(areaInfoMap::get).ifPresent(bzRefund::setAreaInfo);
        }
        //查询所有串号信息
        Map<Integer, List<ProductMkc>> pmGroupMap = Optional.ofNullable(bzRefunds.stream().map(BzRefundBO::getSalesDetails).flatMap(List::stream)
                .filter(sd -> Objects.nonNull(sd.getBasket()) && CollUtil.isNotEmpty(sd.getSalesDetailSninfos())).collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty).map(bzTenantRefundMapper::listProductMkc)
                .filter(CollUtil::isNotEmpty).map(ps -> ps.stream().collect(Collectors.groupingBy(ProductMkc::getBasketId)))
                .orElseGet(Collections::emptyMap);
        bzRefunds.stream().map(BzRefundBO::getSalesDetails).flatMap(List::stream).filter(b->Objects.nonNull(b.getBasket()))
                .forEach(salesDetail-> {
                            salesDetail.setProductMkcs(pmGroupMap.getOrDefault(salesDetail.getBasket().getBasketId(),Collections.emptyList()));
                            if(!salesDetail.getProductMkcs().isEmpty()){
                                salesDetail.setSalesDetailSninfos(salesDetailSninfos.stream()
                                        .filter(sn -> salesDetail.getProductMkcs().stream().anyMatch( pm -> Objects.equals(pm.getImei(), sn.getSn())))
                                        .collect(Collectors.toList()));
                            }
                        });

    }

    /**
     * 验证数据是否有效
     * @param bzRefund
     * @return
     */
    private static boolean baseValidData(BzRefundBO bzRefund) {
        //本地订单号必须存在
        Optional<BzTenantSalesOrder> salesOrderOpt = Optional.ofNullable(bzRefund.getSalesOrder());
        if(!salesOrderOpt.map(BzTenantSalesOrder::getSubId).isPresent()){
            bzRefund.getMessage().add("没有对应本地单号");
        }else if(bzRefund.getSub() == null){
            bzRefund.getMessage().add(StrUtil.format("没有符合退货条件的订单[{}]", bzRefund.getSalesOrder().getSubId()));
        }else if(bzRefund.getMember() == null){
            bzRefund.getMessage().add(StrUtil.format("获取不到会员id[{}]的会员信息", bzRefund.getSub().getUserid()));
        }
        if(bzRefund.getAreaInfo() == null){
            bzRefund.getMessage().add("没有对应本地门店");
        }
        if(bzRefund.getSalesDetails().stream().map(BzTenantSalesDetailBO::getBasket).allMatch(Objects::isNull)){
            bzRefund.getMessage().add("没有本地订单详情信息");
        }
        //退款金额不能为空 不需要这个验证了
        /*if(CollUtil.isEmpty(bzRefund.getSalesTenders())){
            bzRefund.getMessage().add("退款金额不能为空");
        }*/
        //退款方式不能为空, 用实际三方退款方式,不需要这个验证了
        /*if(!Optional.ofNullable(bzRefund.getApp()).map(BzTenantApp::getRefundType).isPresent()){
            bzRefund.getMessage().add("退款方式不能为空");
        }*/
        //退款秘钥不能为空
        if(StrUtil.isBlank(bzRefund.getSecret())){
            bzRefund.getMessage().add("退款加密秘钥不能为空");
        }
        //inwcf地址不能为空
        if(StrUtil.isBlank(bzRefund.getInWcfUrl())){
            bzRefund.getMessage().add("inwcf地址不能为空");
        }
        return bzRefund.getMessage().length() == 0;
    }

    /**
     * 关联信息连接起来
     * @param salesOrders
     * @param salesDetails
     * @param salesTenders
     * @param bzTenantApps
     * @return
     */
    private static List<BzRefundBO> concat(List<BzTenantSalesOrder> salesOrders, List<BzTenantSalesDetailBO> salesDetails
            , List<BzTenantSalesTender> salesTenders, List<BzTenantApp> bzTenantApps) {
        Map<String, List<BzTenantSalesDetailBO>> tnGroupDetail = salesDetails.stream().collect(Collectors.groupingBy(BzTenantSalesDetailBO::getFkTransactionNumber));
        Map<String, List<BzTenantSalesTender>> tnGroupTender = salesTenders.stream().collect(Collectors.groupingBy(BzTenantSalesTender::getFkTransactionNumber));
        Map<String,BzTenantApp> tfGroupApp = CollUtil.toMap(bzTenantApps,new HashMap<>(bzTenantApps.size()),ba->StrUtil.format(TOW_KEY_FORMAT,ba.getFkTenantId(),ba.getChildMerchantName()));
        return salesOrders.stream().map(so->new BzRefundBO().setSalesOrder(so).setMessage(new StringJoiner(" "))
                //订单详情和串号信息
                .setSalesDetails(tnGroupDetail.get(so.getTransactionNumber()))
                //退款信息
                .setSalesTenders(tnGroupTender.get(so.getTransactionNumber()))
                .setApp(tfGroupApp.get(StrUtil.format(TOW_KEY_FORMAT,so.getFkTenantId(),so.getPlatformSource()))))
                .collect(Collectors.toList());
    }

    /**
     * 模拟用户信息
     */
    @Override
    public String simulateUser(AreaInfo areaInfo, BzRefundBO bzRefund){
        OaUserBO user = new OaUserBO();
        user.setUserName("系统");
        user.setXTenant(areaInfo.getXtenant());
        user.setAuthorizeId(areaInfo.getAuthorizeId());
        user.setArea(areaInfo.getArea());
        user.setRank(Arrays.asList("777","2c0","6f2"));
        user.setAreaId(areaInfo.getId());
        String userToken = USER_TOKEN_MAP.computeIfAbsent(areaInfo.getId(), k -> IdUtil.fastSimpleUUID());
        redisTemplate.opsForValue().set(AbstractCurrentRequestComponent.PC_OA_KEY + userToken, JSON.toJSONString(user), Duration.ofHours(NumberConstant.TWO));
        redisTemplate.opsForValue().set(AbstractCurrentRequestComponent.M_OA_KEY + userToken, JSON.toJSONString(Dict.create().set("LogInfo",user)), Duration.ofHours(NumberConstant.TWO));

        return userToken;
    }

    private int appendOrderMsgAndCommit(String transactionNumber, String message){
        int[] result = new int[]{0};
        new MultipleTransaction().execute(DataSourceConstants.SMALLPRO_WRITE
                ,()->result[0]=bzTenantRefundMapper.appendOrderMsg(transactionNumber,message)).commit();
        return result[0];
    }
}
