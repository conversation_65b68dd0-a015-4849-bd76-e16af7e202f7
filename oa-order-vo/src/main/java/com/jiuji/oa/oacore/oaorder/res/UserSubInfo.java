package com.jiuji.oa.oacore.oaorder.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户
 * <AUTHOR>
 */
@Data
public class UserSubInfo implements Serializable {


    private String mobile;


    /**
     * 交易完成时间【最近一次 已完成的订单】
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUserBuyTime;

    /**
     * 销售人员姓名【最近一次已完成的订单，最贵的商品的销售人员】
     */
    private String saleUserName;

}
