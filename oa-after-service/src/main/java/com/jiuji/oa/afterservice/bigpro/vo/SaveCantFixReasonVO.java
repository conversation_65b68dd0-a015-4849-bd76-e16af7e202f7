package com.jiuji.oa.afterservice.bigpro.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SaveCantFixReasonVO {

    /**
     * 售后id
     */
    private Integer shouHouId;



    /**
     * 原因
     */
    private List<String> reasonList;

    public SaveCantFixReasonVO() {
    }

    public SaveCantFixReasonVO(Integer shouHouId, List<String> reasonList) {
        this.shouHouId = shouHouId;
        this.reasonList = reasonList;
    }
}
