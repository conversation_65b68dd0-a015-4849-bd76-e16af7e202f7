package com.jiuji.oa.oacore.common.resubmitAspect;

import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.DateTimeFormatterUtil;
import com.jiuji.oa.oacore.common.util.RedisLockUtil;
import com.jiuji.oa.oacore.common.util.SpringExpressionLanguageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @description 幂等提交切面
 */
@Aspect
@Component
@RequiredArgsConstructor
public class ResubmitAspect {

    private static final String UNLOCK = "Unlock";
    private static final String DELIMITER = "|";
    private static final String BOOLEAN_FALSE = "false";
    private final AbstractCurrentRequestComponent requestComponent;

    /**
     * 环绕防止重复提交
     *
     * @param joinPoint joinPoint
     * @return Object
     * @throws Throwable throws
     */
    @Around("@annotation(com.jiuji.oa.oacore.common.resubmitAspect.Resubmit)")
    public Object handleResubmit(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Resubmit annotation = method.getAnnotation(Resubmit.class);
        final String key = lockMethod(joinPoint, method, annotation);
        // 执行目标方法
        Object proceed;
        try {
            proceed = joinPoint.proceed();
        } catch (Exception e) {
            Optional.ofNullable(key).ifPresent(RedisLockUtil::unlock);
            throw e;
        }
        // 方法执行后
        long delay = annotation.delay();
        unlockMethod(key, delay);
        return proceed;
    }

    /**
     * 方法执行前锁定方法
     *
     * @param joinPoint  joinPoint
     * @param method     method
     * @param annotation annotation
     * @return 返回锁的key
     */
    private String lockMethod(ProceedingJoinPoint joinPoint, Method method, Resubmit annotation) {
        boolean isDel = false;
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            String header = ((ServletRequestAttributes) requestAttributes).getRequest().getHeader(UNLOCK);
            isDel = DigestUtils.md5Hex(LocalDate.now().format(DateTimeFormatterUtil.getYmd())).equals(header);
        }
        String methodName = joinPoint.getTarget().getClass().getName() + "." + method.getName();
        List<Integer> strategy = Arrays.stream(annotation.strategy()).boxed().collect(Collectors.toList());
        boolean flag = true;
        String key = "";
        if (strategy.containsAll(Arrays.asList(Resubmit.TOKEN_STRATEGY, Resubmit.PARAMETER_STRATEGY))) {
            // token 和 parameter 策略(方法名+|+token+|+参数序列化作为key)
            List<String> parameters = SpringExpressionLanguageUtil.listValue(annotation.parameter(), joinPoint);
            if (CollectionUtils.isEmpty(parameters)) {
                throw new CustomizeException("注解中parameter参数不能为空");
            }
            String token = requestComponent.getCurrentStaffId().getToken();
            String parameterString = String.join(DELIMITER, parameters);
            key = methodName + DELIMITER + token + DELIMITER + parameterString;
            // 锁死，手动释放
            flag = isDel ? RedisLockUtil.unlock(key) : RedisLockUtil.lock(key, token, false);
        } else if (strategy.contains(Resubmit.TOKEN_STRATEGY)) {
            // token 策略(方法名+|+token作为key)
            String token = requestComponent.getCurrentStaffId().getToken();
            key = methodName + DELIMITER + token;
            flag = isDel ? RedisLockUtil.unlock(key) : RedisLockUtil.lock(key, token, false);
        } else if (strategy.contains(Resubmit.PARAMETER_STRATEGY)) {
            // parameter 策略(方法名+|+参数序列化作为key，使用时需要小心会锁定所有人)，如果有指定特定参数，则参数需要变更
            List<String> parameters = SpringExpressionLanguageUtil.listValue(annotation.parameter(), joinPoint);
            String parameterString = CollectionUtils.isEmpty(parameters) ? "" : String.join(DELIMITER, parameters);
            key = methodName + DELIMITER + parameterString;
            flag = isDel ? RedisLockUtil.unlock(key) : RedisLockUtil.lock(key, parameterString, true);
        }else if (strategy.contains(Resubmit.JUDGE_STRATEGY)){
            //防重复提交 个性化处理，
            String[] parameter = annotation.parameter();
            List<String> parameters = SpringExpressionLanguageUtil.listValue(parameter, joinPoint);
            //会取出第一个表达式中的参数判断是否是false  并隔离租户
            if (!CommonUtil.isJiuJiXtenant(requestComponent.getCurrentStaffId().getXTenant())){
                return "";
            }
            String parameterString = CollectionUtils.isEmpty(parameters) ? "" : String.join(DELIMITER, parameters);
            key = methodName + DELIMITER + parameterString;
            flag = isDel ? RedisLockUtil.unlock(key) : RedisLockUtil.lock(key, parameterString, true);
        }
        if (!flag) {
            throw new CustomizeException("短时间内禁止重复提交");
        }
        return key;
    }

    /**
     * 方法执行后解锁方法
     *
     * @param key 需要释放的key
     */
    private void unlockMethod(String key, Long delay) {
        RedisLockUtil.unlock(key, delay);
    }

}
