package com.jiuji.oa.oacore.thirdplatform.tuangou.service;

import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;
import com.jiuji.tc.common.vo.R;
import com.meituan.sdk.model.ddzh.common.migrateSession.MigrateSessionResponse;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeResponse;

/**
 * 美团点评团购核销
 * <AUTHOR>
 */
public interface MeituanTuangouService extends TuangouCertificateService{

     void savePayment (TuangouReceiptConsumeResponse receiptConsumeResponse);
    /**
     * 验券准备
     * @param req
     * @return
     */
    @Override
    String certificatePrepare (CertificatePrepareReq req);

    /**
     * 验券
     * @param req
     * @return
     */
    @Override
    String certificateVerify (CertificateVerifyReq req);

    /**
     * 撤销核销
     * @param req
     * @return
     */
    @Override
    String certificateCancel (CertificateCancelReq req);

    /**
     * 券状态查询
     * @param req
     * @return
     */
    @Override
    String certificateGet (CertificateGetReq req);

    /**
     * 券状态批量查询
     * @param req
     * @return
     */
    @Override
    String certificateQuery (CertificateQueryReq req);

    /**
     * 查询token
     * @param appKey
     * @return
     */
    String meituanAuthCallBack (String appKey, String authCode, Integer businessId,String state);

    /**
     * 获取授权链接
     * @param areaId
     * @return
     */
    String meituanAuthUrl(Integer areaId, Integer businessId);

    Boolean meituanRefreshToken(String appKey);

    void queryPoiMapping(String appKey);

    R<PageQuerySessionResponse> querySession(String appKey,String bjxAppKey);

    R<MigrateSessionResponse> migrateSession(String appKey, String bjxAppKey, String session);

    R<String> handleSessionTokenMapping(String appKey, String session);
}
