package com.jiuji.oa.oacore.thirdplatform.baozun.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BaozunTenantPurchaseDetailMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BaozunTenantPurchaseDetail;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.IBaozunTenantPurchaseDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-07-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaozunTenantPurchaseDetailServiceImpl
        extends ServiceImpl<BaozunTenantPurchaseDetailMapper, BaozunTenantPurchaseDetail>
        implements IBaozunTenantPurchaseDetailService {

}
