package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后重大办状态枚举
 * @author: gengjiaping
 * @date: 2020/5/9
 */
@Getter
@AllArgsConstructor
public enum ShouhouImportantStateEnum implements CodeMessageEnumInterface {
    DEAL_IN(0,"处理中"),
    FINISHED(1,"已完成"),
    CANCEL(2,"已取消"),
    CONFIRM(3,"已确认");
    /**
     * 类型
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

}
