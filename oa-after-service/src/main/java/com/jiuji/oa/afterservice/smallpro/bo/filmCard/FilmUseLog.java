package com.jiuji.oa.afterservice.smallpro.bo.filmCard;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 钢化膜使用日志或换新日志
 * <AUTHOR>
 * @date 2019-05-16
 */
@Data
public class FilmUseLog {

    /**
     * basket_id
     */
    @JsonIgnore
    private Integer basketId;

    private Integer subId;

    private Integer basketCount;
    /**
     * 使用店面名称
     */
    private String areaName;

    /**
     * 门店id
     */
    private Integer areaId;
    /**
     * 员工头像
     */
    private String employeeAvatar;
    /**
     * 员工id
     */
    private Integer employeeId;
    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 是否在职
     */
    private Boolean iszaizhi;
    /**
     * 小件单id
     */
    private Integer smallProId;

    /**
     * 串号
     */
    private String imei;

    /**
     * 接件数量
     */
    private Integer smallProBillCount;
    /**
     * 串号大图
     */
    private String imeiBigUrl;
    /**
     * 串号小图
     */
    private String imeiSmallUrl;

    /**
     * 损耗数量
     */
    private Integer lossCount;
    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate useDate;


    /**
     * 使用时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useDateTime;

    /**
     * 使用服务名称
     */
    private String serverName;
}
