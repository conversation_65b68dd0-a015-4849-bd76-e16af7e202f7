package com.jiuji.oa.oacore.oaorder.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 退货单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ReturnSubExportRes", description="退货单")
public class ReturnSubExportRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private Integer id;

    @ApiModelProperty(value = "下单时间")
    private String dtime;

    @ApiModelProperty(value = "收货地址")
    private String shippingAddress;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "状态：待收货，已收货")
    private String received;

    @ApiModelProperty(value = "快递公司")
    private String expressName;

    @ApiModelProperty(value = "快递单号")
    private String expressNum;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格编号")
    private Integer ppriceid;

    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "数量")
    private Integer lcount;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;
}
