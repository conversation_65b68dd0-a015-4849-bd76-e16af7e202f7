package com.jiuji.oa.afterservice.sys.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/7/26 13:58
 */
@Data
public class SysconfigLogVO {
    /**
     * 主键
     */

    private Integer id;
    /**
     * 系统配置项id
     */

    private Integer configId;
    /**
     * 详情系统描述
     */

    private String comment;
    /**
     * 操作用户ID
     */

    private Integer inUserId;

    /**
     * 操作用户名称
     */

    private String inUserName;


    /**
     * 操作类型
     */

    private Integer operationType;
    /**
     * 逻辑删除
     */
    private Boolean del;
    /**
     * 创建日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
