package com.jiuji.oa.oacore.thirdplatform.order.service;

import com.jiuji.oa.oacore.thirdplatform.order.bo.PreparationMealCompleteBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.SelectMeiTuanOrderDetailBO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.CreateMeiTuanSystem;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public interface MeiTuanSDKService {

    /**
     * 获取美团订单详情
     * @param detailBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse getMeiTuanSgOpenResponse(SelectMeiTuanOrderDetailBO detailBO) throws IOException, SgOpenException;


    /**
     * 拣选完成通知美团
     * @param preparationMealCompleteBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse preparationMealComplete(PreparationMealCompleteBO preparationMealCompleteBO) throws IOException, SgOpenException;


    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     */
    SgOpenResponse cancelReason(String orderId)throws IOException, SgOpenException;


    /**
     * 同步门店营业时间到美团
     * @param systemParam
     * @return
     */
    SgOpenResponse synchronizationBusinessHours(CreateMeiTuanSystem createMeiTuanSystem) throws IOException, SgOpenException;



}
