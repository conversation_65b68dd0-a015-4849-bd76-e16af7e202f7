package com.jiuji.oa.oacore.label.service.impl;

import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.label.ProductLabelHisRemarkReq;
import com.jiuji.oa.oacore.label.document.ProductLabelHisLog;
import com.jiuji.oa.oacore.label.repository.ProductLabelHisLogRepository;
import com.jiuji.oa.oacore.label.service.ProductLabelHisLogService;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductLabelHisLogServiceImpl implements ProductLabelHisLogService {
    @Autowired
    private ProductLabelHisLogRepository productLabelHisLogRepository;
    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Autowired
    @Qualifier("ch999oaMongoTemplate")
    private MongoTemplate mongoTemplate;

    /**
     * 保存品生命周期日志
     *
     * @param productLabelHisLog 日志
     */
    @Override
    public void saveProductLabelHisLog(ProductLabelHisLog productLabelHisLog) {
        Long id = productLabelHisLog.getId();
        List<ProductLabelHisLog.Conts> cons = productLabelHisLog.getCons();
        Query query = new Query(Criteria.where("_id").is(id));
        Update update = new Update();
        for (ProductLabelHisLog.Conts item : cons) {
            update.push("conts", item);
        }
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, ProductLabelHisLog.class);
        if (updateResult.getModifiedCount() == 0) {
            ProductLabelHisLog log = new ProductLabelHisLog();
            log.setId(id).setCons(cons);
            productLabelHisLogRepository.save(productLabelHisLog);
        }
    }

    @Override
    public void addRemark(ProductLabelHisRemarkReq productLabelHisRemarkReq) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer xTenant = oaUserBO.getXTenant();
        ProductLabelHisLog log = new ProductLabelHisLog();
        String remark = productLabelHisRemarkReq.getRemark();
        ProductLabelHisLog.Conts conts = new ProductLabelHisLog.Conts();
        conts.setComment("添加备注：" + remark)
                .setDTime(LocalDateTime.now())
                .setInUser(oaUserBO.getUserName())
                .setShowType(true)
                .setXTenant(xTenant);
        log.setId(Long.valueOf(productLabelHisRemarkReq.getProductId())).setCons(Collections.singletonList(conts));
        saveProductLabelHisLog(log);
    }
}