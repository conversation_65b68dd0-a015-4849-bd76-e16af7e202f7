package com.jiuji.oa.oacore.weborder.chainOfResponsibility;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.oaorder.enums.SubDelCollectTypeEnum;
import com.jiuji.oa.oacore.weborder.enums.OrderTypeTag;
import com.jiuji.oa.oacore.weborder.req.PendingPaymentReq;
import com.jiuji.oa.oacore.weborder.req.WebOrderQueryReq;
import com.jiuji.oa.oacore.weborder.service.WebOrderService;
import com.jiuji.oa.oacore.weborder.vo.HuiShouProductVO;
import com.jiuji.oa.oacore.weborder.vo.PendingPaymentVO;
import com.jiuji.oa.oacore.weborder.vo.RecoverSubVO;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 回收单数据处理 第三顺位
 * <AUTHOR>
 */
@Slf4j
@Component(value = "RecyclingOrderHandler")
public class RecyclingOrderHandler extends Handler{

    @Resource
    private WebOrderService webOrderService;



    /**
     * 进行数据的解析
     * @param pageResR
     * @return
     */
    private List<PendingPaymentVO> createPendingPaymentVO(PageRes<RecoverSubVO> pageResR, String tagType,Integer surplusNumber){
        List<PendingPaymentVO> list = new ArrayList<>();
        if(ObjectUtil.isNotNull(pageResR)){
            List<RecoverSubVO> records = pageResR.getRecords();
            if(CollectionUtils.isNotEmpty(records)){
                int number=surplusNumber>records.size()?records.size():surplusNumber;
                for (int i = 0; i < number; i++) {
                    RecoverSubVO recoverSubVO = records.get(i);
                    List<HuiShouProductVO> productList = recoverSubVO.getHuiShouProductList();
                    if (CollectionUtils.isNotEmpty(productList)) {
                        //获取价格最高的商品
                        PendingPaymentVO pendingPaymentVO = new PendingPaymentVO();
                        productList.stream().max(Comparator.comparing(HuiShouProductVO::getPrice)).ifPresent(item -> {
                            pendingPaymentVO.setPpid(item.getPpid());
                        });
                        Integer delivery = Optional.ofNullable(recoverSubVO.getDelivery()).orElse(NumberConstant.ZERO);
                        pendingPaymentVO.setOrderId(recoverSubVO.getRecoverSubId());
                        Integer subCheck = Optional.ofNullable(recoverSubVO.getSubCheck()).orElse(Integer.MAX_VALUE);
                        pendingPaymentVO.setSubCheck(subCheck);
                        pendingPaymentVO.setTagType(tagType);
                        pendingPaymentVO.setDelivery(delivery);
                        pendingPaymentVO.setOrderType(SubDelCollectTypeEnum.SUB_HUISHOU_ORDER.getCode());
                        Optional.ofNullable(recoverSubVO.getAddTime()).ifPresent(item -> {
                            if (delivery.equals(NumberConstant.ONE) && subCheck <= NumberConstant.ONE) {
                                pendingPaymentVO.setCountDownTime(item.minusDays(NumberConstant.THREE));
                            }
                        });
                        list.add(pendingPaymentVO);
                    }
                }
            }
        }
        return list;
    }

    @Override
    protected List<PendingPaymentVO> handle(PendingPaymentReq request,Integer surplusNumber) {
        //构建查询的参数
        WebOrderQueryReq webOrderQueryReq = createWebOrderQueryReq(request);
        webOrderQueryReq.setTagType(OrderTypeTag.Recover_Wait_Test.getCode());
        PageRes<RecoverSubVO> recoverSubVOPageRes;

        /**
         * 待检测数据查询
         */
        log.warn("(售后单)构建查询待检测的订单参数：{}", JSONUtil.toJsonStr(webOrderQueryReq));
        recoverSubVOPageRes = webOrderService.pageRecover(webOrderQueryReq);
        log.warn("(售后单)查询待检测的订单结果：{}", JSONUtil.toJsonStr(recoverSubVOPageRes));
        List<PendingPaymentVO> pendingPaymentVO = createPendingPaymentVO(recoverSubVOPageRes, OrderTypeTag.Recover_Wait_Test.getCode(), surplusNumber);
        surplusNumber=surplusNumber-pendingPaymentVO.size();
        if(surplusNumber.equals(NumberConstant.ZERO)){
            return pendingPaymentVO;
        }

        /**
         * 查询待收款订单
         */
        webOrderQueryReq.setTagType(OrderTypeTag.Recover_Wait_Pay.getCode());
        log.warn("(售后单)构建查询待收款的订单参数：{}", JSONUtil.toJsonStr(webOrderQueryReq));
         recoverSubVOPageRes = webOrderService.pageRecover(webOrderQueryReq);
        log.warn("(售后单)查询待收款的订单结果：{}", JSONUtil.toJsonStr(recoverSubVOPageRes));
        pendingPaymentVO.addAll(createPendingPaymentVO(recoverSubVOPageRes, OrderTypeTag.Recover_Wait_Pay.getCode(),surplusNumber));
        return pendingPaymentVO;
    }
}
