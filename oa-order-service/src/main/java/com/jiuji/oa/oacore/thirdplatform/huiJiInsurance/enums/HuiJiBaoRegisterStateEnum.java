package com.jiuji.oa.oacore.thirdplatform.huiJiInsurance.enums;

import enums.IBaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * @Description 亚丁保险注册状态枚举
 * <AUTHOR>
 * @Date   2022/5/11 17:49
 */
@Getter
@RequiredArgsConstructor
public enum HuiJiBaoRegisterStateEnum implements IBaseEnum<Integer, HuiJiBaoRegisterStateEnum> {


    UNREGISTERED(0, "未注册","未注册"),
    REGISTERED(1, "已注册","审核通过，服务生效中"),
    REGISTING(3, "审核中","提交成功，审核中"),
    /**
     * 被拒了和未注册过的，都算未注册（老蒋）
     */
    UNAPPROVE(2, "审核未通过","审核不通过");


    private final Integer value;
    private final String label;
    private final String ydLabel;


    public static HuiJiBaoRegisterStateEnum ofValue(Integer value) {
        if(value==null){
            return null;
        }
        return Arrays.stream(HuiJiBaoRegisterStateEnum.values())
                .filter(e->e.getValue().equals(value))
                .findFirst().orElse(null);
    }

    @Override
    public HuiJiBaoRegisterStateEnum getEnum(Integer value) {
        return ofValue(value);
    }

    @Override
    public Map<Integer, HuiJiBaoRegisterStateEnum> getAllValueMap() {
        Map<Integer, HuiJiBaoRegisterStateEnum> resMap = new HashMap<>();
        for (HuiJiBaoRegisterStateEnum anEnum : HuiJiBaoRegisterStateEnum.values()) {
            resMap.put(anEnum.value, anEnum);
        }
        return resMap;
    }
}
